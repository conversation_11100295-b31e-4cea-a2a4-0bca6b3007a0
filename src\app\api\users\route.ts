import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { dbQueryService } from '@/lib/services/database-query-service';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import { UserRole } from '@/lib/types';
import { logAPIError, logAuthError, logDatabaseError } from '@/lib/error-handler';

const userSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters long'),
  role: z.nativeEnum(UserRole),
  companyId: z.string().optional(),
});


export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      logAuthError('Users API', new Error('Authentication required'), request.url);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Security: Only Admins and CompanyUsers can list users
    if (user.role !== UserRole.Admin && user.role !== UserRole.CompanyUser) {
      logAuthError('Users API', new Error('Insufficient permissions for user list'), request.url);
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    console.log('🔍 [Users API] Request received:', {
      userId: user.id,
      userRole: user.role,
      url: request.url
    });

    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '20', 10);
    const fetchAll = searchParams.get('fetchAll') === 'true';
    const excludeCompanyUsers = searchParams.get('excludeCompanyUsers') === 'true';
    const search = searchParams.get('search');
    const status = searchParams.get('status');

    console.log('🔍 [Users API] Query parameters:', {
      role,
      page,
      pageSize,
      fetchAll,
      excludeCompanyUsers,
      search,
      status
    });

    // Use optimized database query service
    const queryParams = {
      role: role && Object.values(UserRole).includes(role as UserRole) ? role as UserRole : undefined,
      companyId: user.role === UserRole.CompanyUser ? user.companyId || undefined : undefined,
      isActive: status === 'active' ? true : status === 'inactive' ? false : undefined,
      excludeCompanyUsers,
      search,
      page,
      pageSize,
      fetchAll,
    };

    console.log('🔍 [Users API] Database query params:', queryParams);

    const result = await dbQueryService.getUsersOptimized(user, queryParams);

    console.log('✅ [Users API] Query result:', {
      usersCount: result.users.length,
      total: result.total,
      pages: result.pages,
      currentPage: result.currentPage
    });

    if (result.users.length === 0) {
      console.warn('⚠️ [Users API] No users found with current filters');
      
      // Try a broader query to see if users exist at all
      const broadResult = await dbQueryService.getUsersOptimized(user, {
        isActive: true,
        fetchAll: true
      });
      
      console.log('🔍 [Users API] Broad query result (all active users):', {
        totalUsers: broadResult.users.length,
        users: broadResult.users.map(u => ({ id: u.id, name: u.name, role: u.role }))
      });
    }

    return NextResponse.json({
      success: true,
      users: result.users,
      pagination: {
        page: result.currentPage,
        pageSize: fetchAll ? result.total : pageSize,
        totalPages: result.pages,
        totalUsers: result.total,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('🔥 [Users API] Error getting users:', error);
    logAPIError('Users API', 'GET', error as Error, { path: request.url });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only admins can create users
    if (user.role !== UserRole.Admin) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validation = userSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request body',
          issues: validation.error.flatten().fieldErrors,
        },
        { status: 400 }
      );
    }
    
    const { name, email, password, role, companyId } = validation.data;

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email address already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Generate avatar URL for new users
    const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
      name
    )}&background=random&color=fff&size=128`;

    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        passwordHash,
        role,
        avatarUrl: avatarUrl, // Store external URL temporarily
        companyId,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: newUser,
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
