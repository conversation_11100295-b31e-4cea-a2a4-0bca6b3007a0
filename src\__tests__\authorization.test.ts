import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UserRole } from '@prisma/client';
import { 
  hasPermission, 
  hasPermissionSync, 
  isAdmin, 
  isManager, 
  isCrewChief, 
  hasCrewChiefCompanyAccess,
  isCrewChiefOnShift,
  isAssignedToShift
} from '@/lib/authorization';
import { AuthenticatedUser } from '@/lib/types';

// Mock prisma
vi.mock('@/lib/prisma', () => ({
  prisma: {
    assignedPersonnel: {
      findFirst: vi.fn(),
    },
  },
}));

const { prisma } = await import('@/lib/prisma');

describe('Authorization System', () => {
  let adminUser: AuthenticatedUser;
  let managerUser: AuthenticatedUser;
  let crewChiefUser: AuthenticatedUser;
  let stageHandUser: AuthenticatedUser;
  let companyUser: AuthenticatedUser;

  beforeEach(() => {
    vi.clearAllMocks();

    adminUser = {
      id: 'admin-1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: UserRole.Admin,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;

    managerUser = {
      id: 'manager-1',
      name: 'Manager User',
      email: '<EMAIL>',
      role: UserRole.Manager,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;

    crewChiefUser = {
      id: 'crew-1',
      name: 'Crew Chief',
      email: '<EMAIL>',
      role: UserRole.CrewChief,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;

    stageHandUser = {
      id: 'stage-1',
      name: 'Stage Hand',
      email: '<EMAIL>',
      role: UserRole.StageHand,
      companyId: null,
      company: null,
    } as AuthenticatedUser;

    companyUser = {
      id: 'company-1',
      name: 'Company User',
      email: '<EMAIL>',
      role: UserRole.CompanyUser,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;
  });

  describe('Role Helper Functions', () => {
    it('should correctly identify admin users', () => {
      expect(isAdmin(adminUser)).toBe(true);
      expect(isAdmin(managerUser)).toBe(false);
      expect(isAdmin(crewChiefUser)).toBe(false);
    });

    it('should correctly identify manager users', () => {
      expect(isManager(managerUser)).toBe(true);
      expect(isManager(adminUser)).toBe(false);
      expect(isManager(crewChiefUser)).toBe(false);
    });

    it('should correctly identify crew chief users', () => {
      expect(isCrewChief(crewChiefUser)).toBe(true);
      expect(isCrewChief(adminUser)).toBe(false);
      expect(isCrewChief(stageHandUser)).toBe(false);
    });
  });

  describe('Admin Permissions', () => {
    it('should allow admin full access to all resources', async () => {
      expect(await hasPermission(adminUser, 'USER', 'CREATE')).toBe(true);
      expect(await hasPermission(adminUser, 'USER', 'READ')).toBe(true);
      expect(await hasPermission(adminUser, 'USER', 'UPDATE')).toBe(true);
      expect(await hasPermission(adminUser, 'USER', 'DELETE')).toBe(true);
      
      expect(await hasPermission(adminUser, 'JOB', 'CREATE')).toBe(true);
      expect(await hasPermission(adminUser, 'JOB', 'DELETE')).toBe(true);
      
      expect(await hasPermission(adminUser, 'SHIFT', 'CREATE')).toBe(true);
      expect(await hasPermission(adminUser, 'SHIFT', 'DELETE')).toBe(true);
      
      expect(await hasPermission(adminUser, 'TIMESHEET', 'DELETE')).toBe(true);
      expect(await hasPermission(adminUser, 'TIME_ENTRY', 'DELETE')).toBe(true);
    });
  });

  describe('Manager Permissions', () => {
    it('should allow manager full access but not delete', async () => {
      expect(await hasPermission(managerUser, 'USER', 'CREATE')).toBe(true);
      expect(await hasPermission(managerUser, 'USER', 'READ')).toBe(true);
      expect(await hasPermission(managerUser, 'USER', 'UPDATE')).toBe(true);
      expect(await hasPermission(managerUser, 'USER', 'DELETE')).toBe(false);
      
      expect(await hasPermission(managerUser, 'JOB', 'CREATE')).toBe(true);
      expect(await hasPermission(managerUser, 'JOB', 'DELETE')).toBe(false);
      
      expect(await hasPermission(managerUser, 'SHIFT', 'CREATE')).toBe(true);
      expect(await hasPermission(managerUser, 'SHIFT', 'DELETE')).toBe(false);
      
      expect(await hasPermission(managerUser, 'TIMESHEET', 'DELETE')).toBe(false);
      expect(await hasPermission(managerUser, 'TIME_ENTRY', 'DELETE')).toBe(false);
    });
  });

  describe('Crew Chief Company-Based Access', () => {
    it('should allow crew chief access to jobs for companies where they have been assigned', async () => {
      // Mock that crew chief has been assigned to a shift for this company
      (prisma.assignedPersonnel.findFirst as any).mockResolvedValue({
        id: 'assignment-1',
        userId: 'crew-1',
        shiftId: 'shift-1',
      });

      const job = { companyId: 'company-1' };
      expect(await hasPermission(crewChiefUser, 'JOB', 'READ', { resource: job })).toBe(true);
    });

    it('should deny crew chief access to jobs for companies where they have not been assigned', async () => {
      // Mock that crew chief has NOT been assigned to any shift for this company
      (prisma.assignedPersonnel.findFirst as any).mockResolvedValue(null);

      const job = { companyId: 'company-2' };
      expect(await hasPermission(crewChiefUser, 'JOB', 'READ', { resource: job })).toBe(false);
    });
  });

  describe('Crew Chief Clock Management', () => {
    it('should allow crew chief to manage time entries only on shifts where they are crew chief', async () => {
      // Mock that crew chief is assigned as CC on this shift
      (prisma.assignedPersonnel.findFirst as any).mockResolvedValue({
        id: 'assignment-1',
        userId: 'crew-1',
        shiftId: 'shift-1',
        roleCode: 'CC',
      });

      const timeEntry = { 
        assignedPersonnel: { 
          shiftId: 'shift-1',
          userId: 'worker-1' 
        } 
      };
      expect(await hasPermission(crewChiefUser, 'TIME_ENTRY', 'UPDATE', { resource: timeEntry })).toBe(true);
    });

    it('should allow crew chief to view their own time entries regardless of role', async () => {
      const timeEntry = { 
        assignedPersonnel: { 
          shiftId: 'shift-1',
          userId: 'crew-1' // Their own entry
        } 
      };
      expect(await hasPermission(crewChiefUser, 'TIME_ENTRY', 'READ', { resource: timeEntry })).toBe(true);
    });
  });

  describe('Stage Hand Access Control', () => {
    it('should allow stage hand to view shifts only where they are assigned', async () => {
      // Mock that stage hand is assigned to this shift
      (prisma.assignedPersonnel.findFirst as any).mockResolvedValue({
        id: 'assignment-1',
        userId: 'stage-1',
        shiftId: 'shift-1',
      });

      const shift = { id: 'shift-1', job: { companyId: 'company-1' } };
      expect(await hasPermission(stageHandUser, 'SHIFT', 'READ', { resource: shift })).toBe(true);
    });

    it('should deny stage hand access to shifts where they are not assigned', async () => {
      // Mock that stage hand is NOT assigned to this shift
      (prisma.assignedPersonnel.findFirst as any).mockResolvedValue(null);

      const shift = { id: 'shift-2', job: { companyId: 'company-1' } };
      expect(await hasPermission(stageHandUser, 'SHIFT', 'READ', { resource: shift })).toBe(false);
    });

    it('should allow stage hand to view only their own time entries', async () => {
      const ownTimeEntry = { 
        assignedPersonnel: { 
          userId: 'stage-1' // Their own entry
        } 
      };
      expect(await hasPermission(stageHandUser, 'TIME_ENTRY', 'READ', { resource: ownTimeEntry })).toBe(true);

      const otherTimeEntry = { 
        assignedPersonnel: { 
          userId: 'other-user' // Someone else's entry
        } 
      };
      expect(await hasPermission(stageHandUser, 'TIME_ENTRY', 'READ', { resource: otherTimeEntry })).toBe(false);
    });
  });

  describe('Company User Access', () => {
    it('should allow company user access to their company resources', async () => {
      const job = { companyId: 'company-1' };
      expect(await hasPermission(companyUser, 'JOB', 'READ', { resource: job })).toBe(true);

      const shift = { job: { companyId: 'company-1' } };
      expect(await hasPermission(companyUser, 'SHIFT', 'READ', { resource: shift })).toBe(true);
    });

    it('should deny company user access to other company resources', async () => {
      const job = { companyId: 'company-2' };
      expect(await hasPermission(companyUser, 'JOB', 'READ', { resource: job })).toBe(false);

      const shift = { job: { companyId: 'company-2' } };
      expect(await hasPermission(companyUser, 'SHIFT', 'READ', { resource: shift })).toBe(false);
    });
  });

  describe('Synchronous Permission Checks', () => {
    it('should handle synchronous permissions correctly', () => {
      expect(hasPermissionSync(adminUser, 'USER', 'CREATE')).toBe(true);
      expect(hasPermissionSync(managerUser, 'USER', 'DELETE')).toBe(false);
    });

    it('should throw error for async permissions when called synchronously', () => {
      const job = { companyId: 'company-1' };
      expect(() => {
        hasPermissionSync(crewChiefUser, 'JOB', 'READ', { resource: job });
      }).toThrow('Permission check for JOB.READ is async but called synchronously');
    });
  });
});
