"use client"

import React, { useState, useEffect } from "react"
import { CustomButton } from '@/components/ui/custom-button'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from '@/components/ui/badge'

import { Users, Plus, Minus } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { RoleCode } from "@/lib/types";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

interface WorkerRequirement {
  roleCode: RoleCode;
  requiredCount: number;
}

interface WorkerRequirementsProps {
  shiftId: string;
  workerRequirements?: WorkerRequirement[];
  onUpdate?: (updatedRequirements: WorkerRequirement[]) => void;
}

const ROLE_DEFINITIONS: Record<RoleCode, { name: string; roleColor: "purple" | "blue" | "green" | "yellow" | "red" | "gray"; cardBgColor: string; textColor: string; borderColor: string; }> = {
  'CC': { name: 'Crew Chief', roleColor: 'purple', cardBgColor: 'bg-purple-50 dark:bg-purple-900/30', textColor: 'text-purple-900 dark:text-purple-100', borderColor: 'border-purple-200 dark:border-purple-700' },
  'SH': { name: 'Stage Hand', roleColor: 'blue', cardBgColor: 'bg-blue-50 dark:bg-blue-900/30', textColor: 'text-blue-900 dark:text-blue-100', borderColor: 'border-blue-200 dark:border-blue-700' },
  'FO': { name: 'Fork Operator', roleColor: 'green', cardBgColor: 'bg-green-50 dark:bg-green-900/30', textColor: 'text-green-900 dark:text-green-100', borderColor: 'border-green-200 dark:border-green-700' },
  'RFO': { name: 'Reach Fork Operator', roleColor: 'yellow', cardBgColor: 'bg-yellow-50 dark:bg-yellow-900/30', textColor: 'text-yellow-900 dark:text-yellow-100', borderColor: 'border-yellow-200 dark:border-yellow-700' },
  'RG': { name: 'Rigger', roleColor: 'red', cardBgColor: 'bg-red-50 dark:bg-red-900/30', textColor: 'text-red-900 dark:text-red-100', borderColor: 'border-red-200 dark:border-red-700' },
  'GL': { name: 'General Labor', roleColor: 'gray', cardBgColor: 'bg-gray-100 dark:bg-gray-800/30', textColor: 'text-gray-900 dark:text-gray-100', borderColor: 'border-gray-200 dark:border-gray-700' },
  'SUP': { name: 'Manager', roleColor: 'red', cardBgColor: 'bg-red-50 dark:bg-red-900/30', textColor: 'text-red-900 dark:text-red-100', borderColor: 'border-red-200 dark:border-red-700' },
} as const

export default function WorkerRequirements({ shiftId, workerRequirements: propWorkerRequirements, onUpdate }: WorkerRequirementsProps) {
  const { toast } = useToast()
  const queryClient = useQueryClient();

  const { data: workerRequirements = [], isLoading } = useQuery<WorkerRequirement[]>({
    queryKey: ['workerRequirements', shiftId],
    queryFn: async () => {
      const response = await fetch(`/api/shifts/${shiftId}/worker-requirements`);
      if (response.ok) {
        const result = await response.json();
        return result.data.workerRequirements;
      }
      // If no data exists, initialize with default values
      return [
        { roleCode: 'CC', requiredCount: 1 },
        { roleCode: 'SH', requiredCount: 0 },
        { roleCode: 'FO', requiredCount: 0 },
        { roleCode: 'RFO', requiredCount: 0 },
        { roleCode: 'RG', requiredCount: 0 },

      ];
    },
    initialData: propWorkerRequirements,
    enabled: !!shiftId,
  });

  const mutation = useMutation({
    mutationFn: (newRequirements: WorkerRequirement[]) => {
      return fetch(`/api/shifts/${shiftId}/worker-requirements`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workerRequirements: newRequirements })
      }).then(res => {
        if (!res.ok) {
          return res.json().then(errorData => {
            throw new Error(errorData.error || 'Failed to update worker requirements');
          });
        }
        return res.json();
      });
    },
    onMutate: async (newRequirements: WorkerRequirement[]) => {
      await queryClient.cancelQueries({ queryKey: ['workerRequirements', shiftId] });
      const previousRequirements = queryClient.getQueryData(['workerRequirements', shiftId]);
      queryClient.setQueryData(['workerRequirements', shiftId], newRequirements);
      return { previousRequirements };
    },
    onError: (err, newRequirements, context) => {
      if (context?.previousRequirements) {
        queryClient.setQueryData(['workerRequirements', shiftId], context.previousRequirements);
      }
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update worker requirements",
        variant: "destructive",
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['workerRequirements', shiftId] });
    },
    onSuccess: (data, variables) => {
      if (onUpdate) {
        onUpdate(variables);
      }
      toast({
        title: "Requirements Updated",
        description: `Worker requirements have been updated.`,
      });
    }
  });

  const updateWorkerRequirement = (roleCode: RoleCode, newCount: number) => {
    if (mutation.isPending || newCount < 0) return;

    const allRoleTypes: RoleCode[] = ['CC', 'SH', 'FO', 'RFO', 'RG', 'GL', 'SUP'];
    const newRequirements: WorkerRequirement[] = allRoleTypes.map(role => {
      if (role === roleCode) {
        return { roleCode: role, requiredCount: newCount };
      }
      const existing = workerRequirements.find(req => req.roleCode === role);
      return existing || { roleCode: role, requiredCount: 0 };
    });

    mutation.mutate(newRequirements);
  };

  const getRequiredCount = (roleCode: RoleCode): number => {
    // Handle case where workerRequirements is undefined or null
    if (!workerRequirements || !Array.isArray(workerRequirements)) {
      return 0
    }
    return workerRequirements.find(req => req.roleCode === roleCode)?.requiredCount || 0
  }

  // Show loading state if data is still loading
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Worker Requirements
          </CardTitle>
          <CardDescription>
            Loading worker requirements...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
            <span className="ml-3 text-gray-600">Loading...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Worker Requirements
        </CardTitle>
        <CardDescription>
          Configure how many workers of each type are needed for this shift
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          {(Object.entries(ROLE_DEFINITIONS) as [RoleCode, typeof ROLE_DEFINITIONS[RoleCode]][]).map(([roleCode, roleDef]) => {
            const currentCount = getRequiredCount(roleCode)
            const isCrewChief = roleCode === 'CC'

            return (
              <div key={roleCode} className={`p-4 rounded-lg border ${roleDef.cardBgColor} ${roleDef.borderColor} flex flex-col sm:flex-row sm:items-center sm:justify-between`}>
                <div className="flex items-center justify-between sm:justify-start mb-3 sm:mb-0">
                  <div className="flex-grow">
                    <span className={`font-bold text-lg ${roleDef.textColor}`}>{roleDef.name}</span>
                    <Badge variant="secondary" className="ml-2">{roleCode}</Badge>
                    {isCrewChief && (
                      <Badge variant="outline" className="ml-2 border-purple-500 text-purple-700">
                        Required
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-center w-full">
                  <div className="flex items-center gap-2 flex-grow">
                    <CustomButton
                      size="sm"
                      variant="role"
                      roleColor={roleDef.roleColor}
                      onClick={() => updateWorkerRequirement(roleCode, currentCount - 5)}
                      disabled={currentCount < 5 || mutation.isPending}
                      className="flex-1"
                    >
                      -5
                    </CustomButton>
                    <CustomButton
                      size="sm"
                      variant="role"
                      roleColor={roleDef.roleColor}
                      onClick={() => updateWorkerRequirement(roleCode, currentCount - 1)}
                      disabled={currentCount === 0 || mutation.isPending}
                      className="flex-1"
                    >
                      <Minus className="h-4 w-4" />
                    </CustomButton>
                    <span className={`w-16 text-center font-bold text-2xl ${roleDef.textColor}`}>{currentCount}</span>
                    <CustomButton
                      size="sm"
                      variant="role"
                      roleColor={roleDef.roleColor}
                      onClick={() => updateWorkerRequirement(roleCode, currentCount + 1)}
                      disabled={mutation.isPending}
                      className="flex-1"
                    >
                      <Plus className="h-4 w-4" />
                    </CustomButton>
                    <CustomButton
                      size="sm"
                      variant="role"
                      roleColor={roleDef.roleColor}
                      onClick={() => updateWorkerRequirement(roleCode, currentCount + 5)}
                      disabled={mutation.isPending}
                      className="flex-1"
                    >
                      +5
                    </CustomButton>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
