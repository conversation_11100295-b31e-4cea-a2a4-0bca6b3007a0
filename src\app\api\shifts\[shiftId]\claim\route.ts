import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { WorkerStatus } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: { shiftId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { shiftId } = params;
    const { assignmentId } = await request.json();

    if (!assignmentId) {
      return NextResponse.json({ error: 'Assignment ID is required' }, { status: 400 });
    }

    // Prevent double-assign: ensure user isn't already on this shift
    const existingAssignment = await prisma.assignedPersonnel.findFirst({
      where: { shiftId, userId: user.id },
      select: { id: true },
    });

    if (existingAssignment) {
      return NextResponse.json({ error: 'You are already assigned to this shift' }, { status: 400 });
    }

    // Concurrency-safe claim: update only if still UpForGrabs and unassigned
    const claimed = await prisma.assignedPersonnel.updateMany({
      where: {
        id: assignmentId,
        shiftId,
        status: WorkerStatus.UpForGrabs,
        userId: null,
      },
      data: {
        userId: user.id,
        status: WorkerStatus.Assigned,
        claimedById: user.id,
        claimedAt: new Date(),
      },
    });

    if (claimed.count === 0) {
      return NextResponse.json({ error: 'This shift has already been claimed' }, { status: 409 });
    }

    // Optionally, mark other notifications as read for this shift for this user
    await prisma.notification.updateMany({
      where: { userId: user.id, relatedShiftId: shiftId, type: 'SHIFT_UP_FOR_GRABS', isRead: false },
      data: { isRead: true },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error claiming shift:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
