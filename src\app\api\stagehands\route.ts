import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { userValidation } from '@/lib/validation';
import { UserRole } from '@/lib/types';
import { logAPIError, logAuthError } from '@/lib/error-handler';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      logAuthError('Employees API', new Error('Authentication required'), request.url);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('🔍 [Employees API] Request received:', {
      userId: user.id,
      userRole: user.role,
      url: request.url
    });

    // Only managers can view all employees
    if (user.role !== UserRole.Admin) {
      console.warn('⚠️ [Employees API] Insufficient permissions:', {
        userRole: user.role,
        requiredRole: UserRole.Admin
      });
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('pageSize') || '10', 10); // Use pageSize for consistency
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const status = searchParams.get('status');

    console.log('🔍 [Employees API] Query parameters:', {
      page,
      limit,
      search,
      role,
      status
    });

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (status) {
      where.isActive = status === 'active';
    } else {
      // Default to active users only if no status filter is provided
      where.isActive = true;
    }

    console.log('🔍 [Employees API] Database query where clause:', where);

    const [users, total] = await prisma.$transaction([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          crew_chief_eligible: true,
          fork_operator_eligible: true,
          OSHA_10_Certifications: true,
          location: true,
          certifications: true,
          performance: true,
          avatarUrl: true,
        },
        orderBy: { name: 'asc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    console.log('✅ [Employees API] Query result:', {
      usersCount: users.length,
      total,
      totalPages: Math.ceil(total / limit)
    });

    if (users.length === 0) {
      console.warn('⚠️ [Employees API] No employees found with current filters');
      
      // Try a broader query to see if employees exist at all
      const allActiveUsers = await prisma.user.count({ where: { isActive: true } });
      console.log('🔍 [Employees API] Total active users in database:', allActiveUsers);
    }

    // Transform users to include avatarUrl for backward compatibility
    const transformedUsers = users.map(user => ({
      ...user,
      avatarUrl: user.avatarUrl ? `/api/users/${user.id}/avatar/image` : null,
      // Add properties that might be expected by the frontend
      crewChiefEligible: user.crew_chief_eligible,
      forkOperatorEligible: user.fork_operator_eligible,
    }));

    console.log('🔍 [Employees API] Sample transformed user:', transformedUsers[0]);

    return NextResponse.json({
      users: transformedUsers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('🔥 [Employees API] Error getting employees:', error);
    logAPIError('Employees API', 'GET', error as Error, { path: request.url });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only managers can create employees
    if (user.role !== UserRole.Admin) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validation = userValidation.create.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request body',
          issues: validation.error.flatten().fieldErrors,
        },
        { status: 400 }
      );
    }

    const { name, email, role, password } = validation.data;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Store password directly (no hashing)

    // Create user
    const result = await prisma.user.create({
      data: {
        name,
        email,
        role,
        passwordHash: password,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    const newUser = result;

    return NextResponse.json({
      success: true,
      user: {
        id: newUser.id,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
      },
    });
  } catch (error) {
    console.error('Error creating employee:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
