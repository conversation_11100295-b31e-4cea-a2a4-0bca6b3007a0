import { NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';
import { isAdmin } from '@/lib/auth-server';

export async function POST(req: Request) {
  try {
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Revalidate all data caches for the entire site
    revalidatePath('/', 'layout');

    return NextResponse.json({ success: true, message: 'Server cache cleared' });
  } catch (error) {
    console.error('Error clearing server cache:', error);
    return NextResponse.json({ success: false, message: 'Failed to clear server cache' }, { status: 500 });
  }
}
