'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, Download, Eye } from 'lucide-react';
import PDFConfigSettings from '@/components/pdf-config-settings';
import { PDFElement } from '@/types/pdf';
import PDFViewer from '@/components/pdf-viewer';
import { useToast } from '@/hooks/use-toast';
import PdfElementComponent from '@/components/pdf-element';

interface PDFConfiguration {
  id: string;
  name: string;
  elements: PDFElement[];
  pageSize: 'letter' | 'a4';
  pageOrientation: 'portrait' | 'landscape';
  createdAt: string;
  updatedAt: string;
}

export default function PDFConfigPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [configuration, setConfiguration] = useState<PDFConfiguration>({
    id: 'new-config',
    name: 'Default Timesheet',
    elements: [],
    pageSize: 'letter',
    pageOrientation: 'portrait',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });
  const [selectedElement, setSelectedElement] = useState<PDFElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [templateUrl, setTemplateUrl] = useState<string | null>(null);
  const pdfContainerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(1);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showTemplate, setShowTemplate] = useState(true);
  const [draggedElement, setDraggedElement] = useState<PDFElement | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const pointsToPixels = (points: number) => points * (96 / 72) * zoom;
  const pixelsToPoints = (pixels: number) => pixels / (96 / 72) / zoom;

  const pageWidth = configuration.pageSize === 'letter' ? 612 : 595;
  const pageHeight = configuration.pageSize === 'letter' ? 792 : 842;
  const canvasWidth = pointsToPixels(pageWidth);
  const canvasHeight = pointsToPixels(pageHeight);

  useEffect(() => {
    return () => {
      if (templateUrl) {
        URL.revokeObjectURL(templateUrl);
      }
    };
  }, [templateUrl]);

  const handleMouseDown = (e: React.MouseEvent, element: PDFElement) => {
    if (isPreviewMode) return;
    e.preventDefault();
    e.stopPropagation();
    const rect = e.currentTarget.getBoundingClientRect();
    setDraggedElement(element);
    setIsDragging(true);
    setDragOffset({ x: e.clientX - rect.left, y: e.clientY - rect.top });
    setSelectedElement(element);
  };

  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!isDragging || !draggedElement || !pdfContainerRef.current) return;
      const containerRect = pdfContainerRef.current.getBoundingClientRect();
      const newX = e.clientX - containerRect.left - dragOffset.x;
      const newY = e.clientY - containerRect.top - dragOffset.y;
      const maxX = canvasWidth - pointsToPixels(draggedElement.width);
      const maxY = canvasHeight - pointsToPixels(draggedElement.height);
      const constrainedX = Math.max(0, Math.min(newX, maxX));
      const constrainedY = Math.max(0, Math.min(newY, maxY));

      const updatedElements = configuration.elements.map(el =>
        el.id === draggedElement.id
          ? { ...el, x: pixelsToPoints(constrainedX), y: pixelsToPoints(constrainedY) }
          : el
      );
      setConfiguration(prev => ({ ...prev, elements: updatedElements, updatedAt: new Date().toISOString() }));
    };

    const handleGlobalMouseUp = () => {
      setIsDragging(false);
      setDraggedElement(null);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging, draggedElement, dragOffset, canvasWidth, canvasHeight, pointsToPixels, pixelsToPoints, configuration.elements]);

  const handleElementUpdate = (updatedElement: PDFElement) => {
    const updatedElements = configuration.elements.map(el => (el.id === updatedElement.id ? updatedElement : el));
    setConfiguration(prev => ({ ...prev, elements: updatedElements, updatedAt: new Date().toISOString() }));
    setSelectedElement(updatedElement);
  };

  const handleAddElement = (elementType: PDFElement['type']) => {
    const newElement: PDFElement = {
      id: `element-${Date.now()}`,
      type: elementType,
      label: `New ${elementType}`,
      x: 100, y: 100, width: 100, height: 30,
      fontSize: 12, fontWeight: 'normal', required: false,
      dataKey: `custom${elementType.charAt(0).toUpperCase() + elementType.slice(1)}${Date.now()}`
    };
    setConfiguration(prev => ({ ...prev, elements: [...prev.elements, newElement], updatedAt: new Date().toISOString() }));
    setSelectedElement(newElement);
  };

  const handleDeleteElement = (elementId: string) => {
    if (selectedElement?.id === elementId) setSelectedElement(null);
    const updatedElements = configuration.elements.filter(el => el.id !== elementId);
    setConfiguration(prev => ({ ...prev, elements: updatedElements, updatedAt: new Date().toISOString() }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/admin/pdf-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(configuration),
      });
      if (!res.ok) throw new Error('Failed to save configuration');
      toast({ title: "Success", description: "PDF configuration saved successfully" });
    } catch (error) {
      toast({ title: "Error", description: error instanceof Error ? error.message : 'Failed to save configuration', variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadSample = async () => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/admin/pdf-config/sample', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(configuration),
      });
      if (!res.ok) throw new Error('Failed to generate sample PDF');
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `timesheet-sample-${Date.now()}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast({ title: "Success", description: "Sample PDF downloaded successfully" });
    } catch (error) {
      toast({ title: "Error", description: error instanceof Error ? error.message : 'Failed to generate sample PDF', variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTemplateUpload = async (e: React.ChangeEvent<HTMLInputElement>, templateType: 'overlay' | 'form') => {
    const file = e.target.files?.[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('templateType', templateType);

    try {
      const res = await fetch('/api/admin/pdf-config/template/upload', { method: 'POST', body: formData });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || 'Upload failed');

      const fileUrl = URL.createObjectURL(file);
      setTemplateUrl(prev => { if (prev) URL.revokeObjectURL(prev); return fileUrl; });
      setShowTemplate(true);

      if (templateType === 'form' && data.formFields) {
        const newElements: PDFElement[] = data.formFields.map((field: any) => ({
          id: field.name,
          type: 'text',
          label: field.name,
          x: field.x,
          y: pageHeight - field.y - field.height, // Invert Y-axis
          width: field.width,
          height: field.height,
          fontSize: 12,
          fontWeight: 'normal',
          required: false,
          dataKey: field.name,
        }));
        setConfiguration(prev => ({ ...prev, elements: newElements, updatedAt: new Date().toISOString() }));
        toast({ title: "Success", description: "Form template uploaded and fields extracted" });
      } else {
        toast({ title: "Success", description: "Overlay template uploaded successfully" });
      }
    } catch (err) {
      console.warn(`Upload ${templateType} template failed`, err);
      toast({ title: "Error", description: err instanceof Error ? err.message : `Failed to upload ${templateType} template`, variant: "destructive" });
    } finally {
      e.currentTarget.value = '';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}><ArrowLeft className="h-4 w-4 mr-2" />Back</Button>
            <div>
              <h1 className="text-3xl font-bold">PDF Configuration Tool</h1>
              <p className="text-muted-foreground">Configure timesheet PDF layouts.</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsPreviewMode(!isPreviewMode)}><Eye className="h-4 w-4 mr-2" />{isPreviewMode ? 'Edit' : 'Preview'}</Button>
            <Button variant="outline" onClick={() => setShowTemplate(v => !v)}>{showTemplate ? 'Hide' : 'Show'} Template</Button>
            <label className="inline-flex items-center gap-2 text-xs cursor-pointer p-2 border rounded-md hover:bg-gray-100"><input type="file" className="hidden" accept="application/pdf" onChange={(e) => handleTemplateUpload(e, 'overlay')} /><span>Upload Overlay</span></label>
            <label className="inline-flex items-center gap-2 text-xs cursor-pointer p-2 border rounded-md hover:bg-gray-100"><input type="file" className="hidden" accept="application/pdf" onChange={(e) => handleTemplateUpload(e, 'form')} /><span>Upload Form</span></label>
            <Button variant="outline" onClick={handleDownloadSample} disabled={isLoading}><Download className="h-4 w-4 mr-2" />Sample</Button>
            <Button onClick={handleSave} disabled={isLoading}><Save className="h-4 w-4 mr-2" />Save</Button>
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <Card>
              <CardHeader><CardTitle className="flex items-center justify-between"><span>PDF Canvas</span><Badge variant="secondary">{configuration.pageSize.toUpperCase()} • {configuration.pageOrientation}</Badge></CardTitle></CardHeader>
              <CardContent>
                <div ref={pdfContainerRef} className={`relative border-2 border-dashed bg-white mx-auto ${isDragging ? 'border-primary bg-primary/5' : 'border-gray-300'}`} style={{ width: canvasWidth, height: canvasHeight }}>
                  {templateUrl && showTemplate && (
                    <PDFViewer
                      pdfUrl={templateUrl}
                      pdfName={configuration.name}
                      elements={configuration.elements}
                    />
                  )}
                  {!isPreviewMode && configuration.elements.map(el => (
                    <PdfElementComponent
                      key={el.id}
                      element={el}
                      onMouseDown={handleMouseDown}
                      isSelected={selectedElement?.id === el.id}
                      zoom={zoom}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="lg:col-span-1">
            <PDFConfigSettings selectedElement={selectedElement} onElementUpdate={handleElementUpdate} onElementDelete={handleDeleteElement} onAddElement={handleAddElement} elements={configuration.elements} />
          </div>
        </div>
      </div>
    </div>
  );
}
