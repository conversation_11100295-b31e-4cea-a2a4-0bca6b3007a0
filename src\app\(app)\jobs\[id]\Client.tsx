"use client";

import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation";
import { format } from "date-fns";
import Link from "next/link";
import { useJobs, useShifts } from "@/hooks/use-api";
import { useTimelineColors } from "@/hooks/use-timeline-colors";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Briefcase, Calendar, Users, Clock, MapPin, Plus, AlertCircle, RefreshCw, TrendingUp, Activity, UserCheck, UserX, BarChart3, FileText, User } from "lucide-react";
import { CrewChiefPermissionManager } from "@/components/crew-chief-permission-manager";
import { DangerZone } from "@/components/danger-zone";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { getAssignedWorkerCount, getTotalRequiredWorkers } from "@/lib/worker-count-utils";
import { AuthenticatedUser } from "@/lib/types";

export default function JobDetailClient({ user }: { user: AuthenticatedUser }) {
  const params = useParams();
  const jobId = (params as any).id as string;
  const router = useRouter();
  const canEdit = user?.role === "Admin";
  const { getCrewChiefColor } = useTimelineColors();

  const { data: jobs, isLoading: jobsLoading, isError: jobsError, refetch: refetchJobs } = useJobs({});
  const { data: shifts, isLoading: shiftsLoading, isError: shiftsError, refetch: refetchShifts } = useShifts({ jobId });

  const jobsArray = jobs?.jobs || [];
  const job = jobsArray?.find((j) => j.id === jobId);

  const isLoading = jobsLoading || shiftsLoading;
  const hasError = jobsError || shiftsError;

  const allShifts = shifts || [];
  const upcomingShifts = shifts?.filter((shift: any) => new Date(shift.date) >= new Date()).slice(0, 10) || [];
  const completedShifts = shifts?.filter((shift: any) => shift.status === "Completed") || [];

  const totalShifts = shifts?.length || 0;
  const completedShiftsCount = completedShifts.length;
  const completionRate = totalShifts > 0 ? (completedShiftsCount / totalShifts) * 100 : 0;

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (hasError || !job) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <Alert variant="destructive" className="max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {!job ? "Job not found" : "Error loading job data"}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  refetchJobs();
                  refetchShifts();
                }}
                className="mt-2 w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  const getShiftDisplayName = (shift: any, fallbackJobName?: string) => {
    if (shift.description && shift.description.trim()) {
      return shift.description.trim();
    }
    return shift.job?.name || fallbackJobName || "Unnamed Shift";
  };

  const getShiftStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
        return "bg-green-500";
      case "in_progress":
        return "bg-blue-500";
      case "scheduled":
        return "bg-yellow-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getShiftStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
        return "default";
      case "in_progress":
        return "secondary";
      case "scheduled":
        return "outline";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getAssignmentStatusColor = (assignedCount: number, requiredCount: number) => {
    const percentage = requiredCount > 0 ? (assignedCount / requiredCount) * 100 : 0;
    if (percentage >= 100) return "text-green-600";
    if (percentage >= 75) return "text-yellow-600";
    if (percentage >= 50) return "text-orange-600";
    return "text-red-600";
  };

  const getAssignmentStatusIcon = (assignedCount: number, requiredCount: number) => {
    const percentage = requiredCount > 0 ? (assignedCount / requiredCount) * 100 : 0;
    if (percentage >= 100) return <UserCheck className="h-4 w-4 text-green-600" />;
    if (percentage >= 50) return <Users className="h-4 w-4 text-yellow-600" />;
    return <UserX className="h-4 w-4 text-red-600" />;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/jobs")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Jobs
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/jobs/${jobId}/report`)}
            className="flex items-center gap-2 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900 text-green-700 dark:text-green-300"
          >
            <FileText className="h-4 w-4" />
            Job Report
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push(`/jobs/${jobId}/shifts`)}
            className="flex items-center gap-2 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900 text-green-700 dark:text-green-300"
          >
            <Calendar className="h-4 w-4" />
            View All Shifts
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              router.push(`/jobs/${jobId}/scheduling-timeline`);
            }}
            className="flex items-center gap-2 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900 text-blue-700 dark:text-blue-300"
            style={{ minWidth: "180px" }}
          >
            <BarChart3 className="h-4 w-4" />
            Timeline Manager
          </Button>
          {canEdit && (
            <Button onClick={() => router.push(`/jobs/${jobId}/edit`)} className="flex items-center gap-2">
              Edit Job
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Job Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-4">
                <Briefcase className="h-16 w-16 text-muted-foreground" />
                <div className="flex-1">
                  <CardTitle className="text-2xl">{job.name}</CardTitle>
                  <CardDescription className="text-base">
                    {job.company?.name ? `for ${job.company.name}` : "No company assigned"}
                  </CardDescription>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={job.status === "Active" ? "default" : "secondary"}>{job.status}</Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      {completionRate.toFixed(0)}% shift completion
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            {job.description && (
              <CardContent>
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-muted-foreground">{job.description}</p>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Job Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Job Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">{job.location || "No location specified"}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Start Date</p>
                    <p className="text-sm text-muted-foreground">
                      {job.startDate ? format(new Date(job.startDate), "PPP") : "Not specified"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">End Date</p>
                    <p className="text-sm text-muted-foreground">
                      {job.endDate ? format(new Date(job.endDate), "PPP") : "Not specified"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Total Shifts</p>
                    <p className="text-sm text-muted-foreground">{totalShifts} shifts</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shifts Overview */}
          <Tabs defaultValue="upcoming" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="upcoming">Upcoming ({upcomingShifts.length})</TabsTrigger>
              <TabsTrigger value="completed">Completed ({completedShiftsCount})</TabsTrigger>
              <TabsTrigger value="all">All Shifts ({totalShifts})</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            {/* Upcoming Shifts Tab Content */}
            <TabsContent value="upcoming" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Upcoming Shifts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {upcomingShifts.length > 0 ? (
                    <div className="space-y-4">
                      {upcomingShifts.map((shift: any) => {
                        const assignedCount = getAssignedWorkerCount(shift);
                        const requiredCount = getTotalRequiredWorkers(shift);
                        return (
                          <div key={shift.id} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="space-y-1">
                              <div className="font-medium">{getShiftDisplayName(shift, job.name)}</div>
                              <div className="text-sm text-muted-foreground">
                                {format(new Date(shift.date), "PPP")} • {shift.startTime} - {shift.endTime}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={getShiftStatusBadgeVariant(shift.status)}>{shift.status}</Badge>
                              <span className={getAssignmentStatusColor(assignedCount, requiredCount)}>
                                {assignedCount}/{requiredCount}
                              </span>
                              <Button variant="ghost" size="sm" onClick={() => router.push(`/shifts/[shiftId]`)}>
                                View
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">No upcoming shifts.</div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Completed Shifts Tab Content */}
            <TabsContent value="completed" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Completed Shifts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {completedShifts.length > 0 ? (
                    <div className="space-y-4">
                      {completedShifts.map((shift: any) => (
                        <div key={shift.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="space-y-1">
                            <div className="font-medium">{getShiftDisplayName(shift, job.name)}</div>
                            <div className="text-sm text-muted-foreground">
                              {format(new Date(shift.date), "PPP")} • {shift.startTime} - {shift.endTime}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={getShiftStatusBadgeVariant(shift.status)}>{shift.status}</Badge>
                            <Button variant="ghost" size="sm" onClick={() => router.push(`/shifts/[shiftId]`)}>
                              View
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">No completed shifts.</div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* All Shifts Tab */}
            <TabsContent value="all" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    All Shifts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {allShifts.length > 0 ? (
                    <div className="space-y-4">
                      {allShifts.map((shift: any) => {
                        const assignedCount = getAssignedWorkerCount(shift);
                        const requiredCount = getTotalRequiredWorkers(shift);
                        return (
                          <div key={shift.id} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="space-y-1">
                              <div className="font-medium">{getShiftDisplayName(shift, job.name)}</div>
                              <div className="text-sm text-muted-foreground">
                                {format(new Date(shift.date), "PPP")} • {shift.startTime} - {shift.endTime}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={getShiftStatusBadgeVariant(shift.status)}>{shift.status}</Badge>
                              <span className={getAssignmentStatusColor(assignedCount, requiredCount)}>
                                {assignedCount}/{requiredCount}
                              </span>
                              <Button variant="ghost" size="sm" onClick={() => router.push(`/shifts/[shiftId]`)}>
                                View
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">No shifts found.</div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Permissions & Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <CrewChiefPermissionManager targetId={jobId} targetType="job" targetName={job.name} />
                    {canEdit && (
                      <DangerZone entityType="job" entityId={jobId} entityName={job.name} onSuccess={() => router.push("/jobs")} />
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" onClick={() => router.push(`/jobs/${jobId}/shifts/new`)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Shift
              </Button>
              <Button variant="outline" className="w-full" onClick={() => router.push(`/jobs/${jobId}/scheduling-timeline`)}>
                <BarChart3 className="h-4 w-4 mr-2" />
                Timeline Manager
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Assignment Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Overall completion</span>
                  <span>{completionRate.toFixed(0)}%</span>
                </div>
                <Progress value={completionRate} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
