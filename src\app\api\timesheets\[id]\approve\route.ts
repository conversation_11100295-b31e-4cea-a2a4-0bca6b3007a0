import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { TimesheetStatus } from '@prisma/client';
import { PdfFromExcelGenerator } from '@/lib/pdf-from-excel-generator';
import { generateTimesheetExcel } from '@/lib/excel-generator';
import { uploadToGCS } from '@/lib/gcs';
import { promises as fs } from 'fs';
import path from 'path';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth-config';
import { AuthenticatedUser } from '@/lib/types';
import { isAdmin, isManager, isCompanyUser } from '@/lib/permissions';

// Define schema for input validation
const clientApprovalBodySchema = z.object({
  signature: z.string().min(1, { message: 'Signature is required' }),
  approvalType: z.union([z.literal('client'), z.literal('company')]),
  notes: z.string().optional(),
});

const managerApprovalBodySchema = z.object({
  approvalType: z.literal('manager'),
  notes: z.string().optional(),
});

function sanitizeForFilename(s: string): string {
  return s.replace(/[^a-zA-Z0-9-_\.]+/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const user = session.user as AuthenticatedUser;
  const { id: timesheetId } = params;
  const requestBody = await request.json();
  const { approvalType } = requestBody;

  // Load timesheet with relations needed for authorization, naming, and counts
  const authTimesheet = await prisma.timesheet.findUnique({
    where: { id: timesheetId },
    include: {
      shift: {
        include: {
          job: { include: { company: true } },
          assignedPersonnel: true,
        },
      },
      entries: true,
    },
  });
  if (!authTimesheet || !authTimesheet.shift || !authTimesheet.shift.job || !authTimesheet.shift.job.company) {
    return NextResponse.json({ error: 'Timesheet not found' }, { status: 404 });
  }

  const companyId = authTimesheet.shift.job.companyId;
  const assignedCC = authTimesheet.shift.assignedPersonnel.some(p => p.userId === user.id && p.roleCode === 'CC');
  const isCompanyUserWithMatch = isCompanyUser(user) && user.companyId === companyId;

  // Authorization per rules
  if (approvalType === 'client' || approvalType === 'company') {
    if (!(isAdmin(user) || isManager(user) || assignedCC || isCompanyUserWithMatch)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const parsedData = clientApprovalBodySchema.parse(requestBody);

    const updatedTimesheet = await prisma.$transaction(async (tx) => {
      // Update status and store signature first
      const timesheet = await tx.timesheet.update({
        where: { id: timesheetId },
        data: {
          status: TimesheetStatus.PENDING_MANAGER_APPROVAL,
          company_signature: parsedData.signature,
          company_approved_at: new Date(),
          companyApprovedBy: user.id,
          company_notes: parsedData.notes,
        },
        include: { shift: { include: { job: { include: { company: true } }, assignedPersonnel: true } } },
      });

      // Generate Excel and PDF buffers
      const workbook = await generateTimesheetExcel(timesheet.id, user);
      const excelBuffer = Buffer.from(await workbook.xlsx.writeBuffer());
      const pdfGenerator = new PdfFromExcelGenerator(timesheet.id);
      const pdfBuffer = await pdfGenerator.generatePDF(user);

      // Build filename pieces
      const companyName = sanitizeForFilename(timesheet.shift.job.company.name);
      const jobName = sanitizeForFilename(timesheet.shift.job.name);
      const start = new Date(timesheet.shift.startTime);
      const yyyy = start.getFullYear();
      const mm = String(start.getMonth() + 1).padStart(2, '0');
      const dd = String(start.getDate()).padStart(2, '0');
      const hh = String(start.getHours()).padStart(2, '0');
      const mi = String(start.getMinutes()).padStart(2, '0');
      const workerCount = timesheet.shift.assignedPersonnel.filter(p => p.userId).length;
      const baseName = `${companyName}__${jobName}__${yyyy}-${mm}-${dd}_${hh}-${mi}__${workerCount}workers__${timesheet.id}`;

      let signedPdfUrl = '';
      let signedExcelUrl = '';

      // Prefer GCS if configured
      const bucket = process.env.GCS_BUCKET_NAME;
      if (bucket) {
        const prefix = `timesheets/${yyyy}-${mm}`;
        const pdfDest = `${prefix}/${baseName}.pdf`;
        const xlsxDest = `${prefix}/${baseName}.xlsx`;
        signedPdfUrl = await uploadToGCS(pdfBuffer, pdfDest, 'application/pdf');
        signedExcelUrl = await uploadToGCS(excelBuffer, xlsxDest, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      } else {
        const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
        await fs.mkdir(uploadsDir, { recursive: true });
        const pdfPath = path.join(uploadsDir, `${baseName}.pdf`);
        const xlsxPath = path.join(uploadsDir, `${baseName}.xlsx`);
        await fs.writeFile(pdfPath, pdfBuffer);
        await fs.writeFile(xlsxPath, excelBuffer);
        signedPdfUrl = `/uploads/${baseName}.pdf`;
        signedExcelUrl = `/uploads/${baseName}.xlsx`;
      }

      await tx.timesheet.update({
        where: { id: timesheetId },
        data: { signed_pdf_url: signedPdfUrl, signed_excel_url: signedExcelUrl },
      });

      return timesheet;
    });

    return NextResponse.json({ success: true, message: 'Timesheet approved by company, pending manager approval.', timesheet: updatedTimesheet });
  }

  if (approvalType === 'manager') {
    if (!(isAdmin(user) || isManager(user))) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const parsedData = managerApprovalBodySchema.parse(requestBody);
    const updatedTimesheet = await prisma.$transaction(async (tx) => {
      const timesheet = await tx.timesheet.update({
        where: { id: timesheetId },
        data: {
          status: TimesheetStatus.COMPLETED,
          manager_approved_at: new Date(),
          managerApprovedBy: user.id,
          manager_notes: parsedData.notes,
        },
      });
      await tx.shift.update({
        where: { id: timesheet.shiftId },
        data: { status: 'Completed' },
      });
      return timesheet;
    });
    return NextResponse.json({ success: true, message: 'Timesheet has been fully approved by manager.', timesheet: updatedTimesheet });
  }

  return NextResponse.json({ error: 'Invalid approval type' }, { status: 400 });
}