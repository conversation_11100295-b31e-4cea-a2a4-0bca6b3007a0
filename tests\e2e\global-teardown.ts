import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...');

  try {
    // Clean up test data
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    // Delete test shifts
    await prisma.shift.deleteMany({
      where: {
        id: {
          startsWith: 'test-shift-',
        },
      },
    });

    // Delete test jobs
    await prisma.job.deleteMany({
      where: {
        name: 'Test Job',
      },
    });

    // Delete test users
    await prisma.user.deleteMany({
      where: {
        email: {
          in: testEmails,
        },
      },
    });

    // Delete test company
    await prisma.company.deleteMany({
      where: {
        name: 'Test Company',
      },
    });

    console.log('✅ Test environment cleanup complete');
  } catch (error) {
    console.error('❌ Failed to cleanup test environment:', error);
  } finally {
    await prisma.$disconnect();
  }
}

export default globalTeardown;
