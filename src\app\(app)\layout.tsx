"use client";

import React from "react";
import useUnifiedPrefetch from "@/hooks/useUnifiedPrefetch";
import Header from "@/components/Header";
import { LoadingProvider } from "@/providers/loading-provider";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  useUnifiedPrefetch();

  return (
    <div className="bg-background text-foreground min-h-screen">
      <LoadingProvider>
        <Header />
        <main className="min-h-[calc(100vh-4rem)]">
          {children}
        </main>
      </LoadingProvider>
    </div>
  );
}
