'use client';

import { User, UserRole } from './types';
import { getServerSession } from 'next-auth/next';
import type { Session } from 'next-auth';
import { authOptions } from './auth-config';
import { NextResponse } from 'next/server';
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { User as PrismaUser } from '@prisma/client';

interface UserContext {
  id: string;
  role: UserRole;
}

function isBuildTime(): boolean {
  return !!process.env.npm_lifecycle_event?.includes('build');
}

interface AuthenticatedUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  companyId: string | null;
  avatarUrl?: string;
}

export function hasAnyRole(user: AuthenticatedUser | PrismaUser, roles: UserRole[]): boolean {
  if (!user) {
    return false;
  }
  return roles.includes(user.role);
}

export function verifySignatureRequest(signature: string, data: any): boolean {
    console.warn('WARNING: verifySignatureRequest is not implemented - always returns true');
    if (!signature || typeof signature !== 'string') {
        return false;
    }
    if (!data) {
        return false;
    }
    return signature.length > 0;
}

type ApiHandler = (req: Request, context: { params: any }) => Promise<NextResponse>;

type RoleCheck = (role: UserRole) => boolean;

export function withAuth(Component: React.ComponentType<any>, role?: string) {
  return function WithAuth(props: any) {
    const { data: session, status } = useSession();
    const router = useRouter();

    useEffect(() => {
      if (status === "loading") return;
      if (!session) router.push("/login");
      const user = session?.user as User;
      if (role && user?.role !== role) router.push("/unauthorized");
    }, [session, status, router, role]);

    if (session) {
      return <Component {...props} />;
    }

    return null;
  };
}
