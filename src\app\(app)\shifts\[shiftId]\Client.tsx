"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import React, { useState, useEffect, Suspense, useMemo, useCallback } from "react";
import { LoadingSpinner } from "@/components/loading-states";
import ShiftPageProvider from "@/features/shift-management/ShiftPageProvider";
import { useShiftPageData } from "@/features/shift-management/hooks/useShiftPageData";
import { ShiftWithDetails, AuthenticatedUser, UserRole } from "@/lib/types";
import { SchedulingConflictDialog } from "@/components/SchedulingConflictDialog";
const ShiftDetailsLayout = React.lazy(() => import("@/features/shift-management/components/ShiftDetailsLayout").then(module => ({ default: module.ShiftDetailsLayout })));
import { apiService } from "@/lib/services/api";
import { useToast } from "@/hooks/use-toast";

function ShiftDetailsContent({ user }: { user: AuthenticatedUser }) {
  const params = useParams();
  const shiftId = Array.isArray(params.shiftId) ? params.shiftId[0] : (params as any).shiftId;
  const router = useRouter();
  const { toast } = useToast();

  // Conflict dialog state
  const [conflictDialog, setConflictDialog] = useState<{
    open: boolean;
    conflicts: any[];
    currentShift: any;
    workerName: string;
    userId: string;
    roleCode: string;
    assignmentId?: string;
  }>({
    open: false,
    conflicts: [],
    currentShift: null,
    workerName: '',
    userId: '',
    roleCode: '',
  });

  const {
    shift: rawShift,
    assignments,
    users,
    isShiftLoading,
    assignWorker,
    unassignWorker,
    clockIn,
    clockOut,
    endShift, // This is for the whole shift
    endWorkerShift, // This is for individual assignments
    markNoShow,
    masterStartBreak,
    masterEndShift,
    finalizeTimesheet,
    refetch,
    isUpdatingAssignment,
    setIsUpdatingAssignment,
  } = useShiftPageData(shiftId as string);

  const shift: ShiftWithDetails | null = rawShift ? ("shift" in (rawShift as any) ? (rawShift as any).shift : (rawShift as any)) : null;

  if (!shift) {
    return <LoadingSpinner />;
  }

  // Debug: Log shift and assignments from useShiftPageData
  useEffect(() => {
    console.log('ShiftDetailsContent: shift updated', shift, 'assignedPersonnel ref:', (shift as any)?.assignedPersonnel);
    console.log('ShiftDetailsContent: assignments updated', assignments);
  }, [shift, assignments]);

  if (isShiftLoading) {
    return <LoadingSpinner />;
  }

  if (!shift) {
    return (
      <div className="flex items-center justify-center h-64 bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-4">Shift Not Found</h2>
          <p className="text-muted-foreground">
            The shift you are looking for does not exist or has been deleted.
          </p>
        </div>
      </div>
    );
  }

  if (!assignments || !users) {
    return (
      <div className="flex items-center justify-center h-64 bg-background">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Loading Data</h2>
          <p className="text-muted-foreground">Please wait while we load the shift details...</p>
        </div>
      </div>
    );
  }

  // Authorization check
  useEffect(() => {
    if (!shift || !user) return;

    const isCompanyUser = user.role === UserRole.CompanyUser;
    const isStageHand = user.role === UserRole.StageHand;

    if (isCompanyUser) {
      if (shift.job?.companyId !== user.companyId) {
        router.push('/unauthorized');
      }
    } else if (isStageHand) {
      const isAssigned = assignments.some(assignment =>
        assignment.userId === user.id && assignment.status !== 'NoShow'
      );
      if (!isAssigned) {
        router.push('/unauthorized');
      }
    }
  }, [shift, user, assignments, router]);

  if (user?.role === UserRole.CompanyUser && (shift as any)?.job?.companyId !== user.companyId) {
    return <LoadingSpinner />;
  }

  if (user?.role === UserRole.StageHand) {
    const isAssigned = assignments.some(assignment =>
      assignment.userId === user.id && assignment.status !== 'NoShow'
    );
    if (!isAssigned) {
      return <LoadingSpinner />;
    }
  }

  const sortedAssignments = useMemo(() => {
    return [...assignments].sort((a, b) => {
      if (a.user && b.user) {
        const aFirstName = a.user.name.split(' ')[0];
        const bFirstName = b.user.name.split(' ')[0];
        return aFirstName.localeCompare(bFirstName);
      }
      return 0;
    });
  }, [assignments]);

  const typedShift = shift as ShiftWithDetails;

  const handleEditShift = () => {
    router.push(`/shifts/${shiftId}/edit`);
  };

  const handleAssignmentUpdate = useCallback(async (assignmentId: string, userId: string | null, roleCode?: string) => {
    console.log('handleAssignmentUpdate called:', { assignmentId, userId, roleCode });

    // Check if user has permission to assign workers
    if (user?.role === UserRole.StageHand) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to assign workers.",
        variant: "destructive",
      });
      return;
    }

    // For crew chiefs, check if they can manage this shift
    if (user?.role === UserRole.CrewChief) {
      const isCrewChiefAssigned = assignments.some(assignment => 
        assignment.userId === user.id && 
        assignment.roleCode === 'CC' && 
        assignment.status !== 'NoShow'
      );
      
      if (!isCrewChiefAssigned) {
        toast({
          title: "Access Denied",
          description: "You can only manage shifts where you are assigned as crew chief.",
          variant: "destructive",
        });
        return;
      }
    }

    if (userId && roleCode) {
      // Check if the worker is already assigned to this shift in a *different* slot
      const isAlreadyAssigned = assignments.some(a =>
        a.id !== assignmentId && // Exclude the current assignment slot
        a.userId === userId &&
        a.status !== 'NoShow'
      );

      if (isAlreadyAssigned) {
        toast({
          title: "Assignment Failed",
          description: "This worker is already assigned to another slot in this shift.",
          variant: "destructive",
        });
        // Even if the assignment fails, we should reset the loading state
        useEffect(() => {
          setIsUpdatingAssignment(prev => {
            const newState = { ...prev } as any;
            delete (newState as any)[assignmentId];
            return newState;
          });
        }, [assignmentId]);
        return; // Prevent re-assignment
      }

      console.log('Assigning worker:', { userId, roleCode });

      // Optimistically set loading state for the assignment
      useEffect(() => {
        setIsUpdatingAssignment(prev => ({ ...prev, [assignmentId]: true } as any));
      }, [assignmentId]);

      // Check for scheduling conflicts first
      try {
        const conflictCheck = await apiService.checkSchedulingConflicts(shiftId as string, userId);

        if (conflictCheck && conflictCheck.hasConflicts && conflictCheck.conflicts.length > 0) {
          // Show conflict dialog
          const worker = users.find(u => u.id === userId);
          setConflictDialog({
            open: true,
            conflicts: conflictCheck.conflicts,
            currentShift: {
              date: (shift as any)?.date,
              startTime: (shift as any)?.startTime,
              endTime: (shift as any)?.endTime,
              jobName: (shift as any)?.job?.name,
              companyName: (shift as any)?.job?.company?.name,
            },
            workerName: worker?.name || 'Unknown Worker',
            userId,
            roleCode,
            assignmentId
          });
          useEffect(() => {
            setIsUpdatingAssignment(prev => { // Reset loading state if dialog is shown
              const newState = { ...prev } as any;
              delete (newState as any)[assignmentId];
              return newState;
            });
          }, [assignmentId]);
          return; // Don't proceed with assignment
        }

        // No conflicts, proceed with assignment
        assignWorker.mutate({ userId, roleCode, assignmentId }, { // Pass assignmentId
          onSuccess: () => {
            useEffect(() => {
              setIsUpdatingAssignment(prev => {
                const newState = { ...prev } as any;
                delete (newState as any)[assignmentId];
                return newState;
              });
            }, [assignmentId]);
            refetch();
          },
          onError: async (error: any) => {
            useEffect(() => {
              setIsUpdatingAssignment(prev => {
                const newState = { ...prev } as any;
                delete (newState as any)[assignmentId];
                return newState;
              });
            }, [assignmentId]);

            // Fallback: if server detected a conflict, show the dialog with details
            const message = (error?.message || '').toString();
            if (message.includes('SCHEDULING_CONFLICT')) {
              try {
                const conflictCheck = await apiService.checkSchedulingConflicts(shiftId as string, userId);
                if (conflictCheck?.hasConflicts && conflictCheck.conflicts.length > 0) {
                  const worker = users.find(u => u.id === userId);
                  setConflictDialog({
                    open: true,
                    conflicts: conflictCheck.conflicts,
                    currentShift: {
                      date: (shift as any)?.date,
                      startTime: (shift as any)?.startTime,
                      endTime: (shift as any)?.endTime,
                      jobName: (shift as any)?.job?.name,
                      companyName: (shift as any)?.job?.company?.name,
                    },
                    workerName: worker?.name || 'Unknown Worker',
                    userId,
                    roleCode,
                    assignmentId,
                  });
                  return;
                }
              } catch (e) {
                // ignore and fall through to toast
              }
            }

            toast({
              title: "Assignment Failed",
              description: "Could not assign worker. Please try again.",
              variant: "destructive",
            });
            refetch(); // Re-fetch to revert UI on error
          },
        });
      } catch (error) {
        console.error("Failed to check for scheduling conflicts:", error);
        toast({
          title: "Error",
          description: "Could not check for scheduling conflicts. Please try again.",
          variant: "destructive",
        });
        useEffect(() => {
          setIsUpdatingAssignment(prev => {
            const newState = { ...prev } as any;
            delete (newState as any)[assignmentId];
            return newState;
          });
        }, [assignmentId]);
      }
    } else if (assignmentId && !assignmentId.startsWith('placeholder-')) {
      console.log('Unassigning worker:', assignmentId);
      useEffect(() => {
        setIsUpdatingAssignment(prev => ({ ...prev, [assignmentId]: true } as any)); // Optimistic update for UI
      }, [assignmentId]);
      unassignWorker.mutate(assignmentId, {
        onSuccess: () => {
          useEffect(() => {
            setIsUpdatingAssignment(prev => {
              const newState = { ...prev } as any;
              delete (newState as any)[assignmentId];
              return newState;
            });
          }, [assignmentId]);
          refetch(); // Re-fetch after successful unassignment
        },
        onError: () => {
          useEffect(() => {
            setIsUpdatingAssignment(prev => {
              const newState = { ...prev } as any;
              delete (newState as any)[assignmentId];
              return newState;
            });
          }, [assignmentId]);
          toast({
            title: "Unassignment Failed",
            description: "Could not unassign worker. Please try again.",
            variant: "destructive",
          });
          refetch(); // Re-fetch to revert UI on error
        },
      });
    } else if (assignmentId.startsWith('placeholder-')) {
      console.log('Cannot unassign placeholder assignment:', assignmentId);
      // For placeholder assignments, we can't unassign since they don't exist in DB
      // This is expected behavior
    } else {
      console.log('No action taken - insufficient parameters');
    }
  }, [user, assignments, shiftId, toast, setIsUpdatingAssignment, assignWorker, unassignWorker, refetch, users]);

  const handleConflictConfirm = () => {
    // Optimistically set loading state for the assignment
    useEffect(() => {
      setIsUpdatingAssignment(prev => ({ ...prev, [conflictDialog.userId]: true } as any)); // Using userId as key for animation
    }, [conflictDialog.userId]);

    // Proceed with assignment despite conflicts
    assignWorker.mutate({
      userId: conflictDialog.userId,
      roleCode: conflictDialog.roleCode,
      ignoreConflicts: true,
      assignmentId: conflictDialog.assignmentId,
    }, {
      onSuccess: () => {
        useEffect(() => {
          setIsUpdatingAssignment(prev => {
            const newState = { ...prev } as any;
            delete (newState as any)[conflictDialog.userId];
            return newState;
          });
        }, [conflictDialog.userId]);
        refetch();
      },
      onError: () => {
        useEffect(() => {
          setIsUpdatingAssignment(prev => {
            const newState = { ...prev } as any;
            delete (newState as any)[conflictDialog.userId];
            return newState;
          });
        }, [conflictDialog.userId]);
        toast({
          title: "Assignment Failed",
          description: "Could not assign worker. Please try again.",
          variant: "destructive",
        });
        refetch(); // Re-fetch to revert UI on error
      },
    });
    setConflictDialog(prev => ({ ...prev, open: false }));
  };

  const handleConflictCancel = () => {
    setConflictDialog(prev => ({ ...prev, open: false }));
  };

  const [canManageTime, setCanManageTime] = useState(false);

  useEffect(() => {
    const checkPermissions = () => {
      if (!user) return false;
      if (user.role === UserRole.Admin) return true;
      if (user.role === UserRole.CrewChief) {
        return assignments.some(assignment =>
          assignment.userId === user.id &&
          assignment.roleCode === 'CC' &&
          assignment.status !== 'NoShow'
        );
      }
      return false;
    };
    setCanManageTime(checkPermissions());
  }, [user, assignments]);

  return (
    <>
      <SchedulingConflictDialog
        open={conflictDialog.open}
        onOpenChange={(open) => setConflictDialog(prev => ({ ...prev, open }))}
        conflicts={conflictDialog.conflicts}
        currentShift={conflictDialog.currentShift}
        workerName={conflictDialog.workerName}
        onConfirm={handleConflictConfirm}
        onCancel={handleConflictCancel}
        isLoading={assignWorker.isPending}
      />
      <Suspense fallback={<LoadingSpinner />}>
        <ShiftDetailsLayout
          shift={typedShift}
          assignments={sortedAssignments}
          users={users}
          onAssignmentUpdate={handleAssignmentUpdate}
          onEditShift={handleEditShift}
          onClockIn={canManageTime ? (assignmentId) => clockIn.mutate(assignmentId, {
            onSuccess: () => refetch(),
          }) : undefined}
          onClockOut={canManageTime ? (assignmentId) => clockOut.mutate(assignmentId, {
            onSuccess: () => refetch(),
          }) : undefined}
          onEndWorkerShift={canManageTime ? (assignmentId) => endWorkerShift.mutate(assignmentId, {
            onSuccess: () => refetch(),
          }) : undefined}
          onMarkNoShow={canManageTime ? (assignmentId) => markNoShow.mutate(assignmentId, {
            onSuccess: () => refetch(),
          }) : undefined}
          onMasterStartBreak={canManageTime ? () => masterStartBreak.mutate(undefined, {
            onSuccess: () => refetch(),
          }) : undefined}
          onMasterEndShift={canManageTime ? () => masterEndShift.mutate(undefined, {
            onSuccess: () => refetch(),
          }) : undefined}
          onFinalizeTimesheet={canManageTime ? () => finalizeTimesheet.mutate(undefined, {
            onSuccess: () => refetch(),
          }) : undefined}
          onRefresh={refetch}
          userRole={user?.role}
          isUpdatingAssignment={isUpdatingAssignment}
        />
      </Suspense>
    </>
  );
}

export default function ShiftDetailsClient({ user }: { user: AuthenticatedUser }) {
  return (
    <ShiftPageProvider>
      <ShiftDetailsContent user={user} />
    </ShiftPageProvider>
  );
}
