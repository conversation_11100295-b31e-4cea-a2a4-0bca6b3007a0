import React from 'react';
import { User } from '@/lib/types';
import { UserX, Crown, Truck, HardHat, Award } from "lucide-react";

export const UP_FOR_GRABS_ID = 'up-for-grabs';
export const UNASSIGNED_ID = null;

export const HIGHLIGHTED_UP_FOR_GRABS = -1;
export const HIGHLIGHTED_UNASSIGNED = -2;

export const getUserStatusBadges = (user: User) => {
  const badges = [];
  
  if (!user.isActive) {
    badges.push(
      <span key="inactive" className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
        <UserX className="w-3 h-3 mr-1" />
        Inactive
      </span>
    );
  }
  
  if (user.crew_chief_eligible) {
    badges.push(
      <span key="cc" className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        <Crown className="w-3 h-3 mr-1" />
        CC
      </span>
    );
  }
  
  if (user.fork_operator_eligible) {
    badges.push(
      <span key="fo" className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <Truck className="w-3 h-3 mr-1" />
        FO
      </span>
    );
  }

  if (user.OSHA_10_Certifications) {
    badges.push(
      <span key="osha" className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
        <HardHat className="w-3 h-3 mr-1" />
        OSHA
      </span>
    );
  }

  if (user.certifications && user.certifications.length > 0) {
    badges.push(
      <span key="certs" className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
        <Award className="w-3 h-3 mr-1" />
        {user.certifications.length}
      </span>
    );
  }

  return badges;
};
