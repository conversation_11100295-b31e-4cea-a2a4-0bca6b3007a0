import type { Company, Job, Shift, User, UserRole } from '@prisma/client';

export const mockUsers: Record<UserRole, User> = {
  Staff: { id: 'staff1', name: 'Staff User', email: '<EMAIL>', role: 'Staff', passwordHash: 'hashedpassword', isActive: true, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null, crew_chief_eligible: false, fork_operator_eligible: false, OSHA_10_Certifications: false, phone: null, payrollType: 'HOURLY', payrollBaseRateCents: 0, ssnEncrypted: null, ssnLast4: null, addressLine1: null, addressLine2: null, city: null, state: null, postalCode: null, upForGrabsNotifications: false },
  Admin: { id: 'adm1', name: 'Admin User', email: '<EMAIL>', role: 'Admin', passwordHash: 'hashedpassword', isActive: true, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null, crew_chief_eligible: false, fork_operator_eligible: false, OSHA_10_Certifications: false, phone: null, payrollType: 'HOURLY', payrollBaseRateCents: 0, ssnEncrypted: null, ssnLast4: null, addressLine1: null, addressLine2: null, city: null, state: null, postalCode: null, upForGrabsNotifications: false },
  CompanyUser: { id: 'cli-user1', name: 'John Smith', email: '<EMAIL>', role: 'CompanyUser', companyId: 'cli1', passwordHash: 'hashedpassword', isActive: true, certifications: [], performance: null, location: null, avatarUrl: null, crew_chief_eligible: false, fork_operator_eligible: false, OSHA_10_Certifications: false, phone: null, payrollType: 'HOURLY', payrollBaseRateCents: 0, ssnEncrypted: null, ssnLast4: null, addressLine1: null, addressLine2: null, city: null, state: null, postalCode: null, upForGrabsNotifications: false },
  CrewChief: { id: 'cc1', name: 'Maria Garcia', email: '<EMAIL>', role: 'CrewChief', passwordHash: 'hashedpassword', isActive: true, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null, crew_chief_eligible: false, fork_operator_eligible: false, OSHA_10_Certifications: false, phone: null, payrollType: 'HOURLY', payrollBaseRateCents: 0, ssnEncrypted: null, ssnLast4: null, addressLine1: null, addressLine2: null, city: null, state: null, postalCode: null, upForGrabsNotifications: false },
  StageHand: { id: 'emp2', name: 'Maria Garcia', email: '<EMAIL>', role: 'StageHand', passwordHash: 'hashedpassword', isActive: true, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null, crew_chief_eligible: false, fork_operator_eligible: false, OSHA_10_Certifications: false, phone: null, payrollType: 'HOURLY', payrollBaseRateCents: 0, ssnEncrypted: null, ssnLast4: null, addressLine1: null, addressLine2: null, city: null, state: null, postalCode: null, upForGrabsNotifications: false },
  Manager: { id: 'sup1', name: 'Manager User', email: '<EMAIL>', role: 'Manager', passwordHash: 'hashedpassword', isActive: true, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null, crew_chief_eligible: false, fork_operator_eligible: false, OSHA_10_Certifications: false, phone: null, payrollType: 'HOURLY', payrollBaseRateCents: 0, ssnEncrypted: null, ssnLast4: null, addressLine1: null, addressLine2: null, city: null, state: null, postalCode: null, upForGrabsNotifications: false },
};
