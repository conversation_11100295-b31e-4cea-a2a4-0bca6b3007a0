export const dynamic = 'force-dynamic';
export const revalidate = 0;

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';

// List current and past up-for-grabs offers
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || user.role !== 'Admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const items = await prisma.assignedPersonnel.findMany({
      where: { status: { in: ['UpForGrabs', 'Assigned'] } },
      orderBy: { updatedAt: 'desc' },
      include: {
        shift: { include: { job: { include: { company: true } } } },
        user: true,
      },
      take: 200,
    });

    // Map with offeredBy/claimedBy names if available
    const result = await Promise.all(items.map(async (a) => {
      const offeredBy = a.offeredById ? await prisma.user.findUnique({ where: { id: a.offeredById }, select: { id: true, name: true } }) : null;
      const claimedBy = a.claimedById ? await prisma.user.findUnique({ where: { id: a.claimedById }, select: { id: true, name: true } }) : null;
      return { ...a, offeredBy, claimedBy };
    }));

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error listing up-for-grabs offers', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}