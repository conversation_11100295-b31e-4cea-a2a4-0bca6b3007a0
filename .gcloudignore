# This file specifies files that are *not* uploaded to Google Cloud
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).

.gcloudignore
.git
.gitignore

# Include standard gitignore patterns
#!include:.gitignore

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Build outputs
dist/
build/

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Test files
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Documentation
docs/
*.md
README*

# Sample frontend (not needed for production)
sample-frontend/

# VSCode extension directory
vscode-next/

# Temporary files
tmp/
temp/
.tmp/

# Cache directories
.cache/
.parcel-cache/

# Storybook build outputs
storybook-static/

# Miscellaneous
*.tsbuildinfo
.eslintcache
