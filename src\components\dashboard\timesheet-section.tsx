'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge'; // Still imported, but not used for count badge in header
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  CheckCircle,
  AlertCircle,
  Eye,
  Edit,
  Users, // Still imported (though not explicitly used for count like +X workers)
  Calendar,
  Building2,
  ChevronRight,
} from "lucide-react";
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Avatar } from '@/components/Avatar'; // Assuming this is your custom Avatar component
import { EnhancedStatusBadge as UnifiedStatusBadge } from '@/components/ui/enhanced-status-badge';

// Define a more specific interface for timesheet data for better type safety
interface TimesheetPersonnel {
  user: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
}

interface TimesheetShift {
  id: string;
  date: string;
  description?: string;
  job: {
    id: string;
    name: string;
    company: {
      id: string;
      name: string;
      company_logo_url?: string; // Company logo is not displayed per card in this design
    };
  };
  assignedPersonnel: TimesheetPersonnel[];
}

interface Timesheet {
  id: string;
  shiftId: string;
  status: string; // e.g., 'pending_manager', 'pending_client', 'approved', 'rejected'
  submittedAt?: string;
  shift: TimesheetShift;
  permissions: {
    canApprove: boolean;
    canModify: boolean;
  };
}

interface TimesheetSectionProps {
  timesheets: Timesheet[]; // This component will now only display the first one
  isLoading: boolean;
  error: Error | null; // More specific error type
  onTimesheetClick: (timesheetId: string, shiftId: string) => void;
  onApproveTimesheet?: (timesheetId: string) => void;
  onRejectTimesheet?: (timesheetId: string) => void;
  className?: string;
}

export function TimesheetSection({
  timesheets,
  isLoading,
  error,
  onTimesheetClick,
  onApproveTimesheet,
  className,
}: TimesheetSectionProps) {
  // Get the first timesheet if available
  const timesheet = timesheets?.[0];

  if (!timesheet && !isLoading && !error) {
    return (
      <Card className={cn("shadow-lg bg-slate-900", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-100">
            <FileText className="h-5 w-5 text-green-600" />
            Timesheet Pending Approval
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="h-14 w-14 text-green-500 mx-auto mb-4" />
            <h3 className="font-semibold text-lg text-gray-700 dark:text-gray-300">All caught up!</h3>
            <p className="text-sm text-muted-foreground mt-2">
              No timesheets pending approval at this time.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // --- Loading State ---
  if (isLoading) {
    return (
      <Card className={cn("shadow-lg bg-slate-900", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-100">
            <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            Timesheet Pending Approval {/* Changed to singular */}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Skeleton for a single timesheet item */}
          <div className="flex flex-col gap-3 p-4 border border-slate-700 rounded-xl">
            {/* Top row with main title and subtitle placeholders */}
            <Skeleton className="h-8 w-3/4 mb-1 bg-slate-700" />
            <Skeleton className="h-6 w-1/2 mb-4 bg-slate-700" />

            {/* Company name placeholder */}
            <Skeleton className="h-4 w-full bg-slate-700" />

            {/* Date placeholder */}
            <Skeleton className="h-4 w-1/3 mb-4 bg-slate-700" />

            {/* Avatars and View button placeholders */}
            <div className="flex items-center justify-between">
              <div className="flex -space-x-2">
                <Skeleton className="h-10 w-10 rounded-full bg-slate-700" />
                <Skeleton className="h-10 w-10 rounded-full bg-slate-700" />
                <Skeleton className="h-10 w-10 rounded-full bg-slate-700" />
              </div>
              <Skeleton className="h-9 w-20 rounded-md bg-slate-700" /> {/* View button */}
            </div>
            
            {/* Status badge placeholder */}
            <Skeleton className="h-6 w-1/4 mt-4 bg-slate-700" />

            {/* Submitted at placeholder */}
            <Skeleton className="h-3 w-1/3 mt-4 bg-slate-700" />

            {/* Action buttons placeholders */}
            <div className="flex justify-end gap-2 mt-4">
              <Skeleton className="h-9 w-24 rounded-md bg-slate-700" /> {/* Review button */}
              <Skeleton className="h-9 w-20 rounded-md bg-slate-700" /> {/* Edit button */}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // --- Error State ---
  if (error) {
    return (
      <Card className={cn("shadow-lg bg-slate-900", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-100">
            <FileText className="h-5 w-5 text-red-500" />
            Timesheet Pending Approval
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive" className="flex items-center gap-3 bg-red-900/20 text-red-300 border-red-700">
            <AlertCircle className="h-5 w-5 flex-shrink-0 text-red-400" />
            <AlertDescription className="text-sm">
              Failed to load timesheet. Please try again later.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }


  // --- Main Content (Single Timesheet) ---
  const shiftDescription =
    timesheet.shift.description && timesheet.shift.description.trim().length > 0
      ? timesheet.shift.description.length > 40
        ? timesheet.shift.description.slice(0, 40) + "..."
        : timesheet.shift.description
      : timesheet.shift.job.name;

  const avatars = timesheet.shift.assignedPersonnel;
  const MAX_AVATARS_TO_SHOW = 3;
  const displayAvatars = avatars.slice(0, MAX_AVATARS_TO_SHOW);
  const extraCount = avatars.length - MAX_AVATARS_TO_SHOW;

  return (
    <Card className={cn("shadow-lg bg-slate-900", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-100">
          <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          Timesheet Pending Approval {/* Changed to singular */}
          {/* Removed the timesheets.length Badge, as it's now a single view */}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4"> {/* space-y-4 provides vertical spacing for internal sections */}
        <div
          // This is the container for the single timesheet's details.
          // It now *is* the content of the card, not one of many items.
          className="group flex flex-col gap-3 p-4 border border-slate-700 rounded-xl hover:bg-slate-800 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md"
          onClick={() => onTimesheetClick(timesheet.id, timesheet.shiftId)}
        >
          {/* Main Titles */}
          <div className="flex flex-col gap-1">
            {/* Shift Description (Main Title) */}
            <h3 className="font-bold text-2xl text-gray-100 truncate">
              {shiftDescription}
            </h3>
            {/* Job Name */}
            <p className="font-bold text-xl text-gray-300 truncate">
              {timesheet.shift.job.name}
            </p>
          </div>

          {/* Company Name Row */}
          <div className="flex items-center gap-2 text-base text-gray-400">
            <Building2 className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">{timesheet.shift.job.company.name}</span>
          </div>

          {/* Date with Icon */}
          <div className="flex items-center gap-2 text-base text-gray-400">
            <Calendar className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">
              {timesheet.shift.date ? format(new Date(timesheet.shift.date), "MMM d, yyyy") : 'No date'}
            </span>
          </div>

          {/* Avatars & View Button Row */}
          <div className="flex items-center justify-between gap-4 mt-2">
            <div className="flex items-center -space-x-2">
              {displayAvatars.map((ap) => (
                <Avatar
                  key={ap.user.id}
                  src={ap.user.avatarUrl}
                  name={ap.user.name}
                  userId={ap.user.id}
                  size="md"
                  enableSmartCaching={true}
                  className="h-10 w-10 border-2 border-background shadow-sm"
                />
              ))}
              {extraCount > 0 && (
                <div className="h-10 w-10 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium text-gray-600 shadow-sm">
                  +{extraCount}
                </div>
              )}
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onTimesheetClick(timesheet.id, timesheet.shiftId);
              }}
              className="flex-shrink-0 text-gray-400 hover:text-gray-200"
            >
              <Eye className="h-4 w-4 mr-1" />
              <span>View</span>
              <ChevronRight className="h-4 w-4 ml-1 text-muted-foreground" />
            </Button>
          </div>

          {/* Status Badge - Positioned as per screenshot */}
          <div className="mt-4">
            <UnifiedStatusBadge status={timesheet.status as any} size="md" className="flex-shrink-0" />
          </div>

          {timesheet.submittedAt && (
            <div className="text-xs text-muted-foreground mt-4">
              Submitted {format(new Date(timesheet.submittedAt), "MMM d, h:mm a")}
            </div>
          )}

          {/* Action Buttons at the bottom */}
          <div className="flex justify-end gap-2 mt-4">
            {timesheet.permissions.canApprove && onApproveTimesheet && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onApproveTimesheet(timesheet.id);
                }}
                className="bg-transparent border border-blue-500 text-blue-400 hover:bg-blue-900/20 flex-shrink-0"
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                <span>Review</span>
              </Button>
            )}
            {timesheet.permissions.canModify && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onTimesheetClick(timesheet.id, timesheet.shiftId);
                }}
                className="flex-shrink-0 border-gray-700 text-gray-100 hover:bg-gray-800 dark:border-gray-700 dark:text-gray-100 dark:hover:bg-gray-800"
              >
                <Edit className="h-4 w-4 mr-1" />
                <span>Edit</span>
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
