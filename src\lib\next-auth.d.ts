import 'next-auth';
import { UserRole } from './types';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      role: UserRole;
      companyId?: string;
      avatarUrl?: string;
      name?: string;
      email?: string;
      company?: any;
      upForGrabsNotifications?: boolean;
      OSHA_10_Certifications?: boolean;
      location?: string;
      crew_chief_eligible?: boolean;
      fork_operator_eligible?: boolean;
      certifications?: any[];
      performance?: any;
      phone?: string;
      payrollType?: string;
      payrollBaseRateCents?: number;
      ssnLast4?: string;
      addressLine1?: string;
      addressLine2?: string;
      city?: string;
      state?: string;
      postalCode?: string;
    } & DefaultSession['user'];
  }

  interface User {
    role: UserRole;
    companyId?: string;
    avatarUrl?: string;
    company?: any;
    upForGrabsNotifications?: boolean;
    OSHA_10_Certifications?: boolean;
    location?: string;
    crew_chief_eligible?: boolean;
    fork_operator_eligible?: boolean;
    certifications?: any[];
    performance?: any;
    phone?: string;
    payrollType?: string;
    payrollBaseRateCents?: number;
    ssnLast4?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: UserRole;
    companyId?: string;
    avatarUrl?: string;
    name?: string;
    email?: string;
    company?: any;
    upForGrabsNotifications?: boolean;
    OSHA_10_Certifications?: boolean;
    location?: string;
    crew_chief_eligible?: boolean;
    fork_operator_eligible?: boolean;
    certifications?: any[];
    performance?: any;
    phone?: string;
    payrollType?: string;
    payrollBaseRateCents?: number;
    ssnLast4?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
  }
}