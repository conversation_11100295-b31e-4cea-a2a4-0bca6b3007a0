import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@prisma/client';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const user = await getCurrentUser(req);
  if (!user || user.role !== UserRole.Admin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  const { id } = await params;
  const period = await prisma.payrollPeriod.findUnique({ where: { id }, include: { entries: { include: { user: true } } } });
  if (!period) return NextResponse.json({ error: 'Not found' }, { status: 404 });
  return NextResponse.json({ period });
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const user = await getCurrentUser(req);
  if (!user || user.role !== UserRole.Admin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  const { id } = await params;
  const body = await req.json();
  const { status } = body || {};
  const period = await prisma.payrollPeriod.update({ where: { id }, data: { status } });
  return NextResponse.json({ period });
}
