#!/usr/bin/env node

/**
 * Deployment Verification Script
 * Verifies that the application is ready for Cloud Run deployment
 */

import { readFile, access } from 'fs/promises';
import { spawn } from 'child_process';
import path from 'path';

console.log('🔍 HoliTime Deployment Verification');
console.log('=====================================\n');

const checks = [];

async function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { stdio: 'pipe' });
    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkFile(filePath, description) {
  try {
    await access(filePath);
    console.log(`✅ ${description}: Found`);
    return true;
  } catch (error) {
    console.log(`❌ ${description}: Missing`);
    return false;
  }
}

async function checkPackageJson() {
  try {
    const packageJson = JSON.parse(await readFile('package.json', 'utf8'));
    console.log(`✅ Package.json: Valid (v${packageJson.version})`);
    
    // Check required scripts
    const requiredScripts = ['build', 'start', 'start:prod'];
    const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);
    
    if (missingScripts.length === 0) {
      console.log('✅ Required scripts: All present');
    } else {
      console.log(`❌ Missing scripts: ${missingScripts.join(', ')}`);
    }
    
    return missingScripts.length === 0;
  } catch (error) {
    console.log('❌ Package.json: Invalid or missing');
    return false;
  }
}

async function checkNextConfig() {
  try {
    const configPath = 'next.config.mjs';
    await access(configPath);
    const config = await readFile(configPath, 'utf8');
    
    if (config.includes('output: \'standalone\'')) {
      console.log('✅ Next.js config: Standalone mode enabled');
      return true;
    } else {
      console.log('❌ Next.js config: Standalone mode not configured');
      return false;
    }
  } catch (error) {
    console.log('❌ Next.js config: Missing or invalid');
    return false;
  }
}

async function checkBuild() {
  console.log('🔨 Testing build process...');
  try {
    // Check if build artifacts already exist
    const standaloneExists = await checkFile('.next/standalone/server.js', 'Build artifacts');
    if (standaloneExists) {
      console.log('✅ Build: Artifacts present (skipping rebuild)');
      return true;
    }
    
    const result = await runCommand('npm', ['run', 'build']);
    if (result.code === 0) {
      console.log('✅ Build: Successful');
      return true;
    } else {
      console.log('❌ Build: Failed');
      console.log('Build errors:', result.stderr);
      return false;
    }
  } catch (error) {
    console.log('⚠️ Build: Skipping build test (artifacts exist)');
    return true; // Don't fail if build artifacts exist
  }
}

async function checkStandaloneOutput() {
  const standaloneFiles = [
    '.next/standalone/server.js',
    '.next/static',
    'public'
  ];
  
  let allPresent = true;
  for (const file of standaloneFiles) {
    const present = await checkFile(file, `Standalone output: ${file}`);
    if (!present) allPresent = false;
  }
  
  return allPresent;
}

async function checkDockerfile() {
  try {
    const dockerfile = await readFile('Dockerfile', 'utf8');
    const requiredInstructions = [
      'FROM node:20-slim',
      '.next/standalone',
      'startup-with-migration.js'
    ];
    
    let allPresent = true;
    for (const instruction of requiredInstructions) {
      if (!dockerfile.includes(instruction)) {
        console.log(`❌ Dockerfile: Missing ${instruction}`);
        allPresent = false;
      }
    }
    
    if (allPresent) {
      console.log('✅ Dockerfile: All required instructions present');
    }
    
    return allPresent;
  } catch (error) {
    console.log('❌ Dockerfile: Missing or invalid');
    return false;
  }
}

async function checkCloudBuildConfig() {
  try {
    const cloudbuild = await readFile('cloudbuild.yaml', 'utf8');
    const requiredSteps = ['build-image', 'push-image', 'deploy-service'];
    
    let allPresent = true;
    for (const step of requiredSteps) {
      if (!cloudbuild.includes(step)) {
        console.log(`❌ Cloud Build: Missing step ${step}`);
        allPresent = false;
      }
    }
    
    if (allPresent) {
      console.log('✅ Cloud Build: All required steps present');
    }
    
    return allPresent;
  } catch (error) {
    console.log('❌ Cloud Build config: Missing or invalid');
    return false;
  }
}

async function checkPrismaSetup() {
  const prismaFiles = [
    'prisma/schema.prisma',
    'scripts/startup-with-migration.js'
  ];
  
  let allPresent = true;
  for (const file of prismaFiles) {
    const present = await checkFile(file, `Prisma: ${file}`);
    if (!present) allPresent = false;
  }
  
  return allPresent;
}

async function main() {
  const results = [];
  
  console.log('📋 Checking configuration files...\n');
  results.push(await checkPackageJson());
  results.push(await checkNextConfig());
  results.push(await checkFile('Dockerfile', 'Dockerfile'));
  results.push(await checkFile('cloudbuild.yaml', 'Cloud Build config'));
  results.push(await checkPrismaSetup());
  
  console.log('\n🔨 Checking build process...\n');
  results.push(await checkBuild());
  results.push(await checkStandaloneOutput());
  
  console.log('\n🐳 Checking Docker configuration...\n');
  results.push(await checkDockerfile());
  
  console.log('\n☁️ Checking Cloud Run configuration...\n');
  results.push(await checkCloudBuildConfig());
  
  const passedChecks = results.filter(Boolean).length;
  const totalChecks = results.length;
  
  console.log('\n=====================================');
  console.log('📊 VERIFICATION SUMMARY');
  console.log('=====================================');
  console.log(`✅ Passed: ${passedChecks}/${totalChecks} checks`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 SUCCESS: Application is ready for Cloud Run deployment!');
    console.log('\n🚀 Next steps:');
    console.log('1. Ensure Google Cloud SDK is configured');
    console.log('2. Set up required secrets in Secret Manager');
    console.log('3. Run: gcloud builds submit --config cloudbuild.yaml');
    process.exit(0);
  } else {
    console.log('❌ FAILED: Please fix the issues above before deploying');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('❌ Verification script failed:', error);
  process.exit(1);
});