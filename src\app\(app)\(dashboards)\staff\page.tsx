"use client";

import { useSession } from "next-auth/react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function StaffWelcomePage() {
  const { data: session } = useSession();
  const user = session?.user;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-2xl text-center shadow-lg">
        <CardHeader>
          <div className="flex justify-center mb-4">
            <Avatar className="h-24 w-24">
              <AvatarImage src={user?.image || ""} alt={user?.name || "User"} />
              <AvatarFallback>{user?.name?.[0]}</AvatarFallback>
            </Avatar>
          </div>
          <CardTitle className="text-3xl font-bold">
            Welcome, {user?.name || "User"}!
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-lg text-gray-600">
            Your account is currently pending assignment. Please check back later for full access to the dashboard.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
