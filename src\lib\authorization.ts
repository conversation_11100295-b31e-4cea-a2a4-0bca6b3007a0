import { UserRole } from '@prisma/client';
import { AuthenticatedUser } from '@/lib/types';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export interface AuthorizationContext {
  user: AuthenticatedUser;
  resource?: any;
  resourceId?: string;
}

// Simple hierarchy: Admin > Manager > CrewChief > StageHand > PendingUser
const ROLE_HIERARCHY: Record<UserRole, number> = {
  PendingUser: 0,
  StageHand: 1,
  CrewChief: 2,
  Manager: 3,
  Admin: 4,
  CompanyUser: 0, // treated via ABAC (company scope); not in promotion chain
};

export function hasMinimumRole(user: AuthenticatedUser, minimumRole: UserRole): boolean {
  return (ROLE_HIERARCHY[user.role] ?? 0) >= (ROLE_HIERARCHY[minimumRole] ?? 0);
}

export function isAdmin(user: AuthenticatedUser): boolean {
  return user.role === UserRole.Admin;
}
export function isManager(user: AuthenticatedUser): boolean {
  return user.role === UserRole.Manager;
}
export function isCrewChief(user: AuthenticatedUser): boolean {
  return user.role === UserRole.CrewChief;
}
export function isCompanyUser(user: AuthenticatedUser): boolean {
  return user.role === UserRole.CompanyUser;
}

export function belongsToSameCompany(user: AuthenticatedUser, companyId?: string | null): boolean {
  return !!companyId && user.companyId === companyId;
}

export function canAccessOwnResource(user: AuthenticatedUser, resourceUserId?: string): boolean {
  return !!resourceUserId && user.id === resourceUserId;
}

// Check if crew chief has been assigned to any shift for a company (any worker type)
export async function hasCrewChiefCompanyAccess(user: AuthenticatedUser, companyId: string): Promise<boolean> {
  if (!isCrewChief(user)) return false;

  const assignment = await prisma.assignedPersonnel.findFirst({
    where: {
      userId: user.id,
      shift: {
        job: {
          companyId: companyId
        }
      }
    }
  });

  return !!assignment;
}

// Check if crew chief is assigned as crew chief (CC role) on a specific shift
export async function isCrewChiefOnShift(user: AuthenticatedUser, shiftId: string): Promise<boolean> {
  if (!isCrewChief(user)) return false;

  const assignment = await prisma.assignedPersonnel.findFirst({
    where: {
      userId: user.id,
      shiftId: shiftId,
      roleCode: 'CC'
    }
  });

  return !!assignment;
}

// Check if user is assigned to a shift in any capacity
export async function isAssignedToShift(user: AuthenticatedUser, shiftId: string): Promise<boolean> {
  const assignment = await prisma.assignedPersonnel.findFirst({
    where: {
      userId: user.id,
      shiftId: shiftId
    }
  });

  return !!assignment;
}

// Centralized permission map aligned to RBAC/ABAC requirements
export const PERMISSIONS = {
  USER: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: ({ user, resourceId }: AuthorizationContext) => isAdmin(user) || isManager(user) || canAccessOwnResource(user, resourceId),
    UPDATE: ({ user, resourceId }: AuthorizationContext) => isAdmin(user) || isManager(user) || canAccessOwnResource(user, resourceId),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can hard delete users
  },

  COMPANY: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: ({ user, resource }: AuthorizationContext) => isAdmin(user) || isManager(user) || (isCompanyUser(user) && belongsToSameCompany(user, resource?.id)),
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can delete companies
  },

  JOB: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: async ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      if (isCompanyUser(user) && belongsToSameCompany(user, resource?.companyId)) return true;
      if (isCrewChief(user) && resource?.companyId) {
        return await hasCrewChiefCompanyAccess(user, resource.companyId);
      }
      return false;
    },
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can delete jobs
  },

  SHIFT: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    READ: async ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      if (isCompanyUser(user) && belongsToSameCompany(user, resource?.job?.companyId)) return true;
      if (isCrewChief(user) && resource?.job?.companyId) {
        return await hasCrewChiefCompanyAccess(user, resource.job.companyId);
      }
      if (user.role === UserRole.StageHand && resource?.id) {
        return await isAssignedToShift(user, resource.id);
      }
      return false;
    },
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can delete shifts
  },

  TIMESHEET: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    read: async ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      if (isCompanyUser(user) && belongsToSameCompany(user, resource?.shift?.job?.companyId)) return true;
      if (isCrewChief(user) && resource?.shift?.job?.companyId) {
        return await hasCrewChiefCompanyAccess(user, resource.shift.job.companyId);
      }
      if (user.role === UserRole.StageHand) return resource?.entries?.some((e: any) => e.userId === user.id) ?? false;
      return false;
    },
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can delete timesheets
    APPROVE: ({ user, resource }: AuthorizationContext) => {
      // CompanyUser can approve company step; Manager/Admin can finalize approval
      if (isAdmin(user) || isManager(user)) return true;
      if (isCompanyUser(user) && belongsToSameCompany(user, resource?.shift?.job?.companyId)) return true;
      return false;
    },
    REJECT: ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      return isCompanyUser(user) && belongsToSameCompany(user, resource?.shift?.job?.companyId);
    },
  },

  TIME_ENTRY: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user) || user.role === UserRole.StageHand,
    READ: async ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      if (canAccessOwnResource(user, resource?.assignedPersonnel?.userId)) return true;
      if (isCrewChief(user) && resource?.assignedPersonnel?.shiftId) {
        // Crew chief can read time entries for shifts where they are crew chief OR their own entries
        const isCrewChiefOnThisShift = await isCrewChiefOnShift(user, resource.assignedPersonnel.shiftId);
        const isOwnEntry = canAccessOwnResource(user, resource.assignedPersonnel.userId);
        return isCrewChiefOnThisShift || isOwnEntry;
      }
      return false;
    },
    UPDATE: async ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      if (isCrewChief(user) && resource?.assignedPersonnel?.shiftId) {
        return await isCrewChiefOnShift(user, resource.assignedPersonnel.shiftId);
      }
      return false;
    },
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can delete time entries
  },

  ANNOUNCEMENT: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: ({ user }: AuthorizationContext) => !!user,
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // Only admins can delete announcements
  },
} as const;

export async function hasPermission(
  user: AuthenticatedUser,
  resource: string,
  action: string,
  context: { resource?: any; resourceId?: string } = {}
): Promise<boolean> {
  if (!user) return false;
  const resourcePermissions = (PERMISSIONS as any)[resource];
  if (!resourcePermissions) return false;
  const actionPermission = resourcePermissions[action];
  if (!actionPermission) return false;

  // Handle both sync and async permission functions
  const result = actionPermission({ user, ...context });
  return result instanceof Promise ? await result : result;
}

// Synchronous version for backwards compatibility where async is not needed
export function hasPermissionSync(
  user: AuthenticatedUser,
  resource: string,
  action: string,
  context: { resource?: any; resourceId?: string } = {}
): boolean {
  if (!user) return false;
  const resourcePermissions = (PERMISSIONS as any)[resource];
  if (!resourcePermissions) return false;
  const actionPermission = resourcePermissions[action];
  if (!actionPermission) return false;

  // Only call sync permissions
  const result = actionPermission({ user, ...context });
  if (result instanceof Promise) {
    throw new Error(`Permission check for ${resource}.${action} is async but called synchronously`);
  }
  return result;
}

export function withAuthorization(
  resource: string,
  action: string,
  options: {
    getResource?: (request: NextRequest, params?: any) => Promise<any>;
    getResourceId?: (request: NextRequest, params?: any) => string;
  } = {}
) {
  return function (
    handler: (
      user: AuthenticatedUser,
      request: NextRequest,
      context: any
    ) => Promise<NextResponse> | NextResponse
  ) {
    return async (
      user: AuthenticatedUser,
      request: NextRequest,
      context: any = {}
    ) => {
      try {
        let resourceData: any | undefined;
        let resourceId: string | undefined;

        if (options.getResource) {
          resourceData = await options.getResource(request, context.params);
        }
        if (options.getResourceId) {
          resourceId = options.getResourceId(request, context.params);
        }

        const allowed = await hasPermission(user, resource, action, {
          resource: resourceData,
          resourceId,
        });
        if (!allowed) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        return handler(user, request, context);
      } catch (error) {
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

export function createAuthorizationErrorResponse(message: string = 'Access denied'): NextResponse {
  return NextResponse.json({ error: message }, { status: 403 });
}

export function createAuthenticationErrorResponse(message: string = 'Authentication required'): NextResponse {
  return NextResponse.json({ error: message }, { status: 401 });
}
