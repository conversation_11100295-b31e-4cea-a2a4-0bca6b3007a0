'use client';

export const dynamic = 'force-dynamic';

import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useApiQuery } from '../../../../hooks/use-api';
import { useRouter } from 'next/navigation';
import { apiService } from '../../../../lib/services/api';
import { usePerformanceMonitor } from '../../../../components/performance-monitor';
import { logComponentError, logError, classifyError } from '../../../../lib/error-handler';
import { Card, CardContent, CardHeader, CardTitle } from "../../../../components/ui/card";
import { Button } from "../../../../components/ui/button";
import { Skeleton } from "../../../../components/ui/skeleton";
import { Alert, AlertDescription } from "../../../../components/ui/alert";
import { Badge } from "../../../../components/ui/badge";
import { Progress } from "../../../../components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../../../components/ui/tabs";
import { Briefcase, Calendar,
  Users, TrendingUp, AlertCircle, RefreshCw, Crown, Target, Sparkles, Zap, Settings, FileText, 
  CheckCircle, XCircle, Clock, Building2, Activity, BarChart3, PieChart, ArrowUpRight,
  ArrowDownRight, Plus, Eye,  Filter, Search, MoreHorizontal, ChevronRight, Gauge,
  Shield, AlertTriangle, CheckCircle2, Timer, MapPin, Phone, Mail, 
  Calendar as CalendarIcon, TrendingDown, Bug, Home } from "lucide-react";
import { CompanyAvatar } from "../../../../components/CompanyAvatar";
import { Enhanced3DStatusBadge, EnhancedDateStatusIndicator } from '../../../../components/enhanced-date-status-indicators';
import { ActionItemsInbox } from '../../../../components/admin/ActionItemsInbox';
import { getShiftStatus, getShiftStatusDisplay } from '../../../../lib/shift-status';
import { Job, Shift, Company, ShiftWithDetails } from '../../../../lib/types';
import { format, isToday, isTomorrow, isYesterday, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { cn } from '../../../../lib/utils';
import { getTotalRequiredWorkers, getAssignedWorkerCount } from '../../../../lib/worker-count-utils';

// Safe date formatting helper with enhanced validation
const safeFormatDate = (dateValue: any, formatString: string = 'MMM d, h:mm a'): string => {
  try {
    // Handle null, undefined, empty string
    if (!dateValue || dateValue === '' || dateValue === 'null' || dateValue === 'undefined') {
      console.warn('🗓️ [AdminDashboard] No date value provided for formatting:', { dateValue, type: typeof dateValue });
      return 'No date';
    }

    // Handle already formatted strings that might be invalid
    if (typeof dateValue === 'string' && (dateValue.includes('Invalid') || dateValue.includes('NaN'))) {
      console.warn('🗓️ [AdminDashboard] Date value appears to be already invalid:', dateValue);
      return 'Invalid date';
    }

    const date = new Date(dateValue);
    
    // Check if the date is valid
    if (isNaN(date.getTime()) || !isFinite(date.getTime())) {
      console.error('📅 [AdminDashboard] Invalid date value:', {
        dateValue,
        type: typeof dateValue,
        dateTime: date.getTime(),
        isNaN: isNaN(date.getTime()),
        isFinite: isFinite(date.getTime())
      });
      logComponentError('AdminDashboard', new Error('Invalid date value'), { dateValue, formatString });
      return 'Invalid date';
    }

    // Additional validation for reasonable date ranges
    const year = date.getFullYear();
    if (year < 2010 || year > 2100) {
      console.warn('⚠️ [AdminDashboard] The Year value is out of bounds', 
        { dateValue, year });
      return 'Date out of range';
    }

    const formatted = format(date, formatString);
    console.log('✅ [AdminDashboard] Successfully formatted date:', 
      { input: dateValue, output: formatted });
    return formatted;
  } catch (error) {
    console.error('🔥 [AdminDashboard] Error formatting date:', error, 
      { dateValue, formatString });
    logComponentError('AdminDashboard', error, 
      { dateValue, formatString });
    return 'Date error';
  }
};

// Helper function to get shift display name
const getShiftDisplayName = (shift: any) => {
  try {
    if (shift.description && shift.description.trim()) {
      return shift.description.trim();
    }
    return shift.job?.name || 'Unknown Job';
  } catch (error) {
    console.error('🔥 [AdminDashboard] Error getting shift display name:', error);
    logComponentError('AdminDashboard', error, { shift });
    return 'Unknown Job';
  }
};

// Mobile-first metric card component
const MetricCard = ({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  trend, 
  trendValue, 
  color = 'blue',
  onClick 
}: {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: any;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: 'blue' | 'green' | 'orange' | 'red' | 'purple' | 'gray';
  onClick?: () => void;
}) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 bg-blue-50 dark:bg-blue-950/20 text-blue-600 dark:text-blue-400',
    green: 'from-green-500 to-green-600 bg-green-50 dark:bg-green-950/20 text-green-600 dark:text-green-400',
    orange: 'from-orange-500 to-orange-600 bg-orange-50 dark:bg-orange-950/20 text-orange-600 dark:text-orange-400',
    red: 'from-red-500 to-red-600 bg-red-50 dark:bg-red-950/20 text-red-600 dark:text-red-400',
    purple: 'from-purple-500 to-purple-600 bg-purple-50 dark:bg-purple-950/20 text-purple-600 dark:text-purple-400',
    gray: 'from-gray-500 to-gray-600 bg-gray-50 dark:bg-gray-950/20 text-gray-600 dark:text-gray-400'
  };

  const trendIcon = trend === 'up' ? ArrowUpRight : trend === 'down' ? ArrowDownRight : null;
  const trendColor = trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600';

  return (
    <Card 
      className={cn(
        "relative overflow-hidden transition-all duration-200 hover:shadow-lg cursor-pointer",
        "border-0 shadow-sm bg-white dark:bg-gray-900",
        onClick && "hover:scale-[1.02]"
      )}
      onClick={onClick}
    >
      <div className={cn("absolute top-0 right-0 w-20 h-20 rounded-full -translate-y-10 translate-x-10 opacity-10", colorClasses[color].split(' ')[2])} />
      
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <div className={cn("p-2 rounded-lg", colorClasses[color].split(' ')[2])}>
          <Icon className="h-4 w-4" />
        </div>
      </CardHeader>
      
      <CardContent className="relative z-10">
        <div className="text-2xl font-bold mb-1">{value}</div>
        <div className="flex items-center justify-between">
          {subtitle && (
            <p className="text-xs text-muted-foreground">{subtitle}</p>
          )}
          {trend && trendValue && (
            <div className={cn("flex items-center text-xs font-medium", trendColor)}>
              {trendIcon && React.createElement(trendIcon, { className: "h-3 w-3 mr-1" })}
              {trendValue}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Activity item component with enhanced error handling
const ActivityItem = ({ activity }: { activity: any }) => {
  try {
    console.log('🎯 [ActivityItem] Rendering activity:', {
      id: activity.id,
      type: activity.type,
      title: activity.title,
      time: activity.time,
      timeType: typeof activity.time
    });

    const Icon = activity.icon;
    const colorClasses = {
      blue: 'bg-blue-100 text-blue-600 dark:bg-blue-950/20 dark:text-blue-400',
      green: 'bg-green-100 text-green-600 dark:bg-green-950/20 dark:text-green-400',
      purple: 'bg-purple-100 text-purple-600 dark:bg-purple-950/20 dark:text-purple-400',
      orange: 'bg-orange-100 text-orange-600 dark:bg-orange-950/20 dark:text-orange-400'
    };

    // Use safe date formatting
    const formattedDate = safeFormatDate(activity.time);

    return (
      <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
        <div className={cn("p-2 rounded-full flex-shrink-0", colorClasses[activity.color as keyof typeof colorClasses])}>
          <Icon className="h-4 w-4" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-foreground truncate">{activity.title}</p>
          <p className="text-xs text-muted-foreground">{activity.subtitle}</p>
          <p className="text-xs text-muted-foreground mt-1">
            {formattedDate}
          </p>
        </div>
      </div>
    );
  } catch (error) {
    console.error('🔥 [ActivityItem] Error rendering activity:', error);
    logComponentError('ActivityItem', error, { activity });
    
    // Fallback UI for error case
    return (
      <div className="flex items-start space-x-3 p-3 rounded-lg bg-red-50 dark:bg-red-950/20">
        <div className="p-2 rounded-full flex-shrink-0 bg-red-100 text-red-600">
          <AlertTriangle className="h-4 w-4" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-red-800 dark:text-red-200">Error displaying activity</p>
          <p className="text-xs text-red-600 dark:text-red-400">Failed to render activity item</p>
        </div>
      </div>
    );
  }
};

export default function AdminDashboard() {
  const router = useRouter();
  const { toggle } = usePerformanceMonitor();
  const [activeTab, setActiveTab] = useState('overview');
  
  const { 
    data: dashboardData, 
    isLoading, 
    error,
    refetch 
  } = useApiQuery(['adminDashboard'], apiService.getAdminDashboard);

  const { metrics, recentActivity, upcomingShifts } = useMemo(() => {
    if (!dashboardData) {
      return {
        metrics: {
          jobs: { total: 0, active: 0, completed: 0 },
          shifts: { today: 0, tomorrow: 0, thisWeek: 0, active: 0, understaffed: 0 },
          workforce: { total: 0, active: 0 },
          fulfillment: { rate: 0, understaffed: 0 },
        },
        recentActivity: [],
        upcomingShifts: [],
      };
    }
    return {
      metrics: dashboardData.metrics || {
        jobs: { total: 0, active: 0, completed: 0 },
        shifts: { today: 0, tomorrow: 0, thisWeek: 0, active: 0, understaffed: 0 },
        workforce: { total: 0, active: 0 },
        fulfillment: { rate: 0, understaffed: 0 },
      },
      recentActivity: dashboardData.recentActivity || [],
      upcomingShifts: dashboardData.upcomingShifts || [],
    };
  }, [dashboardData]);

  const handleJobClick = (jobId: string) => {
    router.push(`/jobs/${jobId}`);
  };

  const handleShiftClick = useCallback((shiftId: string) => {
    router.push(`/shifts/${shiftId}`);
  }, [router]);

  const handleRefresh = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="p-4">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </Card>
            ))}
          </div>
          
          {/* Content Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Skeleton className="h-96 w-full" />
            </div>
            <div>
              <Skeleton className="h-96 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    console.error('🔥 [AdminDashboard] Data loading error:', error);
    logComponentError('AdminDashboard', error, {});

    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load dashboard data. 
              <Button variant="link" onClick={handleRefresh} className="p-0 ml-2">
                <RefreshCw className="h-4 w-4 mr-1" />
                Try again
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  try {
    console.log('🎨 [AdminDashboard] Rendering dashboard with data:', dashboardData);

    return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                    <Crown className="h-6 w-6 text-white" />
                  </div>
                  <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Admin Dashboard
                  </h1>
                </div>
                <p className="text-muted-foreground">
                  Monitor and manage your workforce operations
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggle}
                  className="flex items-center gap-2"
                >
                  <Gauge className="h-4 w-4" />
                  <span className="hidden sm:inline">Performance</span>
                </Button>
                <Button
                  size="sm"
                  onClick={() => router.push('/admin/settings')}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  <span className="hidden sm:inline">Settings</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Key Metrics Grid - Mobile First */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <MetricCard
              title="Total Jobs"
              value={metrics.jobs.total}
              subtitle="All projects"
              icon={Briefcase}
              color="blue"
              onClick={() => router.push('/jobs')}
            />
            <MetricCard
              title="Today's Shifts"
              value={metrics.shifts.today}
              subtitle="Scheduled today"
              icon={Calendar}
              color="green"
              onClick={() => router.push('/shifts')}
            />
            <MetricCard
              title="Active Workers"
              value={metrics.workforce.active}
              subtitle={`of ${metrics.workforce.total} total`}
              icon={Users}
              color="purple"
              onClick={() => router.push('/employees')}
            />
            <MetricCard
              title="Fulfillment"
              value={`${Math.round(metrics.fulfillment.rate)}%`}
              subtitle="Shift coverage"
              icon={Target}
              trend={metrics.fulfillment.rate >= 90 ? 'up' : 'down'}
              trendValue={`${metrics.fulfillment.understaffed} understaffed`}
              color={metrics.fulfillment.rate >= 90 ? 'green' : 'orange'}
            />
          </div>

          {/* Secondary Metrics - Hidden on mobile, shown on larger screens */}
          <div className="hidden lg:grid lg:grid-cols-3 gap-4 mb-8">
            <MetricCard
              title="Tomorrow"
              value={metrics.shifts.tomorrow}
              subtitle="Shifts scheduled"
              icon={CalendarIcon}
              color="blue"
            />
            <MetricCard
              title="This Week"
              value={metrics.shifts.thisWeek}
              subtitle="Total shifts"
              icon={Activity}
              color="green"
            />
            <MetricCard
              title="Understaffed"
              value={metrics.fulfillment.understaffed}
              subtitle="Shifts need workers"
              icon={AlertTriangle}
              color={metrics.fulfillment.understaffed > 0 ? 'red' : 'gray'}
            />
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-4">
              <TabsTrigger value="overview" className="text-xs sm:text-sm">Overview</TabsTrigger>
              <TabsTrigger value="shifts" className="text-xs sm:text-sm">Shifts</TabsTrigger>
              <TabsTrigger value="activity" className="text-xs sm:text-sm">Activity</TabsTrigger>
              <TabsTrigger value="analytics" className="hidden lg:block text-xs sm:text-sm">Analytics</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Upcoming Shifts */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Upcoming Shifts
                      </CardTitle>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => router.push('/shifts')}
                      >
                        View All
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {upcomingShifts.length > 0 ? (
                        <div className="space-y-3">
                          {upcomingShifts.map((shift: any) => (
                            <div 
                              key={shift.id}
                              className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors"
                              onClick={() => handleShiftClick(shift.id)}
                            >
                              <div className="flex items-center space-x-3 flex-1 min-w-0">
                                <div className="flex-shrink-0">
                                  <CompanyAvatar
                                    src={shift.job?.company?.company_logo_url}
                                    name={shift.job?.company?.name || ''}
                                    className="w-8 h-8"
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">
                                    {getShiftDisplayName(shift)}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    {format(new Date(shift.startTime), 'MMM d, h:mm a')}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2 flex-shrink-0">
                                <Enhanced3DStatusBadge status={shift.status} size="sm" />
                                <ChevronRight className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No upcoming shifts</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Action Items Inbox */}
                <div>
                  <ActionItemsInbox />
                </div>
              </div>
            </TabsContent>

            {/* Shifts Tab */}
            <TabsContent value="shifts" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <MetricCard
                  title="Active Shifts"
                  value={metrics.shifts.active}
                  subtitle="Currently running"
                  icon={Activity}
                  color="green"
                />
                <MetricCard
                  title="Understaffed"
                  value={metrics.shifts.understaffed}
                  subtitle="Need more workers"
                  icon={AlertTriangle}
                  color={metrics.shifts.understaffed > 0 ? 'red' : 'gray'}
                />
                <MetricCard
                  title="This Week"
                  value={metrics.shifts.thisWeek}
                  subtitle="Total scheduled"
                  icon={CalendarIcon}
                  color="blue"
                />
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Shift Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <Button 
                      className="h-20 flex-col gap-2"
                      onClick={() => router.push('/shifts')}
                    >
                      <Eye className="h-5 w-5" />
                      View All Shifts
                    </Button>
                    <Button 
                      variant="outline"
                      className="h-20 flex-col gap-2"
                      onClick={() => router.push('/admin/shifts/new')}
                    >
                      <Plus className="h-5 w-5" />
                      Create Shift
                    </Button>
                    <Button 
                      variant="outline"
                      className="h-20 flex-col gap-2"
                      onClick={() => router.push('/shifts?status=understaffed')}
                    >
                      <AlertTriangle className="h-5 w-5" />
                      Understaffed
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Activity Tab */}
            <TabsContent value="activity" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {recentActivity.length > 0 ? (
                    <div className="space-y-1">
                      {recentActivity.map((activity) => (
                        <ActivityItem key={activity.id} activity={activity} />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No recent activity</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Analytics Tab - Desktop Only */}
            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Performance Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Shift Fulfillment</span>
                          <span>{Math.round(metrics.fulfillment.rate)}%</span>
                        </div>
                        <Progress value={metrics.fulfillment.rate} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Worker Utilization</span>
                          <span>{Math.round((metrics.workforce.active / metrics.workforce.total) * 100)}%</span>
                        </div>
                        <Progress value={(metrics.workforce.active / metrics.workforce.total) * 100} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Job Completion</span>
                          <span>{Math.round((metrics.jobs.completed / metrics.jobs.total) * 100)}%</span>
                        </div>
                        <Progress value={(metrics.jobs.completed / metrics.jobs.total) * 100} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <PieChart className="h-5 w-5" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      <Button 
                        variant="outline" 
                        className="h-16 flex-col gap-1"
                        onClick={() => router.push('/jobs/new')}
                      >
                        <Plus className="h-4 w-4" />
                        <span className="text-xs">New Job</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-16 flex-col gap-1"
                        onClick={() => router.push('/employees/new')}
                      >
                        <Users className="h-4 w-4" />
                        <span className="text-xs">Add Worker</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-16 flex-col gap-1"
                        onClick={() => router.push('/companies/new')}
                      >
                        <Building2 className="h-4 w-4" />
                        <span className="text-xs">New Company</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-16 flex-col gap-1"
                        onClick={() => router.push('/timesheets')}
                      >
                        <FileText className="h-4 w-4" />
                        <span className="text-xs">Timesheets</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
    );
  } catch (error) {
    console.error('🔥 [AdminDashboard] Critical error rendering dashboard:', error);
    logComponentError('AdminDashboard', error, {
      metrics,
      recentActivityLength: recentActivity?.length,
      upcomingShiftsLength: upcomingShifts?.length,
    });

    // Critical error fallback UI
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <Alert className="mb-6" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div>
                <p className="font-medium">Dashboard Error</p>
                <p className="text-sm mt-1">
                  A critical error occurred while rendering the dashboard. Please refresh the page or contact support if the problem persists.
                </p>
                <div className="flex gap-2 mt-4">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.location.reload()}
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh Page
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push('/')}
                  >
                    <Home className="h-4 w-4 mr-1" />
                    Go Home
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Show error details in development */}
          {process.env.NODE_ENV === 'development' && (
            <Alert className="mb-6">
              <Bug className="h-4 w-4" />
              <AlertDescription>
                <p className="font-medium">Development Error Details:</p>
                <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {error?.message}
                </pre>
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    );
  }
}
