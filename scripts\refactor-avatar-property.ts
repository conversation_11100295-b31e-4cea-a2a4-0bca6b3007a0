import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const directoryToSearch = path.join(__dirname, '../src');
const oldText = 'avatarUrl';
const newText = 'avatarUrl';

// List of file extensions to check
const fileExtensions = ['.ts', '.tsx', '.js', '.jsx'];

function replaceInFile(filePath: string) {
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error(`Error reading file ${filePath}:`, err);
      return;
    }

    // Using a regex to replace all occurrences of the old text
    const regex = new RegExp(oldText, 'g');
    if (regex.test(data)) {
      const updatedData = data.replace(regex, newText);

      fs.writeFile(filePath, updatedData, 'utf8', (err) => {
        if (err) {
          console.error(`Error writing to file ${filePath}:`, err);
        } else {
          console.log(`Replaced "${oldText}" with "${newText}" in ${filePath}`);
        }
      });
    }
  });
}

function traverseDirectory(directory: string) {
  fs.readdir(directory, { withFileTypes: true }, (err, files) => {
    if (err) {
      console.error(`Error reading directory ${directory}:`, err);
      return;
    }

    files.forEach((file) => {
      const fullPath = path.join(directory, file.name);
      if (file.isDirectory()) {
        traverseDirectory(fullPath);
      } else if (fileExtensions.includes(path.extname(fullPath))) {
        replaceInFile(fullPath);
      }
    });
  });
}

console.log(`Starting replacement of "${oldText}" with "${newText}" in ${directoryToSearch}`);
traverseDirectory(directoryToSearch);
