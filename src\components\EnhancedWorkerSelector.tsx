import React, { useState, useMemo, useRef, useEffect } from 'react';
import { User } from '@/lib/types';
import { ChevronDown, Search, X, Filter, UserCheck, MapPin, Award, Users } from "lucide-react";
import { Avatar } from './Avatar';
import { UP_FOR_GRABS_ID, HIGHLIGHTED_UNASSIGNED, HIGHLIGHTED_UP_FOR_GRABS } from './worker-selector-utils';
import { useWorkerFilter } from '../hooks/use-worker-filter';
import { FilterPanel } from './FilterPanel';
import { UserListItem } from './UserListItem';

interface EnhancedWorkerSelectorProps {
  users: User[];
  selectedUserId: string | null;
  onChange: (userId: string | null) => void;
  disabled?: boolean;
  showQuestionMark?: boolean;
  requiredRole?: string; // To filter based on shift requirements
  isUpdating?: boolean; // New prop for optimistic updates
}

interface FilterState {
  searchText: string;
  activeOnly: boolean;
  crewChiefEligible: boolean;
  forkOperatorEligible: boolean;
  hasCertifications: boolean;
  location: string;
}

const EnhancedWorkerSelector: React.FC<EnhancedWorkerSelectorProps> = ({
  users,
  selectedUserId,
  onChange,
  disabled,
  showQuestionMark,
  requiredRole,
  isUpdating // New prop
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [filters, setFilters] = useState<FilterState>({
    searchText: '',
    activeOnly: true,
    crewChiefEligible: false,
    forkOperatorEligible: false,
    hasCertifications: false,
    location: ''
  });

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectedUser = selectedUserId ? users.find(u => u.id === selectedUserId) : null;
  const isUpForGrabs = selectedUserId === UP_FOR_GRABS_ID;

  // Get unique locations for filter dropdown
  const availableLocations = useMemo(() => {
    const locations = users
      .map(user => user.location)
      .filter((location): location is string => !!location)
      .filter((location, index, arr) => arr.indexOf(location) === index)
      .sort();
    return locations;
  }, [users]);

  const filteredUsers = useWorkerFilter(users, filters, requiredRole);

  // Reset highlighted index when filtered users change
  useEffect(() => {
    setHighlightedIndex(-1);
  }, [filteredUsers.length]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setShowFilters(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
      setHighlightedIndex(-1);
    }
  }, [isOpen]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev < filteredUsers.length ? prev + 1 : HIGHLIGHTED_UNASSIGNED
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev > HIGHLIGHTED_UNASSIGNED ? prev - 1 : filteredUsers.length
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (highlightedIndex === HIGHLIGHTED_UP_FOR_GRABS) {
            handleSelect({ id: UP_FOR_GRABS_ID, name: 'Up For Grabs' } as any);
          } else if (highlightedIndex === HIGHLIGHTED_UNASSIGNED) {
            handleSelect(null);
          } else if (highlightedIndex >= 0 && highlightedIndex < filteredUsers.length) {
            handleSelect(filteredUsers[highlightedIndex]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          setIsOpen(false);
          setShowFilters(false);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, highlightedIndex, filteredUsers]);

  const handleSelect = (user: User | null) => {
    onChange(user ? user.id : null);
    setIsOpen(false);
    setShowFilters(false);
    setHighlightedIndex(-1);
    setFilters(prev => ({ ...prev, searchText: '' }));
  };

  const clearFilters = () => {
    setFilters({
      searchText: '',
      activeOnly: true,
      crewChiefEligible: false,
      forkOperatorEligible: false,
      hasCertifications: false,
      location: ''
    });
  };

  const hasActiveFilters = useMemo(() => {
    return (
      filters.searchText !== '' ||
      !filters.activeOnly ||
      filters.crewChiefEligible ||
      filters.forkOperatorEligible ||
      filters.hasCertifications ||
      filters.location !== ''
    );
  }, [filters]);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        disabled={disabled || isUpdating} // Disable button during update
        onClick={() => setIsOpen(!isOpen)}
        className={`relative w-full flex items-center justify-between rounded-md border border-gray-600 bg-gray-900/50 px-3 py-2 text-left text-white shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 disabled:cursor-not-allowed disabled:opacity-50 ${isUpdating ? 'pulse-animation' : ''}`}
      >
        {isUpForGrabs ? (
            <div className="flex items-center gap-2 flex-1 min-w-0">
                <Users className="w-6 h-6 flex-shrink-0 text-yellow-400" />
                <span className="truncate font-semibold text-yellow-400">Up For Grabs</span>
            </div>
        ) : selectedUser ? (
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Avatar name={selectedUser.name} userId={selectedUser.id} size="sm" enableSmartCaching={true} className="w-6 h-6 flex-shrink-0" />
            <span className="truncate">{selectedUser.name}</span>
            {!selectedUser.isActive && (
              <span className="flex-shrink-0 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Inactive
              </span>
            )}
          </div>
        ) : (
          <div className="flex items-center">
            {showQuestionMark && <span className="mr-2 text-gray-400">?</span>}
            <span className="text-gray-400">-- Unassigned --</span>
          </div>
        )}
        <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform flex-shrink-0 ml-2 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div 
          className="absolute top-full left-0 mt-1 bg-gray-800 border border-gray-600 rounded-md shadow-lg z-50"
          style={{ width: '320px' }} // Reduced from default auto width
        >
          {/* Search and Filter Header */}
          <div className="p-3 border-b border-gray-700">
            <div className="relative mb-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search by name, email, or certification..."
                value={filters.searchText}
                onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
                className="w-full pl-10 pr-4 py-2 text-sm bg-gray-900 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              />
            </div>
            
            <FilterPanel
              filters={filters}
              setFilters={setFilters}
              availableLocations={availableLocations}
              showFilters={showFilters}
              setShowFilters={setShowFilters}
              hasActiveFilters={hasActiveFilters}
              clearFilters={clearFilters}
            />
          </div>

          {/* Results */}
          <div className="flex-1 overflow-auto" style={{maxHeight: '400px'}}>
            <ul className="py-1">
              <li
                onClick={() => handleSelect({ id: UP_FOR_GRABS_ID, name: 'Up For Grabs' } as any)}
                className={`relative cursor-pointer select-none py-2 px-3 text-yellow-400 hover:bg-gray-700 flex items-center gap-2 ${
                  highlightedIndex === HIGHLIGHTED_UP_FOR_GRABS ? 'bg-indigo-600' : ''
                }`}
              >
                <div className="w-6 h-6 rounded-full bg-yellow-900/50 flex items-center justify-center">
                  <Users className="w-4 h-4" />
                </div>
                Up For Grabs
              </li>
              <li
                onClick={() => handleSelect(null)}
                className={`relative cursor-pointer select-none py-2 px-3 text-gray-300 hover:bg-gray-700 flex items-center gap-2 ${
                  highlightedIndex === HIGHLIGHTED_UNASSIGNED ? 'bg-indigo-600' : ''
                }`}
              >
                <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center">
                  <X className="w-4 h-4" />
                </div>
                -- Unassigned --
              </li>
              
              {filteredUsers.length === 0 ? (
                <li className="py-8 px-3 text-center text-gray-400">
                  <div className="space-y-2">
                    <Search className="w-8 h-8 mx-auto opacity-50" />
                    <p>No users found</p>
                    <p className="text-xs">Try adjusting your search or filters</p>
                  </div>
                </li>
              ) : (
                filteredUsers.map((user, index) => (
                  <UserListItem
                    key={user.id}
                    user={user}
                    onSelect={handleSelect}
                    isSelected={selectedUserId === user.id}
                    isHighlighted={index === highlightedIndex}
                  />
                ))
              )}
            </ul>
          </div>

          {/* Footer with count */}
          <div className="p-2 border-t border-gray-700 bg-gray-900 text-xs text-gray-400 text-center">
            {filteredUsers.length} of {users.length} users
            {hasActiveFilters && ' (filtered)'}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedWorkerSelector;
