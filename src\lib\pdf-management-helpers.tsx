import React from 'react';
import { CheckCircle, AlertTriangle } from 'lucide-react';

export const ACTION_NAMES = {
  CLEANUP_CACHE: 'cleanup-cache',
  CLEAR_CACHE: 'clear-cache',
  OPTIMIZE_SYSTEM: 'optimize-system',
  CLEAR_METRICS: 'clear-metrics',
};

export const getHealthColor = (status: string) => {
  switch (status) {
    case 'healthy': return 'text-green-600';
    case 'warning': return 'text-yellow-600';
    case 'critical': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export const getHealthIcon = (status: string) => {
  switch (status) {
    case 'healthy': return <CheckCircle className="h-5 w-5 text-green-600" />;
    case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    case 'critical': return <AlertTriangle className="h-5 w-5 text-red-600" />;
    default: return <AlertTriangle className="h-5 w-5 text-gray-600" />;
  }
};
