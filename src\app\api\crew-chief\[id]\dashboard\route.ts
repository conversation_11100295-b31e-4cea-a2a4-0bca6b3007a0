import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAuthenticatedUser } from '@/lib/auth-server';
import { UserRole } from '@/lib/types';

export async function GET(req: Request, { params }: { params: { id: string } }) {
  const { id } = params;

  if (!id || typeof id !== 'string') {
    return NextResponse.json({ error: 'Crew Chief ID is required' }, { status: 400 });
  }

  try {
    const user = await getAuthenticatedUser();
    if (!user || (user.role !== UserRole.Admin && user.id !== id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    const activeShifts = await prisma.shift.findMany({
      where: {
        assignedPersonnel: {
          some: {
            userId: id,
            roleCode: 'CC',
          },
        },
        status: {
          in: ['InProgress', 'Active', 'Pending'],
        },
      },
      include: {
        job: {
          include: {
            company: true,
          },
        },
        assignedPersonnel: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: {
        date: 'asc',
      },
    });

    return NextResponse.json({
      activeShifts,
    });
  } catch (error) {
    console.error(`Error fetching crew chief dashboard data for ${id}:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
