#!/usr/bin/env node

/**
 * Optimized startup script for Next.js standalone builds in Docker containers
 * This version provides better error handling and debugging information
 */

import { spawn } from 'child_process';
import { readdir, access, stat } from 'fs/promises';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting HoliTime application (Optimized)...');

async function checkFileExists(filePath) {
  try {
    const stats = await stat(filePath);
    return stats.isFile();
  } catch (error) {
    return false;
  }
}

async function listDirectoryContents(dir = '.') {
  try {
    const files = await readdir(dir);
    console.log(`📂 Contents of ${dir}:`, files.slice(0, 15).join(', '));
    if (files.length > 15) {
      console.log(`   ... and ${files.length - 15} more files`);
    }
  } catch (error) {
    console.log(`⚠️  Could not list contents of ${dir}:`, error.message);
  }
}

async function findServerFile() {
  // Check common locations for the Next.js server file
  const possiblePaths = [
    './server.js',
    './.next/standalone/server.js',
    './standalone/server.js'
  ];

  for (const path of possiblePaths) {
    if (await checkFileExists(path)) {
      console.log(`✅ Found server file at: ${path}`);
      return path;
    }
  }

  return null;
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`📋 Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    const port = process.env.PORT || '8080';
    
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🚪 Port: ${port}`);
    console.log(`📁 Working directory: ${process.cwd()}`);
    console.log(`📋 Process ID: ${process.pid}`);
    
    // Debug: List directory contents
    await listDirectoryContents('.');
    
    // Check if .next directory exists
    if (await checkFileExists('./.next')) {
      await listDirectoryContents('./.next');
    }

    // Find the server file
    const serverFile = await findServerFile();
    
    if (!serverFile) {
      console.log('❌ No server.js file found in expected locations');
      console.log('🔍 Expected locations:');
      console.log('   - ./server.js (standalone build)');
      console.log('   - ./.next/standalone/server.js');
      console.log('   - ./standalone/server.js');
      
      // Try to provide helpful debugging info
      console.log('\n🔧 Debugging information:');
      await listDirectoryContents('./.next');
      
      process.exit(1);
    }

    if (isProduction) {
      console.log('🔄 Production environment detected');
      console.log('✅ Database migrations will be handled by the deployment process');
    }
    
    // Start the Next.js application
    console.log(`🎉 Starting Next.js server from: ${serverFile}`);
    
    // Start the server process
    const serverProcess = spawn('node', [serverFile], {
      stdio: 'inherit',
      env: {
        ...process.env,
        PORT: port,
        HOSTNAME: '0.0.0.0'
      }
    });

    // Handle server process events
    serverProcess.on('error', (error) => {
      console.error('❌ Server failed to start:', error);
      process.exit(1);
    });

    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
      process.exit(code || 0);
    });

    // Handle graceful shutdown
    const shutdown = (signal) => {
      console.log(`Received ${signal}, shutting down gracefully...`);
      serverProcess.kill(signal);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    
    console.log('✅ Server startup completed successfully');
    
  } catch (error) {
    console.error('❌ Startup failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

main();