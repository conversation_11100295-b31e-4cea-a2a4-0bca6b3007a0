"use client";

import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format, addDays, startOfWeek, isSameDay, parseISO } from 'date-fns';
import { formatPacificTime, formatPacificTimeRange, utcToPacificTime } from '@/lib/time-utils';
import { ChevronLeft, ChevronRight, Calendar, Users, Clock, MapPin } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import UnifiedEnhancedTimeTracking from '@/components/unified-enhanced-time-tracking';
import { apiService } from '@/lib/services/api';
import { ShiftWithDetails, Assignment, User } from '@/lib/types';
import { useToast } from "@/hooks/use-toast";
import { useQuery } from '@tanstack/react-query';
import { WORKER_TYPES, LEGACY_WORKER_TYPES } from '@/constants/worker-types';
import '@/styles/job-timeline.css';

// Define a type for shifts with an optional lane property
interface ShiftWithLane extends Record<string, any> {
  id: string;
  startTime: string;
  endTime: string;
  lane?: number; // Optional lane property
}

interface VisualJobTimelineProps {
  job: any;
  shifts: ShiftWithLane[];
  onRefresh: () => void;
}

/**
 * Assigns a lane to each shift to prevent visual overlaps.
 * Shifts are sorted by start time, and a greedy algorithm is used to place them
 * in the lowest available lane.
 * @param shifts An array of shift objects for a given day.
 * @returns The array of shifts with an assigned 'lane' property.
 */
function assignLanes(shifts: ShiftWithLane[]): ShiftWithLane[] {
  // Sort shifts by their start time to process them in chronological order
  const sortedShifts = [...shifts].sort((a, b) => {
    const startTimeA = new Date(a.startTime).getTime();
    const startTimeB = new Date(b.startTime).getTime();
    return startTimeA - startTimeB;
  });

  // Keep track of the end time of the last shift in each lane
  // Map: laneIndex -> endTime (timestamp)
  const laneEnds = new Map<number, number>();

  sortedShifts.forEach(shift => {
    const currentShiftStartTime = new Date(shift.startTime).getTime();
    const currentShiftEndTime = new Date(shift.endTime).getTime();
    let assigned = false;

    // Try to place the shift in the lowest available lane
    for (let lane = 0; ; lane++) {
      const lastShiftEndTimeInLane = laneEnds.get(lane);

      // If no shift in this lane yet, or current shift starts after the last one ends in this lane
      if (lastShiftEndTimeInLane === undefined || currentShiftStartTime >= lastShiftEndTimeInLane) {
        shift.lane = lane;
        laneEnds.set(lane, currentShiftEndTime);
        assigned = true;
        break;
      }
    }
  });

  return sortedShifts;
}

// Color palette for crew chiefs
const CREW_CHIEF_COLORS = [
  { bg: '#ef4444', light: '#fecaca', name: 'Red' },
  { bg: '#3b82f6', light: '#bfdbfe', name: 'Blue' },
  { bg: '#10b981', light: '#a7f3d0', name: 'Green' },
  { bg: '#f59e0b', light: '#fed7aa', name: 'Orange' },
  { bg: '#8b5cf6', light: '#c4b5fd', name: 'Purple' },
  { bg: '#06b6d4', light: '#a5f3fc', name: 'Cyan' },
  { bg: '#84cc16', light: '#d9f99d', name: 'Lime' },
  { bg: '#f97316', light: '#fed7aa', name: 'Orange' },
];

// Generate colors for different worker types from unified constants
const WORKER_TYPE_COLORS = Object.fromEntries(
  Object.entries(WORKER_TYPES).map(([roleCode, config]) => [
    roleCode,
    {
      bg: config.color,
      light: config.color + '33', // Adding alpha transparency for light version
      name: config.label,
      icon: getWorkerTypeIcon(roleCode)
    }
  ])
);

const DEFAULT_WORKER_COLOR = { bg: '#6b7280', light: '#6b728033', name: 'Worker', icon: '👷' };

// Legacy mapping for backward compatibility
const LEGACY_COLORS = Object.fromEntries(
  Object.entries(LEGACY_WORKER_TYPES).map(([legacyKey, config]) => [
    legacyKey,
    WORKER_TYPE_COLORS[config.roleCode] || DEFAULT_WORKER_COLOR
  ])
);

function getWorkerTypeIcon(roleCode: string): string {
  switch (roleCode) {
    case 'CC': return '👑';
    case 'SH': return '🔧';
    case 'FO': return '🚛';
    case 'RFO': return '📦';
    case 'RG': return '⚡';
    case 'GL': return '👷';
    case 'SUP': return '🧑‍✈️';
    default: return '👷';
  }
}

export function VisualJobTimeline({ job, shifts, onRefresh }: VisualJobTimelineProps) {
  console.log(`🎬 [VisualJobTimeline] Component rendered with:`, {
    jobName: job?.name,
    shiftsCount: shifts?.length || 0,
    sampleShift: shifts?.[0] ? {
      id: shifts[0].id,
      startTime: shifts[0].startTime,
      endTime: shifts[0].endTime,
      hasCrewChiefs: !!shifts[0].crewChiefs,
      hasWorkers: !!shifts[0].workers
    } : 'No shifts'
  });

  const { toast } = useToast();
  const handleTimeUpdate = async (entryId: string, field: 'clockIn' | 'clockOut', value: string) => {
    // This is a placeholder. In a real app, you'd call a mutation to update the time entry.
    console.log(`Updating time entry ${entryId}, field ${field} to ${value}`);
    // Example API call:
    // await updateTimeEntryMutation.mutateAsync({ entryId, field, value });
    onRefresh();
  };
  const [viewMode, setViewMode] = useState<'day' | '3days' | 'week' | 'full'>('full');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedShift, setSelectedShift] = useState<ShiftWithLane | null>(null);

  const { data: detailedShift, isLoading: isLoadingShiftDetails, refetch: fetchShiftDetails } = useQuery<ShiftWithDetails>({
    queryKey: ['shiftDetails', selectedShift?.id],
    queryFn: async () => {
      const response = await fetch(`/api/shifts/${selectedShift!.id}?_t=${Date.now()}`, {
        cache: 'no-store',
        headers: { 'Cache-Control': 'no-store' },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch shift details');
      }
      const data = await response.json();
      return data.shift;
    },
    enabled: !!selectedShift,
  });

  const { data: availableUsers = [], isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ['availableUsers', job.companyId],
    queryFn: async () => {
      const response = await fetch(`/api/users?fetchAll=true${job.companyId ? `&companyId=${job.companyId}` : ''}`);
      if (!response.ok) {
        throw new Error('Failed to fetch available users');
      }
      const data = await response.json();
      return data.users;
    },
  });

  const handleAssignmentUpdate = React.useCallback(async (assignmentId: string, userId: string | null, roleCode?: string) => {
    try {
      if (userId && roleCode) {
        // Assign into slot. If assignmentId is a placeholder, backend will just create a new assignment
        await apiService.assignWorker(selectedShift!.id, userId, roleCode, false, assignmentId);
        toast({ title: 'Worker Assigned', description: 'Assignment updated successfully.' });
      } else if (assignmentId && !assignmentId.startsWith('placeholder-')) {
        // Unassign only if it's a real DB assignment
        await apiService.unassignWorker(selectedShift!.id, assignmentId);
        toast({ title: 'Worker Unassigned', description: 'Assignment removed successfully.' });
      }
    } catch (err: any) {
      console.error('Assignment update failed:', err);
      toast({ title: 'Assignment Failed', description: err?.message || 'Please try again.', variant: 'destructive' });
    } finally {
      await fetchShiftDetails();
      onRefresh();
    }
  }, [fetchShiftDetails, onRefresh, selectedShift, toast]);

  const handleRefresh = React.useCallback(() => {
    fetchShiftDetails();
    onRefresh();
  }, [fetchShiftDetails, onRefresh]);

  // Get unique crew chiefs and assign colors
  const crewChiefs = useMemo(() => {
    const chiefs = new Set<string>();
    shifts.forEach(shift => {
      // Use the new data structure: shift.crewChiefs
      if (shift.crewChiefs && Array.isArray(shift.crewChiefs)) {
        shift.crewChiefs.forEach((crewChief: any) => {
          if (crewChief.name) {
            chiefs.add(crewChief.name);
          }
        });
      }
    });
    
    return Array.from(chiefs).map((name, index) => ({
      name,
      color: CREW_CHIEF_COLORS[index % CREW_CHIEF_COLORS.length]
    }));
  }, [shifts]);

  // Calculate date range based on view mode
  const displayDays = useMemo(() => {
    try {
      const jobStartDate = job.startDate ? parseISO(job.startDate) : new Date();
      const jobEndDate = job.endDate ? parseISO(job.endDate) : addDays(jobStartDate, 7);
      
      let startDate: Date;
      let endDate: Date;
      
      switch (viewMode) {
        case 'day':
          startDate = new Date(currentDate);
          endDate = new Date(currentDate);
          break;
        case '3days':
          startDate = new Date(currentDate);
          endDate = addDays(currentDate, 2);
          break;
        case 'week':
          startDate = startOfWeek(currentDate, { weekStartsOn: 1 }); // Start Monday
          endDate = addDays(startDate, 6);
          break;
        case 'full':
        default:
          startDate = jobStartDate;
          endDate = jobEndDate;
          break;
      }
      
      // Ensure we don't go outside job boundaries for non-full views
      if (viewMode !== 'full') {
        startDate = startDate < jobStartDate ? jobStartDate : startDate;
        endDate = endDate > jobEndDate ? jobEndDate : endDate;
      }
      
      const days = [];
      let current = new Date(startDate);
      
      while (current <= endDate) {
        days.push(new Date(current));
        current = addDays(current, 1);
      }
      
      return days;
    } catch {
      return Array.from({ length: 7 }, (_, i) => addDays(new Date(), i));
    }
  }, [job.startDate, job.endDate, viewMode, currentDate]);
  
  // Time slots (6 AM to 11 PM in 1-hour increments)
  const timeSlots = Array.from({ length: 18 }, (_, i) => {
    const hour = 6 + i;
    return {
      hour,
      label: hour <= 12 ? `${hour}${hour === 12 ? 'PM' : 'AM'}` : `${hour - 12}PM`,
      position: (i / 17) * 100 // Percentage position
    };
  });

  // Get crew chief color and name for a shift
  const getCrewChiefInfo = (shift: any) => {
    // Use the new data structure: shift.crewChiefs
    const crewChief = shift.crewChiefs && shift.crewChiefs.length > 0 ? shift.crewChiefs[0] : null;
    if (!crewChief) {
      return { 
        color: CREW_CHIEF_COLORS[0], 
        name: 'Unassigned',
        isAssigned: false
      };
    }
    
    const chief = crewChiefs.find(c => c.name === crewChief.name);
    return { 
      color: chief?.color || CREW_CHIEF_COLORS[0], 
      name: crewChief.name,
      isAssigned: true
    };
  };

  // Calculate staffing details using unified worker types
  const getTotalRequiredWorkers = (shift: any) => {
    return Object.values(WORKER_TYPES).reduce((total, config) => {
      const dbField = config.dbField as keyof typeof shift;
      return total + (typeof shift[dbField] === 'number' ? shift[dbField] : 0);
    }, 0);
  }

  const getAssignedWorkerCount = (shift: any) => {
    return (shift.crewChiefs?.length || 0) + (shift.workers ? Object.values(shift.workers).flat().length : 0);
  }

  const getStaffingDetails = (shift: any) => {
    const workerData = {
      ...shift,
      assignedPersonnel: shift.assignedPersonnel?.map((p: any) => ({ ...p, userId: p.userId || undefined })) || [],
    };
    const required = getTotalRequiredWorkers(workerData);
    const assigned = getAssignedWorkerCount(workerData);
    const filled = Math.min(assigned, required);
    const unfilled = Math.max(0, required - assigned);
    
    return {
      totalRequired: required,
      totalAssigned: assigned,
      filled,
      unfilled,
      percentage: shift.staffingCompletion || (required > 0 ? (assigned / required) * 100 : 0)
    };
  };

  // Create individual worker type segments
  const createWorkerTypeSegments = (shift: any) => {
    const segments: Array<any> = [];
    let currentPosition = 0;
    
    // Use unified worker types structure
    const workerRequirements = Object.entries(WORKER_TYPES).map(([roleCode, config]) => ({
      type: roleCode,
      legacyType: Object.keys(LEGACY_WORKER_TYPES).find(key => LEGACY_WORKER_TYPES[key].roleCode === roleCode),
      required: shift[config.dbField] || 0
    }));
    
    const totalRequired = workerRequirements.reduce((sum, req) => sum + req.required, 0);
    const totalAssigned = shift.totalAssigned || ((shift.crewChiefs?.length || 0) +
                         (shift.workers ? Object.values(shift.workers).flat().length : 0));
    
    if (totalRequired === 0) {
      return [{
        type: 'empty',
        workerType: 'empty',
        color: '#f3f4f6',
        width: 100,
        left: 0,
        filled: false,
        label: 'No workers required'
      }];
    }

    // Count assigned workers by type using unified role codes
    const assignedByType: Record<string, number> = {};
    
    // Count crew chiefs using CC role code
    if (shift.crewChiefs && Array.isArray(shift.crewChiefs)) {
      assignedByType['CC'] = shift.crewChiefs.length;
    }
    
    // Count workers by role code directly (no mapping needed)
    if (shift.workers && typeof shift.workers === 'object') {
      Object.entries(shift.workers).forEach(([roleCode, workersList]: [string, any]) => {
        if (roleCode in WORKER_TYPES && Array.isArray(workersList)) {
          assignedByType[roleCode] = (assignedByType[roleCode] || 0) + workersList.length;
        }
      });
    }

    // Create segments based on requirements
    workerRequirements.forEach(({ type, required }) => {
      if (required > 0) {
        const assigned = Math.min(assignedByType[type] || 0, required);
        const segmentWidth = (required / totalRequired) * 100;
        const workerTypeColor = WORKER_TYPE_COLORS[type as keyof typeof WORKER_TYPE_COLORS] || WORKER_TYPE_COLORS.GL;
        
        // Create segments for this worker type
        for (let i = 0; i < required; i++) {
          const isFilled = i < assigned;
          const individualWidth = segmentWidth / required;
          
          segments.push({
            type: type,
            workerType: type,
            color: isFilled ? workerTypeColor.bg : workerTypeColor.light,
            width: individualWidth,
            left: currentPosition + (i * individualWidth),
            filled: isFilled,
            label: `${workerTypeColor.name} ${i + 1}/${required}${isFilled ? ' (Assigned)' : ' (Open)'}`,
            icon: workerTypeColor.icon
          });
        }
        
        currentPosition += segmentWidth;
      }
    });
    
    return segments;
  };

  // Calculate shift position and width (use Pacific time)
  const getShiftPosition = (shift: any) => {
    try {
      console.log(`📍 [Timeline] Calculating position for shift ${shift.id}:`, {
        startTime: shift.startTime,
        endTime: shift.endTime
      });
      
      const startTime = new Date(shift.startTime);
      const endTime = new Date(shift.endTime);

      // Convert UTC times to Pacific time for proper positioning
      const pacificStartTime = utcToPacificTime(startTime);
      const pacificEndTime = utcToPacificTime(endTime);
      
      const startHour = pacificStartTime.getUTCHours() + (pacificStartTime.getUTCMinutes() / 60);
      const endHour = pacificEndTime.getUTCHours() + (pacificEndTime.getUTCMinutes() / 60);
      
      // Convert to percentage position (6 AM = 0%, 11 PM = 100%)
      const startPercent = Math.max(0, (startHour - 6) / 17 * 100);
      const endPercent = Math.min(100, (endHour - 6) / 17 * 100);
      const width = Math.max(2, endPercent - startPercent); // Minimum 2% width
      
      const position = { left: startPercent, width };
      console.log(`📍 [Timeline] Position calculated:`, position);
      return position;
    } catch (error) {
      console.error(`❌ [Timeline] Error calculating position for shift ${shift.id}:`, error);
      return { left: 0, width: 10 }; // Fallback
    }
  };

  // Group shifts by date using Pacific time for proper date matching
  const shiftsByDate = useMemo(() => {
    console.log(`📅 [Timeline] Grouping ${shifts.length} shifts by date`);
    
    const grouped: Record<string, any[]> = {};
    displayDays.forEach(day => {
      // Build a date key for the display day, converting to Pacific time first
      const pacificDay = utcToPacificTime(day);
      const dateKey = format(pacificDay, 'yyyy-MM-dd');

      // Match shifts by comparing their startTime Pacific date portion
      grouped[dateKey] = shifts.filter(shift => {
        try {
          const shiftStartTime = new Date(shift.startTime);
          const pacificShiftTime = utcToPacificTime(shiftStartTime);
          const shiftKey = format(pacificShiftTime, 'yyyy-MM-dd');
          const matches = shiftKey === dateKey;
          return matches;
        } catch {
          return false;
        }
      });
      
      console.log(`📅 [Timeline] Date ${dateKey}: ${grouped[dateKey].length} shifts`);
    });
    
    return grouped;
  }, [shifts, displayDays]);

  // Calculate display period info
  const periodInfo = useMemo(() => {
    if (displayDays.length === 0) return '';
    const startDate = displayDays[0];
    const endDate = displayDays[displayDays.length - 1];
    const totalDays = displayDays.length;
    
    const viewModeLabels = {
      day: 'Day View',
      '3days': '3-Day View', 
      week: 'Week View',
      full: 'Full Job'
    };
    
    if (totalDays === 1) {
      return `${viewModeLabels[viewMode]} - ${format(startDate, 'MMM d, yyyy')}`;
    }
    
    return `${viewModeLabels[viewMode]} - ${format(startDate, 'MMM d')} to ${format(endDate, 'MMM d, yyyy')} (${totalDays} days)`;
  }, [displayDays, viewMode]);
  
  // Navigation functions
  const goToPrevious = () => {
    if (viewMode === 'full') return;
    
    const days = viewMode === 'day' ? 1 : viewMode === '3days' ? 3 : 7;
    setCurrentDate(addDays(currentDate, -days));
  };
  
  const goToNext = () => {
    if (viewMode === 'full') return;
    
    const days = viewMode === 'day' ? 1 : viewMode === '3days' ? 3 : 7;
    setCurrentDate(addDays(currentDate, days));
  };
  
  const goToToday = () => {
    setCurrentDate(new Date());
  };
  
  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Visual Timeline</h2>
              <p className="text-muted-foreground">{job.name}</p>
              <p className="text-sm text-gray-600">📅 {periodInfo}</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-sm text-muted-foreground mr-4">
                {shifts.length} total shifts | {displayDays.length} days shown
              </div>
              <Button variant="outline" size="sm" onClick={onRefresh}>
                Refresh
              </Button>
            </div>
          </div>
          
          {/* View Mode Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium">View:</span>
              <div className="flex items-center gap-1">
                {[
                  { value: 'day', label: '1 Day' },
                  { value: '3days', label: '3 Days' },
                  { value: 'week', label: '1 Week' },
                  { value: 'full', label: 'Full Job' }
                ].map((option) => (
                  <Button
                    key={option.value}
                    variant={viewMode === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setViewMode(option.value as typeof viewMode);
                      if (option.value !== 'full') {
                        // Set to job start date when switching to date-based views
                        const jobStart = job.startDate ? new Date(job.startDate) : new Date();
                        setCurrentDate(jobStart);
                      }
                    }}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Navigation Controls */}
            {viewMode !== 'full' && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={goToPrevious}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={goToToday}>
                  Today
                </Button>
                <Button variant="outline" size="sm" onClick={goToNext}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Time Scale */}
          <div className="flex items-center">
            <div className="w-32"></div>
            <div className="flex-1 relative">
              <div className="flex justify-between text-xs text-gray-500 pb-1">
                {timeSlots.map((slot) => (
                  <div key={slot.hour} className="flex-shrink-0">
                    {slot.label}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Crew Chief Legend */}
          {crewChiefs.length > 0 && (
            <div className="flex items-center gap-2 text-xs">
              <span className="text-gray-600">Crew Chiefs:</span>
              <div className="flex flex-wrap gap-2">
                {crewChiefs.map((chief) => (
                  <div 
                    key={chief.name} 
                    className="flex items-center gap-1 px-2 py-1 rounded"
                    style={{ backgroundColor: chief.color.light, color: chief.color.bg }}
                  >
                    <div 
                      className="w-2 h-2 rounded-full" 
                      style={{ backgroundColor: chief.color.bg }}
                    ></div>
                    <span>{chief.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Timeline Grid */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          {displayDays.map((day, index) => {
            const pacificDay = utcToPacificTime(day);
            const dateKey = format(pacificDay, 'yyyy-MM-dd');
            
            // Assign lanes to shifts for the current day
            const dayShifts = assignLanes(shiftsByDate[dateKey] || []);
            console.log(`📅 [Timeline] Rendering day ${dateKey} with ${dayShifts.length} shifts`);
            
            // Calculate the maximum lane used to determine the minimum height for the day row
            const maxLane = dayShifts.reduce((max, shift) => Math.max(max, shift.lane || 0), 0);
            // Base height (e.g., 40px per lane) + some padding
            const minDayHeight = 40 + (maxLane + 1) * 40;
            const isEvenDay = index % 2 === 0; // Determine if it's an even or odd day for alternating background

            return (
              <div
                key={dateKey}
                className={`flex ${isEvenDay ? 'bg-gray-50' : 'bg-white'}`} // Apply alternating background
                style={{ minHeight: `${minDayHeight}px` }}
              >
                {/* Date column */}
                <div className="w-32 p-4 border-r bg-gray-50/50">
                  <div className="text-sm font-medium">
                    {format(utcToPacificTime(day), 'EEE, MMM d')}
                  </div>
                  <div className="text-xs text-gray-600">
                    {dayShifts.length} shift{dayShifts.length !== 1 ? 's' : ''}
                  </div>
                </div>

                {/* Timeline area */}
                <div className="flex-1 relative p-2">
                  {/* Grid lines */}
                  <div className="absolute inset-0 flex">
                    {timeSlots.map((slot) => (
                      <div 
                        key={slot.hour}
                        className="absolute top-0 bottom-0 w-px bg-gray-200"
                        style={{ left: `${slot.position}%` }}
                      />
                    ))}
                  </div>

                  {/* Shift bars */}
                  {dayShifts.map((shift, index) => {
                    console.log(`🎯 [Timeline] Rendering shift ${shift.id}:`, {
                      startTime: shift.startTime,
                      endTime: shift.endTime,
                      crewChiefs: shift.crewChiefs?.length || 0,
                      crewChiefsData: shift.crewChiefs,
                      workers: shift.workers ? Object.keys(shift.workers) : 'none',
                      workersData: shift.workers,
                      totalAssigned: shift.totalAssigned,
                      staffingCompletion: shift.staffingCompletion
                    });
                    
                    const position = getShiftPosition(shift);
                    const crewChiefInfo = getCrewChiefInfo(shift);
                    const staffing = getStaffingDetails(shift);
                    const workerSegments = createWorkerTypeSegments(shift);
                    const shiftLabel = shift.description || job.name;
                    
                    return (
                      <div
                        key={shift.id || index}
                        className="absolute group cursor-pointer transition-all hover:shadow-md hover:z-10"
                        style={{
                          left: `${position.left}%`,
                          width: `${position.width}%`,
                          top: `${4 + ((shift.lane || 0) * 40)}px`, // Use lane for vertical positioning
                          height: '36px', // Adjusted height for better spacing
                        }}
                        onClick={() => {
                          setSelectedShift(shift);
                          setIsModalOpen(true);
                        }}
                        title={`${shiftLabel} (${formatPacificTimeRange(shift.startTime, shift.endTime)}) • Crew Chief: ${crewChiefInfo.isAssigned ? crewChiefInfo.name : 'Unassigned'} • Staffing: ${staffing.totalAssigned}/${staffing.totalRequired} workers • Click to manage assignments`}
                      >
                        {/* Main shift container with crew chief border color */}
                        <div 
                          className={`relative h-full rounded ${crewChiefInfo.isAssigned ? 'crew-chief-assigned' : 'crew-chief-unassigned'}`}
                          style={{
                            borderColor: crewChiefInfo.color.bg,
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          }}
                        >
                          {/* Individual worker type segments */}
                          {workerSegments.map((segment, segmentIndex) => (
                            <div
                              key={segmentIndex}
                              className="absolute h-full transition-opacity hover:opacity-90"
                              style={{
                                left: `${segment.left}%`,
                                width: `${segment.width}%`,
                                backgroundColor: segment.color,
                                borderRight: segmentIndex < workerSegments.length - 1 ? `1px solid rgba(0,0,0,0.1)` : 'none',
                                opacity: segment.filled ? 0.9 : 0.75,
                              }}
                            >
                              {/* Worker type icon (for larger segments) */}
                              {segment.width > 8 && segment.icon && (
                                <div className="absolute inset-0 flex items-center justify-center text-xs opacity-70">
                                  {segment.icon}
                                </div>
                              )}
                            </div>
                          ))}
                          
                          {/* Personnel indicators */}
                          <div className="absolute top-0 right-1 flex -space-x-1">
                            {/* Crew Chief indicator */}
                            {crewChiefInfo.isAssigned ? (
                              <div 
                                className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center text-xs text-white font-bold shadow-md"
                                style={{
                                  backgroundColor: crewChiefInfo.color.bg,
                                }}
                                title={`Crew Chief: ${crewChiefInfo.name}`}
                              >
                                C
                              </div>
                            ) : (
                              <div 
                                className="w-5 h-5 rounded-full border-2 border-red-300 bg-red-100 flex items-center justify-center text-xs text-red-600 font-bold shadow-md animate-pulse"
                                title="No Crew Chief Assigned - Click to assign"
                              >
                                !
                              </div>
                            )}
                            {/* Worker indicators */}
                            {shift.workers && Object.entries(shift.workers).map(([roleCode, workers]: [string, any]) => (
                              workers.slice(0, 3).map((worker: any, idx: number) => (
                                <div 
                                  key={`${roleCode}-${idx}`}
                                  className="w-4 h-4 rounded-full bg-green-500 border border-white flex items-center justify-center text-xs text-white font-bold"
                                >
                                  {worker.name.charAt(0)}
                                </div>
                              ))
                            )).flat()}
                            {/* Show +N if more workers */}
                            {shift.workers && Object.values(shift.workers).flat().length > 3 && (
                              <div 
                                className="w-4 h-4 rounded-full bg-gray-500 border border-white flex items-center justify-center text-xs text-white font-bold"
                              >
                                +{Object.values(shift.workers).flat().length - 3}
                              </div>
                            )}
                          </div>
                          
                          {/* Text overlay */}
                          <div 
                            className="absolute inset-0 flex flex-col justify-center px-2 text-xs font-medium text-black pointer-events-none overflow-hidden"
                            style={{
                              textShadow: '0 0 3px rgba(255,255,255,0.8)',
                            }}
                          >
                            <div className="truncate leading-tight">
                              {formatPacificTimeRange(shift.startTime, shift.endTime)}
                            </div>
                            <div className="truncate text-xs text-gray-600">
                              {staffing.totalAssigned}/{staffing.totalRequired} staff
                            </div>
                            {/* Show crew chief name if assigned */}
                            {crewChiefInfo.isAssigned && (
                              <div className="truncate text-xs text-blue-700 font-semibold">
                                CC: {crewChiefInfo.name}
                              </div>
                            )}
                            {/* Show worker names if any */}
                            {shift.workers && Object.keys(shift.workers).length > 0 && (
                              <div className="truncate text-xs text-green-700">
                                Workers: {Object.values(shift.workers).flat().map((w: any) => w.name).join(', ')}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Shift Assignment Modal */}
      {selectedShift && (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
            <DialogHeader>
              <DialogTitle>
                Manage Shift Assignments for {selectedShift.description || job.name} on {selectedShift.startTime ? format(new Date(selectedShift.startTime), 'MMM d, yyyy') : 'N/A'}, {selectedShift.startTime ? format(new Date(selectedShift.startTime), 'h:mm a') : 'N/A'}-{selectedShift.endTime ? format(new Date(selectedShift.endTime), 'h:mm a') : 'N/A'}
              </DialogTitle>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              {isLoadingShiftDetails || isLoadingUsers ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
                  <span className="ml-2 text-gray-600">Loading assignments...</span>
                </div>
              ) : detailedShift ? (
                <UnifiedEnhancedTimeTracking
                  shiftId={(detailedShift as ShiftWithDetails).id}
                  assignments={(detailedShift as ShiftWithDetails).assignedPersonnel || []}
                  availableUsers={availableUsers as User[]}
                  onAssignmentUpdate={handleAssignmentUpdate}
                  onTimeUpdate={handleTimeUpdate}
                  onRefresh={handleRefresh}
                  enableTimeTracking={false}
                  shiftStatus={(detailedShift as ShiftWithDetails).status}
                  timesheets={(detailedShift as ShiftWithDetails).timesheets}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  No shift details available.
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}

export default VisualJobTimeline;
