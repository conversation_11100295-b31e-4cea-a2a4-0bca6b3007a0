{"name": "handson", "version": "0.5.0", "private": true, "type": "module", "scripts": {"analyze:bundle": "ANALYZE=true npm run build", "analyze:deps": "node scripts/analyze-dependencies.js", "build": "prisma generate && next build", "build:cloud-run": "prisma generate && next build", "clean": "\"npm cache clean --force\" && \"pwsh ./clean.ps1\"", "cloud-run:check": "node cloud-run-check.js", "cloud-run:pre-check": "pwsh cloud-run-pre-deploy-check.ps1", "compress:avatars": "tsx scripts/compress-avatars.ts", "compress:avatars:dry-run": "DRY_RUN=true tsx scripts/compress-avatars.ts", "compress:avatars:js": "node scripts/compress-avatars.js", "configure:bucket-public": "node configure-bucket-public.js", "convert:avatars": "node convert-avatars-to-gcs.js", "convert:avatars:dry-run": "node convert-avatars-to-gcs.js --dry-run", "convert:avatars:force": "node convert-avatars-to-gcs.js --force", "db:seed": "prisma db seed", "deploy": "powershell -ExecutionPolicy Bypass -File deploy.ps1", "deploy:cloudbuild": "pwsh ./deploy-local-docker.ps1", "deploy:local": "powershell -ExecutionPolicy Bypass -File deploy.ps1 -LocalBuild", "deploy:skip-verify": "powershell -ExecutionPolicy Bypass -File deploy.ps1 -SkipVerification", "dev": "next dev --hostname localhost --port 3000", "dev-clean": "npm run clean && npm run dev", "dev:custom": "node dev-server.js", "dev:firefox": "next dev --hostname localhost --port 3000", "fix:gcs-urls": "node fix-gcs-urls.js", "fresh": "npm run clean && npm install && npm run dev", "health-check": "tsx scripts/production-health-check.ts", "backfill:worker-reqs": "tsx scripts/backfill-worker-requirements.ts", "lint": "eslint .", "migrate": "prisma migrate dev", "migrate:avatar-data": "node scripts/migrate-avatar-data.js", "migrate:prod": "prisma migrate deploy", "migrate:status": "prisma migrate status", "migrate:test": "pwsh scripts/test-migration.ps1", "pre-deploy": "pwsh scripts/pre-deploy.ps1", "pre-deploy-check": "pwsh scripts/pre-deploy-check.ps1", "pretest:pre-deploy-e2e": "kill-port 3000", "start": "next start", "start:prod": "node scripts/startup-with-migration.js", "studio": "prisma studio", "verify:deployment": "node scripts/deployment-verification.js"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/pg-adapter": "^1.2.0", "@faker-js/faker": "^8.4.1", "@google-cloud/cloud-sql-connector": "^1.8.2", "@google-cloud/storage": "^7.16.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.3.4", "@mantine/core": "^7.11.1", "@mantine/form": "^7.11.1", "@mantine/hooks": "^7.11.1", "@mantine/notifications": "^7.11.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.15.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "0.5.16", "@tanstack/query-sync-storage-persister": "^5.83.0", "@tanstack/react-query": "^5.51.15", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-query-persist-client": "^5.83.0", "@types/google.maps": "^3.58.1", "@types/pdfkit": "^0.17.2", "@types/pusher-js": "^5.1.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dnd": "^2.0.36", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.19", "bcryptjs": "3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "csv-parse": "^5.5.6", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "exceljs": "^4.4.0", "gh": "^2.5.0", "git": "^0.1.5", "googleapis": "^140.0.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libreoffice-convert": "^1.6.1", "lucide-react": "^0.437.0", "next": "^15.5.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "pg": "^8.12.0", "postcss": "^8.4.40", "postcss-import": "^16.1.0", "postcss-preset-mantine": "^1.17.0", "prisma": "^6.15.0", "process": "^0.11.10", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "^19.1.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-pdf": "^10.1.0", "react-signature-canvas": "^1.0.6", "react-toastify": "^11.0.5", "recharts": "^2.12.7", "robocopy": "^0.1.16", "sonner": "^1.7.4", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.7", "ts-node": "^10.9.2", "tsx": "^4.16.2", "typescript": "^5.9.2", "uuid": "^10.0.0", "vaul": "^1.1.2", "whatwg-fetch": "^3.6.20", "ws": "^8.17.1", "xlsx": "^0.18.5", "yup": "^1.4.0", "zod": "^4.1.0"}, "optionalDependencies": {"sharp": "^0.33.4"}, "devDependencies": {"@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.34.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.13", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.6", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "@types/react-signature-canvas": "^1.0.5", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.11", "cross-env": "^7.0.3", "dotenv": "^17.2.1", "dotenv-cli": "^7.4.4", "eslint": "^8.57.0", "eslint-config-next": "^15.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "portfinder": "^1.0.37", "puppeteer": "^24.15.0", "start-server-and-test": "^2.0.3", "tree-kill": "^1.2.2"}}