import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { UserRole } from '@/lib/types';
import { getDashboardPath } from '@/lib/routing';

export default async function Dashboard() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect('/login');
  }

  // Redirect to role-specific dashboard
  const user = session.user as any;
  const dashboardPath = getDashboardPath(user.role as UserRole, user.companyId);

  if (dashboardPath && dashboardPath !== '/dashboard') {
    redirect(dashboardPath);
  }

  // Fallback - this shouldn't normally be reached
  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Redirecting to dashboard...</p>
    </div>
  );
}
