'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import * as Tabs from '@radix-ui/react-tabs';
import { ShiftHeader } from './ShiftHeader';
import { ShiftInfoCard } from './ShiftInfoCard';
import { StaffingOverviewCard } from './StaffingOverviewCard';
import UnifiedEnhancedTimeTracking from '@/components/unified-enhanced-time-tracking';
import { ShiftNotes } from './ShiftNotes';
import { Button } from '@/components/ui/button';
import { ShiftPermissionsManager } from './ShiftPermissionsManager';
import { ShiftDangerZone } from './ShiftDangerZone';
import { ShiftWithDetails } from '@/lib/types';
import { FileText, Download } from "lucide-react";

interface ShiftDetailsLayoutProps {
  shift: ShiftWithDetails;
  assignments: any[];
  users: any[];
  onAssignmentUpdate: (assignmentId: string, userId: string | null, roleCode?: string) => void;
  onEditShift: () => void;
  onClockIn?: (assignmentId: string) => void;
  onClockOut?: (assignmentId: string) => void;
  onEndShift?: () => void;
  onEndWorkerShift?: (assignmentId: string) => void;
  onMarkNoShow?: (assignmentId: string) => void;
  onMasterStartBreak?: () => void;
  onMasterEndShift?: () => void;
  onFinalizeTimesheet?: () => void;
  onTimeUpdate?: (entryId: string, field: 'clockIn' | 'clockOut', value: string) => void;
  onRefresh?: () => void;
  userRole?: string;
  isUpdatingAssignment?: Record<string, boolean>;
}

export function ShiftDetailsLayout({
  shift,
  assignments,
  users,
  onAssignmentUpdate,
  onEditShift,
  onClockIn,
  onClockOut,
  onEndShift,
  onEndWorkerShift,
  onMarkNoShow,
  onMasterStartBreak,
  onMasterEndShift,
  onFinalizeTimesheet,
  onTimeUpdate,
  onRefresh,
  userRole,
  isUpdatingAssignment = {},
}: ShiftDetailsLayoutProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const hasTimesheet = shift.timesheets && shift.timesheets.length > 0;
  const timesheet = hasTimesheet ? shift.timesheets[0] : null;

  const sections = {
    overview: useRef<HTMLDivElement>(null),
    'time-tracking': useRef<HTMLDivElement>(null),
    details: useRef<HTMLDivElement>(null),
    settings: useRef<HTMLDivElement>(null),
  };

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
    sections[tab as keyof typeof sections].current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveTab(entry.target.id);
          }
        });
      },
      { rootMargin: '-50% 0px -50% 0px' }
    );

    Object.values(sections).forEach((sectionRef) => {
      if (sectionRef.current) {
        observer.observe(sectionRef.current);
      }
    });

    return () => {
      Object.values(sections).forEach((sectionRef) => {
        if (sectionRef.current) {
          observer.unobserve(sectionRef.current);
        }
      });
    };
  }, []);

  return (
    <div className="space-y-6">
      <ShiftHeader shift={shift} />
      
      {hasTimesheet && timesheet && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="h-5 w-5 text-green-400" />
              <div>
                <h3 className="text-sm font-medium text-green-400">Timesheet Available</h3>
                <p className="text-xs text-green-300/80">
                  Status: {timesheet.status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
                <Link
                  href={`/timesheets/${timesheet.id}`}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                >
                  View Timesheet
                </Link>
                <a
                  href={`/api/timesheets/${timesheet.id}/pdf`}
                  download
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </a>
            </div>
          </div>
        </div>
      )}
      
      <Tabs.Root value={activeTab} onValueChange={setActiveTab} className="sticky top-0 bg-background z-10 py-2">
        <Tabs.List className="flex border-b overflow-x-auto pb-px no-scrollbar">
          {Object.keys(sections).map(tab => (
            <Tabs.Trigger 
              key={tab}
              value={tab} 
              onClick={() => handleTabClick(tab)}
              className="flex-shrink-0 px-4 py-2 -mb-px border-b-2 border-transparent data-[state=active]:border-blue-500 text-sm sm:text-base capitalize"
            >
              {tab.replace('-', ' ')}
            </Tabs.Trigger>
          ))}
        </Tabs.List>
      </Tabs.Root>

      <div id="overview" ref={sections.overview} className="py-6 scroll-mt-20">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div className="lg:col-span-1">
            <ShiftInfoCard shift={shift} />
          </div>
          <div className="lg:col-span-2">
            <StaffingOverviewCard shift={shift} assignments={assignments} />
          </div>
        </div>
      </div>

      <div id="time-tracking" ref={sections['time-tracking']} className="py-6 scroll-mt-20">
        {hasTimesheet && timesheet && (
          <div className="mb-4">
            <Link href={`/timesheets/${timesheet.id}`}>
              <Button variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                View Associated Timesheet
              </Button>
            </Link>
          </div>
        )}
        <UnifiedEnhancedTimeTracking
          shiftId={shift.id}
          assignments={assignments}
          availableUsers={users}
          onAssignmentUpdate={onAssignmentUpdate}
          onRefresh={onRefresh || (() => {})}
          requirements={(Array.isArray((shift as any).workerRequirements) && (shift as any).workerRequirements.length > 0)
            ? (shift as any).workerRequirements.map((r: any) => ({ roleCode: (r.roleCode || r.workerTypeCode), requiredCount: Number(r.requiredCount) || 0 }))
            : [
                { roleCode: 'CC', requiredCount: Number((shift as any).requiredCrewChiefs) || 0 },
                { roleCode: 'RG', requiredCount: Number((shift as any).requiredRiggers) || 0 },
                { roleCode: 'RFO', requiredCount: Number((shift as any).requiredReachForkOperators) || 0 },
                { roleCode: 'FO', requiredCount: Number((shift as any).requiredForkOperators) || 0 },
                { roleCode: 'SH', requiredCount: Number((shift as any).requiredStagehands) || 0 },
              ]}
          shiftStatus={shift.status}
          timesheets={shift.timesheets}
          disabled={userRole === 'Employee'}
          enableTimeTracking={!!onClockIn && !!onClockOut}
          onClockIn={onClockIn}
          onClockOut={onClockOut}
          onEndWorkerShift={onEndWorkerShift}
          onMarkNoShow={onMarkNoShow}
          onMasterStartBreak={onMasterStartBreak}
          onMasterEndShift={onMasterEndShift}
          onFinalizeTimesheet={onFinalizeTimesheet}
          onTimeUpdate={onTimeUpdate}
          isUpdatingAssignment={isUpdatingAssignment}
        />
      </div>

      <div id="details" ref={sections.details} className="py-6 scroll-mt-20">
        <ShiftNotes shiftId={shift.id} initialNotes={shift.notes || ""} />
      </div>

      <div id="settings" ref={sections.settings} className="py-6 scroll-mt-20">
        <ShiftPermissionsManager shift={shift} />
        <ShiftDangerZone shift={shift} onEndShift={onEndShift || (() => {})} onFinalizeTimesheet={onFinalizeTimesheet || (() => {})} />
      </div>
    </div>
  );
}
