import React from 'react';
import { Avatar } from '@/components/Avatar';

type Props = {
  src?: string | null;
  name: string;
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
};

export function CompanyAvatar({ src, name, className, size = 'md' }: Props) {
  return (
    <Avatar
      src={src || undefined}
      name={name}
      className={className}
      size={size}
      type="company"
    />
  );
}
