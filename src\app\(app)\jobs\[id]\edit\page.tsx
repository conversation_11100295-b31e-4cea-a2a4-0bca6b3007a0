"use client"

import React, { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useUnifiedJob, useUnifiedCompanies, useUnifiedMutation } from "@/hooks/use-unified-api"
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { UserRole } from '@prisma/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Briefcase, AlertCircle, RefreshCw, Save } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useUser } from "@/hooks/use-user"
import { formatDateForInput, apiDateUtils } from '@/lib/date-utils'
import { PageErrorBoundary, ComponentErrorBoundary } from "@/components/error-boundary"
import { PageLoader, InlineLoader } from "@/components/loading-states"

interface JobEditPageProps {
  params: { id: string }
}

function JobEditPageContent({ params }: JobEditPageProps) {
  const { id } = params
  const { user } = useUser()
  const router = useRouter()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data: job, isLoading: jobLoading, isError: jobError, refetch: refetchJob } = useUnifiedJob(id)
  const { data: companiesData, isLoading: companiesLoading, isError: companiesError, refetch: refetchCompanies } = useUnifiedCompanies()
  const companies = React.useMemo(() => companiesData?.companies ?? [], [companiesData]);

  // Ensure the current job's company is in the dropdown, even if not in the paginated companies list
  const mergedCompanies = React.useMemo(() => {
    if (!job) return companies;
    const exists = companies.some((c: any) => c.id === job.companyId);
    return exists ? companies : [job.company, ...companies];
  }, [companies, job])

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    companyId: '',
    startDate: '',
    endDate: ''
  })

  // Update job mutation with optimistic updates
  const updateJobMutation = useUnifiedMutation(
    async (data: typeof formData) => {
      const submitData = {
        name: data.name || job?.name || '',
        description: data.description ?? (job?.description ?? ''),
        companyId: data.companyId || job?.companyId,
        startDate: data.startDate ? apiDateUtils.formatForAPI(data.startDate) : (job?.startDate || undefined),
        endDate: data.endDate ? apiDateUtils.formatForAPI(data.endDate) : (job?.endDate || undefined),
      };

      const response = await fetch(`/api/jobs/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update job');
      }

      return response.json();
    },
    {
      entityType: 'jobs',
      mutationType: 'update',
      entityId: id,
      optimisticUpdate: {
        queryKey: ['jobs'],
        updateFn: (oldData: any, variables: typeof formData) => {
          if (Array.isArray(oldData)) {
            return oldData.map((j: any) => 
              j.id === id ? { ...j, ...variables } : j
            );
          }
          return oldData;
        }
      },
      onSuccess: (data) => {
        toast({ 
          title: "Job Updated Successfully", 
          description: `"${formData.name}" has been updated and saved.` 
        });
        router.push(`/jobs/${id}`);
      },
      onError: (error) => {
        console.error('Job update error:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to update job",
          variant: "destructive",
        });
      }
    }
  );

  useEffect(() => {
    if (user && user.role !== UserRole.Admin) {
      router.push('/jobs')
    }
  }, [user, router])

  useEffect(() => {
    if (job) {
      setFormData({
        name: job.name || '',
        description: job.description || '',
        companyId: job.companyId || '',
        startDate: job.startDate ? formatDateForInput(job.startDate) : '',
        endDate: job.endDate ? formatDateForInput(job.endDate) : ''
      });
    }
  }, [job]);

  const loading = jobLoading || companiesLoading;
  const error = jobError || companiesError;

  if (loading) {
    return <PageLoader title="Loading Job..." description="Please wait while we load the job details." />
  }

  if (error || !job) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[60vh]">
            <Alert className="max-w-md bg-red-900/20 border-red-800">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-200">
                Error loading job data: {error?.toString()}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => { refetchJob(); refetchCompanies(); }}
                  className="mt-2 w-full border-red-700 text-red-200 hover:bg-red-800"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </main>
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Basic validation
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Job name is required",
        variant: "destructive",
      })
      return
    }

    if (!formData.companyId) {
      toast({
        title: "Validation Error", 
        description: "Company selection is required",
        variant: "destructive",
      })
      return
    }

    // Validate date range
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      
      if (endDate < startDate) {
        toast({
          title: "Validation Error",
          description: "End date cannot be before start date",
          variant: "destructive",
        })
        return
      }
    }

    updateJobMutation.mutate(formData);
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-3xl mx-auto space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push(`/jobs/${id}`)} className="text-gray-300 hover:text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Job
            </Button>
          </div>
          
          <ComponentErrorBoundary componentName="Job Edit Form">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Briefcase className="h-5 w-5" /> 
                  Edit Job
                </CardTitle>
                <CardDescription>
                  Update the job details below for client: <span className="font-semibold text-indigo-400">{job.company.name}</span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-gray-300">Job Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter job name"
                      required
                      disabled={updateJobMutation.isPending}
                      className="bg-gray-700 border-gray-600 text-white disabled:opacity-50"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="client" className="text-gray-300">Client *</Label>
                    <Select
                      value={formData.companyId}
                      onValueChange={(value) => setFormData({ ...formData, companyId: value })}
                      required
                      disabled={updateJobMutation.isPending}
                    >
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white disabled:opacity-50">
                        <SelectValue placeholder="Select a client" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700 text-white">
                        {mergedCompanies?.map((company: any) => (
                          <SelectItem key={company.id} value={company.id}>
                            {company.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-gray-300">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter job description"
                      rows={4}
                      disabled={updateJobMutation.isPending}
                      className="bg-gray-700 border-gray-600 text-white disabled:opacity-50"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate" className="text-gray-300">Start Date</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={formData.startDate}
                        onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                        disabled={updateJobMutation.isPending}
                        className="bg-gray-700 border-gray-600 text-white disabled:opacity-50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="endDate" className="text-gray-300">End Date</Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={formData.endDate}
                        onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                        disabled={updateJobMutation.isPending}
                        className="bg-gray-700 border-gray-600 text-white disabled:opacity-50"
                      />
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    <Button 
                      type="submit" 
                      disabled={updateJobMutation.isPending} 
                      className="bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
                    >
                      {updateJobMutation.isPending ? (
                        <InlineLoader message="Updating..." />
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Update Job
                        </>
                      )}
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => router.push(`/jobs/${id}`)} 
                      disabled={updateJobMutation.isPending}
                      className="bg-gray-800 border-gray-700 hover:bg-gray-700 disabled:opacity-50"
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </ComponentErrorBoundary>
        </div>
      </main>
    </div>
  )
}

export default function JobEditPage({ params }: JobEditPageProps) {
  return (
    <PageErrorBoundary>
      <JobEditPageContent params={params} />
    </PageErrorBoundary>
  )
}
