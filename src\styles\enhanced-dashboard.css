/*
 * Enhanced Dashboard Styles - Mobile-First with 3D Effects
 */

/* Enhanced animations for status indicators */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes statusGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

@keyframes urgentBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  25% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  75% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes floatUp {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Enhanced status badge styles */
.status-badge-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-badge-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.status-badge-enhanced:hover::before {
  left: 100%;
}

/* Critical status animations */
.status-critical {
  animation: urgentBlink 2s infinite;
}

.status-urgent {
  animation: statusPulse 1.5s infinite;
}

.status-active {
  animation: statusGlow 3s infinite;
}

/* Card hover effects */
.card-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-enhanced:hover::before {
  opacity: 1;
}

.card-enhanced:hover {
  transform: translateY(-4px) scale(1.02);
}

/* Progress bar enhancements */
.progress-enhanced {
  position: relative;
  overflow: hidden;
}

.progress-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 50px 50px;
  animation: shimmer 2s linear infinite;
}

/* Date indicator styles */
.date-indicator-today {
  animation: statusGlow 2s infinite;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.date-indicator-tomorrow {
  background: linear-gradient(135deg, #10b981, #059669);
}

.date-indicator-overdue {
  animation: urgentBlink 1s infinite;
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.date-indicator-soon {
  animation: statusPulse 2s infinite;
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
  .card-enhanced:hover {
    transform: none; /* Disable hover effects on mobile */
  }
  
  .status-badge-enhanced {
    min-height: 44px; /* Ensure touch target size */
    min-width: 44px;
  }
  
  /* Larger text for mobile readability */
  .mobile-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  .mobile-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .card-enhanced::before {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.02) 100%
    );
  }
  
  .status-badge-enhanced::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
  .status-critical,
  .status-urgent,
  .status-active,
  .progress-enhanced::after {
    animation: none;
  }
  
  .card-enhanced {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .status-badge-enhanced {
    border-width: 2px;
    font-weight: bold;
  }
  
  .card-enhanced {
    border-width: 2px;
  }
}

/* Focus styles for keyboard navigation */
.status-badge-enhanced:focus,
.card-enhanced:focus {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(
    90deg,
    #374151 25%,
    #4b5563 50%,
    #374151 75%
  );
  background-size: 200% 100%;
}

/* Staggered animations for lists */
.stagger-animation {
  animation: fadeInUp 0.6s ease-out;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }

/* Utility classes for enhanced effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-border {
  position: relative;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899) border-box;
  border: 2px solid transparent;
}

.dark .gradient-border {
  background: linear-gradient(#1f2937, #1f2937) padding-box,
              linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899) border-box;
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}