'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PDFStats } from '@/types/pdf-stats';

interface PerformanceTabProps {
  performance: PDFStats['performance'];
}

const MetricRow: React.FC<{ label: string; value: string; variant?: "default" | "secondary" | "destructive" | "outline" | null | undefined }> = ({ label, value, variant = "outline" }) => (
  <div className="flex justify-between">
    <span>{label}:</span>
    <Badge variant={variant}>{value}</Badge>
  </div>
);

const GenerationExtreme: React.FC<{ title: string; data: PDFStats['performance']['fastestGeneration'] | PDFStats['performance']['slowestGeneration']; colorClass: string }> = ({ title, data, colorClass }) => {
  if (!data) return null;
  return (
    <div>
      <h4 className={`font-medium ${colorClass}`}>{title}</h4>
      <p className="text-sm text-gray-600">
        {data.generationTime.toFixed(0)}ms
        <br />
        Timesheet: {data.timesheetId.slice(0, 8)}...
      </p>
    </div>
  );
};

export const PerformanceTab: React.FC<PerformanceTabProps> = ({ performance }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>PDF generation performance statistics</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <MetricRow 
            label="Average Generation Time" 
            value={`${performance.averageGenerationTime.toFixed(0)}ms`}
            variant={performance.averageGenerationTime > 5000 ? 'destructive' : 'secondary'}
          />
          <MetricRow 
            label="Average PDF Size" 
            value={`${(performance.averagePdfSize / 1024).toFixed(1)} KB`}
          />
          <MetricRow 
            label="Error Rate" 
            value={`${performance.errorRate.toFixed(1)}%`}
            variant={performance.errorRate > 5 ? 'destructive' : 'secondary'}
          />
          <MetricRow 
            label="Peak Memory Usage" 
            value={`${(performance.peakMemoryUsage / 1024 / 1024).toFixed(1)} MB`}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Generation Extremes</CardTitle>
          <CardDescription>Fastest and slowest PDF generations</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <GenerationExtreme title="Fastest Generation" data={performance.fastestGeneration} colorClass="text-green-600" />
          <GenerationExtreme title="Slowest Generation" data={performance.slowestGeneration} colorClass="text-red-600" />
        </CardContent>
      </Card>
    </div>
  );
};
