"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useUnifiedShift, useUnifiedUsers, useUnifiedMutation } from "@/hooks/use-unified-api"
import { useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, Save, Upload } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { generateShiftUrl } from "@/lib/utils"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import WorkerRequirements from "@/components/worker-requirements"
import ShiftImportCSV from "@/components/shift-import-csv"
import type { RoleCode } from "@/lib/types"
import { useUser } from "@/hooks/use-user"
import {
  formatDateForInput,
  formatTimeForInput,
  createDateTimeFromInputs,
  formatDateForAPI,
  formatDateForDisplay,
} from "@/lib/date-utils"
import { PageErrorBoundary, ComponentErrorBoundary } from "@/components/error-boundary"
import { PageLoader, InlineLoader } from "@/components/loading-states"

const shiftSchema = z.object({
  date: z.string().min(1, "Date is required"),
  startTime: z.string().min(1, "Start time is required"),
  endTime: z.string().min(1, "End time is required"),
  location: z.string().min(1, "Location is required"),
  description: z.string().optional(),
  notes: z.string().optional(),
  workerRequirements: z.array(z.object({
    roleCode: z.string().optional(),
    workerTypeCode: z.string().optional(),
    requiredCount: z.number().min(0),
  })).optional(),
})

type ShiftFormData = z.infer<typeof shiftSchema>

function EditShiftPageContent() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useUser()
  const shiftId = params.shiftId as string
  const queryClient = useQueryClient()

  const { data: shift, isLoading: shiftLoading, isError: shiftError, error: shiftFetchError } = useUnifiedShift(shiftId)

  const form = useForm<ShiftFormData>({
    resolver: zodResolver(shiftSchema),
  })

  // Update shift mutation with optimistic updates
  const updateShiftMutation = useUnifiedMutation(
    async (data: ShiftFormData) => {
      const { date, startTime, endTime, ...restOfData } = data;

      const payload = {
        ...restOfData,
        date: formatDateForAPI.formatForAPI(date),
        startTime: formatDateForAPI.formatForAPI(createDateTimeFromInputs(date, startTime)),
        endTime: formatDateForAPI.formatForAPI(createDateTimeFromInputs(date, endTime)),
      };

      const response = await fetch(`/api/shifts/[shiftId]`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to update shift');
      }

      return response.json();
    },
    {
      entityType: 'shifts',
      mutationType: 'update',
      entityId: shiftId,
      optimisticUpdate: {
        queryKey: ['shift', shiftId],
        updateFn: (oldData: any, variables: ShiftFormData) => {
          if (oldData) {
            return { ...oldData, ...variables };
          }
          return oldData;
        }
      },
      onSuccess: () => {
        toast({
          title: "Shift Updated Successfully",
          description: `Shift details for ${shift?.job?.name || 'Unknown Job'} on ${shift?.date ? formatDateForDisplay(shift.date) : 'Unknown Date'} have been saved.`,
        });

        if (shiftId) {
          router.push(generateShiftUrl(shiftId));
        }
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: "Failed to update shift. Please try again.",
          variant: "destructive",
        });
      }
    }
  );

  useEffect(() => {
    if (shift) {
      form.reset({
        date: formatDateForInput(shift.date),
        startTime: formatTimeForInput(shift.startTime),
        endTime: formatTimeForInput(shift.endTime),
        location: shift.location || '',
        description: shift.description || '',
        notes: shift.notes || '',
        workerRequirements: (shift.workerRequirements || []).map((r: any) => ({
          roleCode: r.roleCode || r.workerTypeCode,
          requiredCount: r.requiredCount || 0,
        })),
      });
    }
  }, [shift, form.reset]);

  const onSubmit = (data: ShiftFormData) => {
    updateShiftMutation.mutate(data);
  };

  // Debug logging
  console.log('EditShiftPage - shiftId:', shiftId);
  console.log('EditShiftPage - shift data:', shift);
  console.log('EditShiftPage - isLoading:', shiftLoading);
  console.log('EditShiftPage - isError:', shiftError);
  console.log('EditShiftPage - error:', shiftFetchError);

  if (shiftLoading) {
    return <PageLoader title="Loading Shift..." description="Please wait while we load the shift details." />
  }

  if (shiftError || !shift) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Shift Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The shift you're looking for doesn't exist or you don't have permission to edit it.
              </p>
              {process.env.NODE_ENV === 'development' && shiftFetchError && (
                <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
                  <p className="text-sm text-red-600">
                    <strong>Debug Error:</strong> {shiftFetchError.message}
                  </p>
                </div>
              )}
              <Button onClick={() => {
                // Navigate to admin/shifts for admins, otherwise to regular shifts page
                const shiftsPath = user?.role === 'Admin' ? '/admin/shifts' : '/shifts';
                router.push(shiftsPath);
              }}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Shifts
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const workerRequirements: { roleCode: RoleCode, requiredCount: number }[] = (
    Array.isArray(shift.workerRequirements) ? shift.workerRequirements : []
  ).map((r: any) => ({ roleCode: (r.roleCode || r.workerTypeCode) as RoleCode, requiredCount: r.requiredCount || 0 }));

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.push(generateShiftUrl(shiftId))}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Shift
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit Shift</h1>
            <p className="text-muted-foreground">
              {shift.job?.company?.name} • {shift.job?.name} • {formatDateForDisplay(shift.date)}
            </p>
          </div>
        </div>
      </div>

      {/* Debug info - remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardHeader>
            <CardTitle className="text-sm">Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs space-y-1">
              <div><strong>Shift Location:</strong> {shift?.location || 'null'}</div>
              <div><strong>Form Location:</strong> {form.watch('location') || 'empty'}</div>
              <div>
                <strong>Form Errors:</strong>
                {Object.entries(form.formState.errors).length > 0 ? (
                  <ul className="list-disc list-inside pl-4">
                    {Object.entries(form.formState.errors).map(([key, error]) => (
                      error?.message && <li key={key}><strong>{key}:</strong> {error.message}</li>
                    ))}
                  </ul>
                ) : (
                  ' None'
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <ComponentErrorBoundary componentName="Shift Details Form">
          <Card>
            <CardHeader>
              <CardTitle>Shift Details</CardTitle>
              <CardDescription>
                Update the basic information for this shift
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="date">Date *</Label>
                  <Input
                    id="date"
                    type="date"
                    {...form.register("date")}
                    disabled={updateShiftMutation.isPending}
                  />
                  {form.formState.errors.date && (
                    <p className="text-sm text-destructive">{form.formState.errors.date.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="startTime">Start Time *</Label>
                  <Input
                    id="startTime"
                    type="time"
                    {...form.register("startTime")}
                    disabled={updateShiftMutation.isPending}
                  />
                  {form.formState.errors.startTime && (
                    <p className="text-sm text-destructive">{form.formState.errors.startTime.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endTime">End Time *</Label>
                  <Input
                    id="endTime"
                    type="time"
                    {...form.register("endTime")}
                    disabled={updateShiftMutation.isPending}
                  />
                  {form.formState.errors.endTime && (
                    <p className="text-sm text-destructive">{form.formState.errors.endTime.message}</p>
                  )}
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    placeholder="Enter shift location"
                    {...form.register("location")}
                    disabled={updateShiftMutation.isPending}
                  />
                  {form.formState.errors.location && (
                    <p className="text-sm text-destructive">{form.formState.errors.location.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </ComponentErrorBoundary>

        <ComponentErrorBoundary componentName="Worker Requirements">
          <Card>
            <CardHeader>
              <CardTitle>Worker Requirements & Assignment</CardTitle>
              <CardDescription>
                Manage worker requirements manually or import from CSV data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="requirements" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="requirements">Worker Requirements</TabsTrigger>
                  <TabsTrigger value="import">
                    <Upload className="h-4 w-4 mr-2" />
                    Import from CSV
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="requirements" className="mt-6">
                  <WorkerRequirements
                    shiftId={shiftId}
                    workerRequirements={workerRequirements}
                    onUpdate={(updatedRequirements) => {
                      // Handle successful update without page refresh
                      console.log('Worker requirements updated:', updatedRequirements);
                      // The component handles its own state updates
                    }}
                  />
                </TabsContent>

                <TabsContent value="import" className="mt-6">
                  <ShiftImportCSV
                    shiftId={shiftId}
                    onImportComplete={(result) => {
                      toast({
                        title: "Import Successful",
                        description: `Imported ${result.workersProcessed} workers and updated requirements automatically.`
                      });
                      // Note: Components handle their own state updates
                      console.log('Import completed:', result);
                    }}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </ComponentErrorBoundary>

        <ComponentErrorBoundary componentName="Additional Information Form">
          <Card>
            <CardHeader>
              <CardTitle>Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the work to be performed"
                  {...form.register("description")}
                  rows={3}
                  disabled={updateShiftMutation.isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Additional notes or comments"
                  {...form.register("notes")}
                  rows={3}
                  disabled={updateShiftMutation.isPending}
                />
              </div>
            </CardContent>
          </Card>
        </ComponentErrorBoundary>

        <div className="flex justify-end gap-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.push(generateShiftUrl(shiftId))}
            disabled={updateShiftMutation.isPending}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={updateShiftMutation.isPending}
          >
            {updateShiftMutation.isPending ? (
              <InlineLoader message="Saving..." />
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default function EditShiftPage() {
  return (
    <PageErrorBoundary>
      <EditShiftPageContent />
    </PageErrorBoundary>
  )
}
