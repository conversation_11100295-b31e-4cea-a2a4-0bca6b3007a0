import { useMemo } from 'react';
import { User } from '../lib/types';

interface FilterState {
  searchText: string;
  activeOnly: boolean;
  crewChiefEligible: boolean;
  forkOperatorEligible: boolean;
  hasCertifications: boolean;
  location: string;
}

export const useWorkerFilter = (users: User[], filters: FilterState, requiredRole?: string) => {
  const filteredUsers = useMemo(() => {
    let filtered = users.filter((user) => {
      // Search text filter
      if (filters.searchText) {
        const searchLower = filters.searchText.toLowerCase();
        const matchesName = user.name.toLowerCase().includes(searchLower);
        const matchesEmail = user.email.toLowerCase().includes(searchLower);
        const matchesCertifications = user.certifications?.some((cert: string) => 
          cert.toLowerCase().includes(searchLower)
        );
        
        if (!matchesName && !matchesEmail && !matchesCertifications) {
          return false;
        }
      }

      // Active status filter
      if (filters.activeOnly && !user.isActive) {
        return false;
      }

      // Crew chief eligibility filter
      if (filters.crewChiefEligible && !user.crew_chief_eligible) {
        return false;
      }

      // Fork operator eligibility filter
      if (filters.forkOperatorEligible && !user.fork_operator_eligible) {
        return false;
      }

      // Has certifications filter
      if (filters.hasCertifications && (!user.certifications || user.certifications.length === 0)) {
        return false;
      }

      // Location filter
      if (filters.location && user.location !== filters.location) {
        return false;
      }

      return true;
    });

    // Sort by name
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    // If there's a required role, prioritize eligible users
    if (requiredRole) {
      filtered.sort((a, b) => {
        if (requiredRole === 'CC') {
          if (a.crew_chief_eligible && !b.crew_chief_eligible) return -1;
          if (!a.crew_chief_eligible && b.crew_chief_eligible) return 1;
        }
        if (requiredRole === 'FO' || requiredRole === 'RFO') {
          if (a.fork_operator_eligible && !b.fork_operator_eligible) return -1;
          if (!a.fork_operator_eligible && b.fork_operator_eligible) return 1;
        }
        return 0;
      });
    }

    return filtered;
  }, [users, filters, requiredRole]);

  return filteredUsers;
};
