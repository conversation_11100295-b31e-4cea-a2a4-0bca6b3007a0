import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ shiftId: string; assignmentId: string }> }
) {
  try {
    const { shiftId, assignmentId } = await params
    const body = await request.json()
    const { action } = body

    console.log(`Clock ${action} request - FIXED:`, { shiftId, assignmentId, action })

    if (!action || !['clock_in', 'clock_out'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be clock_in or clock_out' },
        { status: 400 }
      )
    }

    if (action === 'clock_in') {
      // Prevent multiple active entries
      const hasOpenEntry = await prisma.timeEntry.findFirst({
        where: {
          assignedPersonnelId: assignmentId,
          clockOut: null,
        },
      })
      if (hasOpenEntry) {
        return NextResponse.json(
          { error: 'Worker already has an active time entry' },
          { status: 400 }
        )
      }

      // Determine next entry number (max + 1, capped at 3)
      const latestEntry = await prisma.timeEntry.findFirst({
        where: { assignedPersonnelId: assignmentId },
        orderBy: { entryNumber: 'desc' },
      })
      const nextEntryNumber = Math.min((latestEntry?.entryNumber ?? 0) + 1, 3)
      if (nextEntryNumber > 3) {
        return NextResponse.json(
          { error: 'Maximum number of time entries (3) reached' },
          { status: 400 }
        )
      }

      const result = await prisma.$transaction(async (tx) => {
        const timeEntry = await tx.timeEntry.create({
          data: {
            assignedPersonnelId: assignmentId,
            clockIn: new Date(),
            entryNumber: nextEntryNumber,
            isActive: true,
          },
        })

        await tx.assignedPersonnel.update({
          where: { id: assignmentId },
          data: { status: 'ClockedIn' },
        })

        return timeEntry
      })

      return NextResponse.json({ success: true, timeEntry: result })
    } else if (action === 'clock_out') {
      // Find the most recent open (active) entry
      const activeEntry = await prisma.timeEntry.findFirst({
        where: {
          assignedPersonnelId: assignmentId,
          clockOut: null,
        },
        orderBy: { entryNumber: 'desc' },
      })

      if (!activeEntry) {
        return NextResponse.json(
          { error: 'No active time entry found to clock out' },
          { status: 400 }
        )
      }

      const result = await prisma.$transaction(async (tx) => {
        const updated = await tx.timeEntry.update({
          where: { id: activeEntry.id },
          data: { clockOut: new Date(), isActive: false },
        })

        await tx.assignedPersonnel.update({
          where: { id: assignmentId },
          data: { status: 'ClockedOut' },
        })

        return updated
      })

      return NextResponse.json({ success: true, timeEntry: result })
    }

  } catch (error) {
    console.error('Error processing clock action:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
