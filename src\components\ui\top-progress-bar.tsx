"use client";

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

export const TopProgressBar = () => {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Start loading on route change
    setIsLoading(true);
    setProgress(0);

    let timer: ReturnType<typeof setInterval>;
    
    // Simulate progress
    timer = setInterval(() => {
      setProgress((oldProgress) => {
        if (oldProgress < 90) {
          return oldProgress + Math.random() * 10;
        }
        return oldProgress;
      });
    }, 100);

    // Complete loading after a short delay
    const completeTimer = setTimeout(() => {
      clearInterval(timer);
      setProgress(100);
      
      setTimeout(() => {
        setIsLoading(false);
        setProgress(0);
      }, 300);
    }, 500);

    return () => {
      clearInterval(timer);
      clearTimeout(completeTimer);
    };
  }, [pathname]);

  if (!isLoading) return null;

  return (
    <div
      className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-600 z-[9999] transition-all duration-300 ease-out"
      style={{ 
        width: `${progress}%`,
        boxShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
      }}
    />
  );
};