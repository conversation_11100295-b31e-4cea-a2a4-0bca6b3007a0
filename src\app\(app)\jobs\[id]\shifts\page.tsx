"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { format, isToday, isTomorrow, isYesterday, differenceInDays, parseISO } from "date-fns"
import { useUser } from "@/hooks/use-user"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"

import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Users,
  UserCheck,
  Calendar,
  Clock,
  MapPin,
  Building,
  Building2,
  AlertCircle,
  RefreshCw,
  Calendar as CalendarIcon,
  Briefcase,
  FileText,
  Eye,
  ExternalLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ef<PERSON>
} from "lucide-react"
import { CompanyAvatar } from "@/components/CompanyAvatar"
import { EnhancedStatusBadge as UnifiedStatusBadge, getFulfillmentStatus, getPriorityBadge } from "@/components/ui/enhanced-status-badge"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import Link from 'next/link'

import { calculateShiftRequirements, calculateAssignedWorkers } from "@/lib/worker-count-utils"
import { getShiftStatus, getShiftStatusDisplay } from '@/lib/shift-status';
import { UserRole, ShiftStatus } from '@/lib/types'
import { useJobs } from '@/hooks/use-api'
import { useQuery } from '@tanstack/react-query'
import { useEnhancedPerformance } from '@/hooks/use-enhanced-performance'
import { useCacheManagement } from '@/hooks/use-cache-management'
import { useNavigationPerformance } from '@/hooks/use-navigation-performance'
import { useHoverPrefetch } from '@/hooks/use-intelligent-prefetch'

// Update the date and time formatting functions
const formatSimpleDate = (date: string | Date) => {
  try {
    if (typeof date === 'string') {
      return format(parseISO(date), 'MM/dd/yyyy')
    }
    return format(date, 'MM/dd/yyyy')
  } catch (error) {
    console.error('Invalid date in formatSimpleDate:', date);
    return 'Invalid Date';
  }
}

const formatSimpleTime = (time: string | Date) => {
  try {
    if (typeof time === 'string') {
      return format(parseISO(time), 'hh:mm a')
    }
    return format(time, 'hh:mm a')
  } catch (error) {
    console.error('Invalid time in formatSimpleTime:', time);
    return 'Invalid Time';
  }
}

export default function JobShiftsPage() {
  const { user } = useUser()
  const router = useRouter()
  const params = useParams()
  const jobId = params.id as string
  
  const { smartPrefetch, prefetchForPage } = useEnhancedPerformance()
  const { refreshShifts, isDevelopment } = useCacheManagement()
  
  // Enhanced navigation performance
  const { navigateWithPrefetch, handleHover, cancelHover } = useNavigationPerformance({
    enableHoverPrefetch: true,
    enableRoutePreloading: true,
  })
  
  const { cancelHover: cancelIntelligentHover } = useHoverPrefetch()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("date")
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted on client side to prevent hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Get job data
  const { data: jobsData } = useJobs()
  const job = jobsData?.find((j: any) => j.id === jobId)

  // Fetch job-specific shifts data
  const { data: shifts = [], isLoading, isError, error, refetch } = useQuery({
    queryKey: ['jobShifts', jobId],
    queryFn: async () => {
      const response = await fetch(`/api/jobs/${jobId}/shifts`);
      if (!response.ok) {
        throw new Error('Failed to fetch job shifts');
      }
      const data = await response.json();
      return data.shifts || [];
    },
    enabled: !!user && !!jobId,
  });

  // Filter and sort shifts
  const filteredShifts = React.useMemo(() => {
    let filtered = shifts.filter((shift: any) => {
      const matchesSearch = searchTerm === "" || 
        shift.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shift.description?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesStatus = statusFilter === "all" || shift.status === statusFilter
      
      return matchesSearch && matchesStatus
    })

    // Sort shifts
    filtered.sort((a: any, b: any) => {
      if (sortBy === "date") {
        return new Date(b.date).getTime() - new Date(a.date).getTime() // Most recent first
      }
      return 0
    })

    return filtered
  }, [shifts, searchTerm, statusFilter, sortBy])

  const canManage = user?.role === 'Admin' || user?.role === 'CrewChief'

  // Performance optimization on mount - must be before conditional returns
  useEffect(() => {
    if (user) {
      smartPrefetch(`/jobs/${jobId}/shifts`);
    }
  }, [user, smartPrefetch, jobId]);

  // Show loading state if user is not loaded yet
  if (!user) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto text-center py-12">
          <p className="text-muted-foreground">Please log in to view job shifts.</p>
        </div>
      </main>
    )
  }

  const getDateBadge = (date: string | Date) => {
    const shiftDate = new Date(date)
    if (isToday(shiftDate)) {
      return <Badge className="bg-success text-white border-success text-xs">Today</Badge>
    }
    if (isTomorrow(shiftDate)) {
      return <Badge className="bg-info text-white border-info text-xs">Tomorrow</Badge>
    }
    if (isYesterday(shiftDate)) {
      return <Badge variant="secondary" className="text-xs">Yesterday</Badge>
    }
    return <Badge variant="outline" className="text-xs">{format(shiftDate, 'MMM d')}</Badge>
  }

  const handleShiftView = (shiftId: string) => {
    navigateWithPrefetch(`/shifts/${shiftId}`)
  }

  const handleShiftEdit = (shiftId: string) => {
    navigateWithPrefetch(`/shifts/${shiftId}/edit`)
  }

  const handleShiftHover = (shiftId: string) => {
    handleHover(`/shifts/${shiftId}`)
  }


  if (!mounted || isLoading) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push(`/jobs/${jobId}`)} className="text-gray-300 hover:text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Job
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Job Shifts</h1>
              <p className="text-muted-foreground">Loading shift data...</p>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="flex justify-between">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
    )
  }

  if (isError) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button variant="ghost" size="sm" onClick={() => router.push(`/jobs/${jobId}`)} className="text-gray-300 hover:text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Job
            </Button>
          </div>
          <div className="flex items-center justify-center min-h-[60vh]">
            <Alert className="max-w-md bg-destructive/20 border-destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Error loading job shifts: {error instanceof Error ? error.message : String(error) || 'Unknown error'}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => refetch()}
                  className="mt-2 w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </main>
    )
  }

  return (
    <main className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.push(`/jobs/${jobId}`)} className="text-gray-300 hover:text-white">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Job
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {job?.name} - Shifts
            </h1>
            <p className="text-muted-foreground">
              {filteredShifts.length} shift{filteredShifts.length !== 1 ? 's' : ''} found for this job
            </p>
          </div>
          <div className="flex gap-2">
            {canManage && (
              <Button 
                onClick={() => navigateWithPrefetch(`/jobs/${jobId}/shifts/new`)}
                onMouseEnter={() => handleHover(`/jobs/${jobId}/shifts/new`)}
                onMouseLeave={cancelHover}
                className="bg-primary hover:bg-primary/90"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Shift
              </Button>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card className="bg-card border-border">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-card-foreground">
                <Filter className="h-5 w-5 text-primary" />
                <span>Filters & Search</span>
              </CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => {
                  setSearchTerm("")
                  setStatusFilter("all")
                  setSortBy("date")
                }}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search shifts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Status</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {Object.values(ShiftStatus).map(status => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Sort By</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Date (Recent First)</SelectItem>
                    <SelectItem value="createdAt">Date Created</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shifts Grid */}
        {filteredShifts.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="relative">
              <CalendarIcon className="h-16 w-16 text-muted-foreground/50 mb-4" />
              <Clock className="h-8 w-8 text-blue-400 absolute -top-2 -right-2" />
            </div>
            <h3 className="text-lg font-medium mb-2">No shifts found</h3>
            <p className="text-muted-foreground text-center max-w-md">
              {searchTerm || statusFilter !== 'all' 
                ? "Try adjusting your filters to see more shifts."
                : "No shifts have been scheduled for this job yet."}
            </p>
            {canManage && !searchTerm && statusFilter === 'all' && (
              <Button
                onClick={() => navigateWithPrefetch(`/jobs/${jobId}/shifts/new`)}
                className="mt-4 bg-primary hover:bg-primary/90"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create First Shift
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredShifts.map((shift: any) => {
              const required = calculateShiftRequirements(shift);
              const assigned = calculateAssignedWorkers(shift);
              const fulfillmentStatus = getFulfillmentStatus(assigned, required);
              const daysUntil = differenceInDays(new Date(shift.date), new Date());
              const priorityStatus = getPriorityBadge(daysUntil);
              
              // Get actual shift status based on timing and completion
              const shiftStatus = getShiftStatus(shift);
              const displayStatus = getShiftStatusDisplay(shiftStatus);
              
              const isCompleted = shiftStatus.isCompleted;
              const hasTimesheet = shift.timesheets && shift.timesheets.length > 0;
              const timesheet = hasTimesheet ? shift.timesheets[0] : null;

              return (
                <Card
                  key={shift.id}
                  className={`group hover:shadow-lg transition-all duration-300 cursor-pointer card-consistent hover:border-primary/30 ${
                    shiftStatus.isLive
                      ? 'bg-error/10 border-error/40 hover:border-error/60 hover:shadow-error/20'
                      : isCompleted 
                      ? 'bg-success/10 border-success/40 hover:border-success/60 hover:shadow-success/20' 
                      : 'hover:border-primary/30 hover:shadow-primary/10'
                  }`}
                  onClick={() => handleShiftView(shift.id)}
                  onMouseEnter={() => handleShiftHover(shift.id)}
                  onMouseLeave={cancelHover}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            {getDateBadge(shift.date)}
                            <UnifiedStatusBadge status={shiftStatus.status as any} size="sm" />
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-secondary-consistent">
                              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="font-medium">{formatSimpleDate(shift.date)}</span>
                            </div>
                            <div className="flex items-center text-sm text-secondary-consistent">
                              <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span>{formatSimpleTime(shift.startTime)} - {formatSimpleTime(shift.endTime)}</span>
                            </div>
                            {shift.location && (
                              <div className="flex items-center text-sm text-secondary-consistent">
                                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                                <span className="truncate">{shift.location}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" className="h-8 w-8 p-0 interactive-hover">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="bg-popover border-border">
                            <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleShiftView(shift.id); }}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            {canManage && (
                              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleShiftEdit(shift.id); }}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Shift
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      {/* Description */}
                      {shift.description && (
                        <div className="bg-surface rounded-lg p-3 border border-border/50">
                          <p className="text-sm text-secondary-consistent">{shift.description}</p>
                        </div>
                      )}

                      {/* Worker Status */}
                      <div className="flex items-center justify-between pt-2 border-t border-border/30">
                        <div className="flex items-center text-sm gap-2">
                          <Users className="h-4 w-4 text-primary" />
                          <span className="text-secondary-consistent font-medium">
                            {assigned} of {required} Workers
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {isCompleted && hasTimesheet ? (
                            <Link
                              href={`/timesheets/${timesheet.id}`}
                              onClick={(e) => e.stopPropagation()}
                              className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-white bg-success/80 border border-success rounded-md hover:bg-success transition-colors"
                            >
                              <FileText className="h-3 w-3" />
                              <span>Timesheet</span>
                            </Link>
                          ) : !isCompleted ? (
                            <UnifiedStatusBadge 
                              status={fulfillmentStatus as any}
                              size="sm"
                            />
                          ) : null}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>
    </main>
  )
}
