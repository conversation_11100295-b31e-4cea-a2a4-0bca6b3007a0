'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Zap, Trash2, Download } from 'lucide-react';
import { ACTION_NAMES } from '@/lib/pdf-management-helpers';

interface ActionsTabProps {
  actionLoading: string | null;
  performAction: (action: string) => void;
  exportMetrics: (format: 'json' | 'csv') => void;
}

const ActionButton: React.FC<{ actionName: string; currentAction: string | null; onClick: () => void; variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | null | undefined; children: React.ReactNode; icon: React.ReactNode }> = ({ actionName, currentAction, onClick, variant, children, icon }) => (
  <Button
    onClick={onClick}
    disabled={currentAction === actionName}
    variant={variant}
    className="w-full"
  >
    {currentAction === actionName ? (
      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
    ) : (
      icon
    )}
    {children}
  </Button>
);

export const ActionsTab: React.FC<ActionsTabProps> = ({ actionLoading, performAction, exportMetrics }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>System Actions</CardTitle>
          <CardDescription>Perform system maintenance tasks</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <ActionButton
            actionName={ACTION_NAMES.OPTIMIZE_SYSTEM}
            currentAction={actionLoading}
            onClick={() => performAction(ACTION_NAMES.OPTIMIZE_SYSTEM)}
            icon={<Zap className="h-4 w-4 mr-2" />}
          >
            Optimize System
          </ActionButton>
          <ActionButton
            actionName={ACTION_NAMES.CLEAR_METRICS}
            currentAction={actionLoading}
            onClick={() => performAction(ACTION_NAMES.CLEAR_METRICS)}
            variant="outline"
            icon={<Trash2 className="h-4 w-4 mr-2" />}
          >
            Clear Performance Metrics
          </ActionButton>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Export Data</CardTitle>
          <CardDescription>Download system metrics and reports</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button onClick={() => exportMetrics('json')} variant="outline" className="w-full">
            <Download className="h-4 w-4 mr-2" />
            Export as JSON
          </Button>
          <Button onClick={() => exportMetrics('csv')} variant="outline" className="w-full">
            <Download className="h-4 w-4 mr-2" />
            Export as CSV
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
