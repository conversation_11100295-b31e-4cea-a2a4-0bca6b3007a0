---
description: Repository Information Overview
alwaysApply: true
---

# HandsOn Application Information

## Summary
A Next.js web application for workforce management, shift scheduling, and timesheet tracking. The application includes features for job management, shift assignments, time tracking, PDF generation, and payroll processing.

## Structure
- **src/app**: Next.js application routes and API endpoints
- **src/components**: React components organized by feature
- **src/lib**: Core utilities, API clients, and business logic
- **src/hooks**: Custom React hooks for data fetching and state management
- **src/features**: Feature-specific components and logic
- **prisma**: Database schema and migrations
- **pdf-templates**: PDF generation templates for timesheets
- **scripts**: Utility scripts for deployment and maintenance

## Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.9.x
**Runtime**: Node.js 20.x
**Framework**: Next.js 15.5.x
**Build System**: Next.js build system
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- **Database**: Prisma 6.15.x with PostgreSQL
- **Authentication**: NextAuth.js 4.24.x
- **UI**: Tailwind CSS 3.4.x, Radix <PERSON> components, Mantine 7.11.x
- **State Management**: React Query 5.51.x
- **PDF Generation**: pdf-lib 1.17.x, pdfkit 0.17.x, jspdf 3.0.x
- **Data Processing**: ExcelJS 4.4.x, XLSX 0.18.x
- **Form Handling**: React Hook Form 7.62.x, Zod 4.1.x
- **Real-time**: Pusher 5.2.x

**Development Dependencies**:
- ESLint 8.57.x
- Testing Library (React 16.3.x)
- Puppeteer 24.15.x

## Build & Installation
```bash
# Install dependencies
npm ci --legacy-peer-deps

# Development
npm run dev

# Production build
npm run build

# Start production server
npm run start:prod
```

## Docker
**Dockerfile**: Multi-stage build optimized for Next.js with Prisma
**Base Image**: node:20-slim
**Exposed Port**: 8080
**Build Command**: `npm run build:cloud-run`
**Start Command**: `node scripts/startup-with-migration.js`

## Testing
**Framework**: Testing Library with Jest
**Test Location**: No dedicated test directory found
**Configuration**: No specific test configuration found
**Run Command**: No specific test command found

## Database
**ORM**: Prisma
**Database**: PostgreSQL
**Schema**: Complex data model for workforce management including users, companies, jobs, shifts, timesheets

UserRoles: Admin, Manager, CrewChief, StageHand, CompanyUser, PendingUser


WorkerTypes: CrewChief, StageHand, ForkOperator, ReachForkOperator, Rigger


**Migration**: `npm run migrate` (development), `npm run migrate:prod` (production)

## Deployment
**Platform**: Google Cloud Run (indicated by cloudbuild.yaml)
**CI/CD**: Google Cloud Build
**Environment Variables**: Multiple .env files (.env.development, .env.production)
**Commands**:
```bash
npm run deploy
npm run deploy:cloudbuild
```