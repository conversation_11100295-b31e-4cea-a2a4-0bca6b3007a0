'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Crown,
  FileText,
  Briefcase,
  Calendar,
  Users,
  TrendingUp,
  AlertCircle,
  RefreshCw,
  Settings,
  BarChart3,
  Shield,
  Zap,
  Server,
  UserCheck,
  Clock,
  UserX,
} from "lucide-react";
import { useDashboardData } from '@/hooks/use-dashboard-data';
import { TimesheetSection } from '@/components/dashboard/timesheet-section';
import { RecentJobsSection } from '@/components/dashboard/recent-jobs-section';
import { ShiftsSection } from '@/components/dashboard/shifts-section';
import { UnifiedJobCard } from "@/components/unified-job-card";
import { DashboardPage } from '@/components/DashboardPage';
import { cn } from '@/lib/utils';

export function EnhancedAdminDashboard() {
  const router = useRouter();
  const [shiftsPage, setShiftsPage] = useState(1);
  // Assuming useDashboardData fetches data for timesheets, jobs, and shifts
  // The '5' likely refers to items per page for shifts, adjust if needed.
  const { timesheets, jobs, shifts, isLoading, isError, error, refetchAll } = useDashboardData(shiftsPage, 5);

  // --- Navigation Handlers ---
  const handleTimesheetClick = (timesheetId: string, shiftId: string) => {
    router.push(`/shifts/${shiftId}?tab=timesheet`);
  };

  const handleJobClick = (jobId: string) => {
    router.push(`/jobs/${jobId}`);
  };

  const handleShiftClick = (shiftId: string) => {
    router.push(`/shifts/${shiftId}`);
  };

  // --- Action Handlers (Placeholders) ---
  const handleApproveTimesheet = async (timesheetId: string) => {
    console.log('Approve timesheet:', timesheetId);
    // TODO: Implement actual approval logic and potentially refetch data
  };

  const handleRejectTimesheet = async (timesheetId: string) => {
    console.log('Reject timesheet:', timesheetId);
    // TODO: Implement actual rejection logic and potentially refetch data
  };

  // --- Data Calculation ---
  // Safely calculate summary metrics
  const totalJobs = jobs.data?.jobs?.length ?? 0;
  const activeJobs = jobs.data?.jobs?.filter((job: any) => job.status === 'Active')?.length ?? 0;
  const pendingTimesheets = timesheets.data?.meta?.pending ?? 0;
  const todaysShifts = shifts.data?.meta?.total ?? 0; // Assuming 'total' in meta means today's shifts

  const systemStatus = {
    activeUsers: 120, // Mock data
    clockedIn: 75, // Mock data
    onBreak: 15, // Mock data
    issues: 3, // Mock data
  };

  // --- Loading State ---
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-6 md:p-8 lg:p-10">
        <DashboardPage
          title={
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                <Crown className="h-6 w-6 text-white" />
              </div>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent font-bold text-2xl">
                Admin Dashboard
              </span>
            </div>
          }
          description="System overview, manage operations, and track performance"
        >
          {/* Loading Metrics Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="relative overflow-hidden shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <Skeleton className="h-4 w-24 rounded-md" />
                  <Skeleton className="h-8 w-8 rounded-lg" />
                </CardHeader>
                <CardContent className="pb-4">
                  <Skeleton className="h-8 w-16 mb-2 rounded-md" />
                  <Skeleton className="h-4 w-32 rounded-md" />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Loading Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-8">
              <Card className="shadow-sm">
                <CardHeader>
                  <Skeleton className="h-6 w-48 rounded-md" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <Skeleton key={i} className="h-20 w-full rounded-lg" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-8">
              <Card className="shadow-sm">
                <CardHeader>
                  <Skeleton className="h-6 w-32 rounded-md" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-16 w-full rounded-lg" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </DashboardPage>
      </div>
    );
  }

  // --- Error State ---
  if (isError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-6 md:p-8 lg:p-10">
        <DashboardPage
          title={
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                <Crown className="h-6 w-6 text-white" />
              </div>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent font-bold text-2xl">
                Admin Dashboard
              </span>
            </div>
          }
          description="System overview, manage operations, and track performance"
        >
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load dashboard data. Please check your connection or try again later.
              <Button variant="link" onClick={refetchAll} className="p-0 ml-2 text-blue-400 hover:text-blue-300">
                <RefreshCw className="h-4 w-4 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </DashboardPage>
      </div>
    );
  }

  // --- Main Dashboard Content ---
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-6 md:p-8 lg:p-10">
      <DashboardPage
        title={
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
              <Crown className="h-6 w-6 text-white" />
            </div>
            <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent font-bold text-2xl">
              Admin Dashboard
            </span>
          </div>
        }
        description="System overview, manage operations, and track performance with comprehensive insights"
        buttonText="Admin Settings"
        buttonAction={() => router.push('/admin/settings')}
      >
        {/* Top row: Metrics Summary & Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-10"> {/* Increased gap slightly */}

          {/* Summary Metrics Card */}
          <Card className="relative overflow-hidden bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01] lg:col-span-1">
            <CardHeader className="pb-3 pt-5 px-5 relative z-10"> {/* Adjusted padding */}
              <CardTitle className="text-base font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="relative z-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-4 px-5 pb-5"> {/* Adjusted padding */}
              {/* Total Jobs */}
              <div className="flex flex-col">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Total Jobs</span>
                <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">{totalJobs}</span>
              </div>

              {/* Active Jobs */}
              <div className="flex flex-col">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Active Jobs</span>
                <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">{activeJobs}</span>
              </div>

              {/* Pending Timesheets */}
              <div className="flex flex-col">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Pending Timesheets</span>
                <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">{pendingTimesheets}</span>
              </div>

              {/* Today's Shifts */}
              <div className="flex flex-col">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Today's Shifts</span>
                <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">{todaysShifts}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card className="bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg lg:col-span-2">
            <CardHeader className="pb-3 pt-5 px-5">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Zap className="h-5 w-5 text-yellow-500" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-3 px-5 pb-5">
              <Button
                variant="outline"
                className="w-full justify-start py-3 px-4 text-sm"
                onClick={() => router.push('/admin/users')}
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start py-3 px-4 text-sm"
                onClick={() => router.push('/admin/companies')}
              >
                <Shield className="h-4 w-4 mr-2" />
                Manage Companies
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start py-3 px-4 text-sm"
                onClick={() => router.push('/performance')}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                View Reports
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start py-3 px-4 text-sm"
                onClick={() => router.push('/admin/settings')}
              >
                <Settings className="h-4 w-4 mr-2" />
                System Settings
              </Button>
            </CardContent>
          </Card>

          {/* System Status Card */}
          <Card className="bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
            <CardHeader className="pb-3 pt-5 px-5">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Server className="h-5 w-5 text-green-500" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4 px-5 pb-5">
              <div className="flex items-center gap-2">
                <UserCheck className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-bold">{systemStatus.clockedIn}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Clocked In</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="font-bold">{systemStatus.onBreak}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">On Break</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                <div>
                  <div className="font-bold">{systemStatus.activeUsers}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Active Users</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <UserX className="h-5 w-5 text-red-500" />
                <div>
                  <div className="font-bold">{systemStatus.issues}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Reported Issues</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard Content: Two Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8"> {/* Consistent gap */}

          {/* Left Column: Timesheets & Jobs */}
          <div className="space-y-8">
            {/* Timesheets Pending Approval Section */}
            <TimesheetSection
              timesheets={timesheets.data?.timesheets || []}
              isLoading={timesheets.isLoading}
              error={timesheets.error}
              onTimesheetClick={handleTimesheetClick}
              onApproveTimesheet={handleApproveTimesheet}
              onRejectTimesheet={handleRejectTimesheet}
              className="bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01]" // Added hover effect
            />

            {/* Recent Jobs Section */}
            <RecentJobsSection
              jobs={jobs.data?.jobs || []}
              isLoading={jobs.isLoading}
              error={jobs.error}
              onJobClick={handleJobClick}
              className="bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01]" // Added hover effect
            />
            
            <Card className="bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5" />
                  Recent Jobs
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 gap-4">
                {jobs.data?.jobs?.slice(0, 3).map((job: any) => (
                  <UnifiedJobCard
                    key={job.id}
                    job={job}
                    onView={() => handleJobClick(job.id)}
                    onEdit={() => router.push(`/jobs/${job.id}/edit`)}
                    onDelete={() => console.log('delete job', job.id)}
                    variant="detailed"
                  />
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Right Column: Today's Shifts */}
          <div className="space-y-8">
            {/* Today's Shifts Section */}
            <ShiftsSection
              shifts={shifts.data?.shifts || []}
              isLoading={shifts.isLoading}
              error={shifts.error}
              meta={shifts.data?.meta}
              onShiftClick={handleShiftClick}
              onPageChange={setShiftsPage}
              className="bg-white/70 backdrop-blur-sm dark:bg-gray-900/70 border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01]" // Added hover effect
            />
          </div>
        </div>
      </DashboardPage>
    </div>
  );
}
