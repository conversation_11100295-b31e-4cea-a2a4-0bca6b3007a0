"use client";

import React from "react";
import { use<PERSON><PERSON>, use<PERSON>ontroller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

const employeeSchema = yup.object().shape({
  name: yup.string().min(2, "Name must be at least 2 characters").required("Name is required"),
  email: yup.string().email("Invalid email address").required("Email is required"),
  phone: yup.string().min(10, "Phone number must be at least 10 digits").required("Phone is required"),
  role: yup.string().oneOf(["<PERSON> Hand", "Crew Chief", "Admin"]).required("Role is required"),
  certifications: yup.string(),
});

type EmployeeFormData = yup.InferType<typeof employeeSchema>;

export default function AddEmployeePage() {
  const form = useForm<EmployeeFormData>({
    resolver: yupResolver(employeeSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      role: undefined,
      certifications: "",
    },
  });

  // --- useController fields ---
  const {
    field: nameField,
    fieldState: { error: nameError },
  } = useController({ name: "name", control: form.control });

  const {
    field: emailField,
    fieldState: { error: emailError },
  } = useController({ name: "email", control: form.control });

  const {
    field: phoneField,
    fieldState: { error: phoneError },
  } = useController({ name: "phone", control: form.control });

  const {
    field: roleField,
    fieldState: { error: roleError },
  } = useController({ name: "role", control: form.control });

  const {
    field: certificationsField,
  } = useController({ name: "certifications", control: form.control });

  const onSubmit = (data: EmployeeFormData) => {
    console.log("Form submitted:", data);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-lg bg-white rounded-2xl shadow-lg p-8 space-y-6">
        <h1 className="text-2xl font-bold text-gray-800 text-center">
          Add New Employee
        </h1>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              placeholder="Enter full name"
              {...nameField}
              className={cn(
                "transition-colors duration-200",
                nameError ? "border-red-500 focus-visible:ring-red-500" : nameField.value ? "border-green-500 focus-visible:ring-green-500" : ""
              )}
            />
            {nameError && (
              <p className="text-sm text-red-500 animate-fadeIn">
                {nameError.message}
              </p>
            )}
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter email"
              {...emailField}
              className={cn(
                "transition-colors duration-200",
                emailError ? "border-red-500 focus-visible:ring-red-500" : emailField.value ? "border-green-500 focus-visible:ring-green-500" : ""
              )}
            />
            {emailError && (
              <p className="text-sm text-red-500 animate-fadeIn">
                {emailError.message}
              </p>
            )}
          </div>

          {/* Phone */}
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              placeholder="Enter phone number"
              {...phoneField}
              className={cn(
                "transition-colors duration-200",
                phoneError ? "border-red-500 focus-visible:ring-red-500" : phoneField.value ? "border-green-500 focus-visible:ring-green-500" : ""
              )}
            />
            {phoneError && (
              <p className="text-sm text-red-500 animate-fadeIn">
                {phoneError.message}
              </p>
            )}
          </div>

          {/* Role */}
          <div className="space-y-2">
            <Label>Role</Label>
            <Select
              onValueChange={(value) => roleField.onChange(value)}
              defaultValue={roleField.value}
            >
              <SelectTrigger
                className={cn(
                  "transition-colors duration-200",
                  roleError ? "border-red-500 focus-visible:ring-red-500" : roleField.value ? "border-green-500 focus-visible:ring-green-500" : ""
                )}
              >
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Stage Hand">Stage Hand</SelectItem>
                <SelectItem value="Crew Chief">Crew Chief</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
              </SelectContent>
            </Select>
            {roleError && (
              <p className="text-sm text-red-500 animate-fadeIn">
                {roleError.message}
              </p>
            )}
          </div>

          {/* Certifications */}
          <div className="space-y-2">
            <Label htmlFor="certifications">Certifications</Label>
            <Textarea
              id="certifications"
              placeholder="List certifications (optional)"
              {...certificationsField}
              className="transition-colors duration-200"
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={!form.formState.isValid}
          >
            Add Employee
          </Button>
        </form>
      </div>
    </div>
  );
}
