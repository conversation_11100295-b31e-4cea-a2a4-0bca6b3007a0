import React, { useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CardTitle } from '@/components/ui/card';
import { Clock, Users, Coffee, CheckCircle, FileText, Eye, RefreshCw, Unlock } from 'lucide-react';
import Link from 'next/link';
import { TimesheetApprovalButton } from '@/components/timesheet-approval-button';
import { UnlockTimesheetDialog } from '@/components/unlock-timesheet-dialog';
import WorkerRequirementsManager from '@/components/worker-requirements-manager';
import EndShiftButton from '@/components/end-shift-button';
import type { RoleCode, TimesheetStatus, UserRole, Assignment } from '@/lib/types';

interface RoleStats {
    [key: string]: {
        assigned: number;
        required: number;
    };
}

interface TimeTrackingHeaderInfoProps {
    enableTimeTracking: boolean;
    totalAssigned: number;
    totalRequired: number;
    roleStats: RoleStats;
}

export const TimeTrackingHeaderInfo: React.FC<TimeTrackingHeaderInfoProps> = ({
    enableTimeTracking,
    totalAssigned,
    totalRequired,
    roleStats,
}) => (
    <div>
        <CardTitle className="flex items-center gap-2">
            {enableTimeTracking ? <Clock className="h-5 w-5" /> : <Users className="h-5 w-5" />}
            {enableTimeTracking ? 'Time Tracking & Worker Assignment' : 'Worker Assignment'}
        </CardTitle>
        <p className="text-sm text-muted-foreground mt-1">
            {enableTimeTracking
                ? 'Assign workers and track up to 3 in/out periods per worker with smart break management'
                : 'Assign workers to this shift and manage their roles.'}
        </p>
        <div className="flex items-center gap-4 mt-2">
            <div className="text-sm"><span className="font-medium">Total: {totalAssigned}/{totalRequired}</span></div>
            {Object.entries(roleStats).map(([roleCode, stats]) => (
                stats.required > 0 && (
                    <div key={roleCode} className="text-xs">
                        <Badge variant="outline" className="mr-1">{roleCode}</Badge>
                        <span className={stats.assigned === stats.required ? 'text-green-600' : 'text-orange-600'}>
                            {stats.assigned}/{stats.required}
                        </span>
                    </div>
                )
            ))}
        </div>
    </div>
);

interface TimeTrackingHeaderActionsProps {
    shiftId: string;
    roleStats: RoleStats;
    isTimeTrackingDisabled: boolean;
    isShiftCompleted: boolean;
    canFinalizeTimesheet: boolean;
    clockedInWorkersCount: number;
    workersForMasterEndShiftCount: number;
    assignedPersonnel: Assignment[];
    onRequirementsChange: (newRequirements: { roleCode: RoleCode; requiredCount: number }[]) => void;
    onAssignmentStructureChange: () => void;
    onMasterStartBreak: () => void;
    onMasterEndShift: () => void;
    onFinalizeTimesheet: () => void;
}

export const TimeTrackingHeaderActions: React.FC<TimeTrackingHeaderActionsProps> = ({
    shiftId,
    roleStats,
    isTimeTrackingDisabled,
    isShiftCompleted,
    canFinalizeTimesheet,
    clockedInWorkersCount,
    workersForMasterEndShiftCount,
    assignedPersonnel,
    onRequirementsChange,
    onAssignmentStructureChange,
    onMasterStartBreak,
    onMasterEndShift,
    onFinalizeTimesheet,
}) => {
    const validAssignedPersonnel = useMemo(() => {
        return assignedPersonnel
            .filter(p => p.user)
            .map(p => ({
                ...p,
                user: p.user as { name: string },
                timeEntries: p.timeEntries.map(te => ({
                    ...te,
                    clockIn: String(te.clockIn),
                    clockOut: te.clockOut ? String(te.clockOut) : undefined,
                })),
            }));
    }, [assignedPersonnel]);

    return (
        <div className="flex gap-2">
            <WorkerRequirementsManager
                shiftId={shiftId}
                requirements={Object.entries(roleStats).map(([roleCode, stats]) => ({ roleCode: roleCode as RoleCode, requiredCount: stats.required }))}
                onRequirementsUpdate={(newRequirements) => {
                    onRequirementsChange(newRequirements);
                    onAssignmentStructureChange();
                }}
                onAssignmentStructureChange={onAssignmentStructureChange}
                disabled={isTimeTrackingDisabled}
            />
            {!isShiftCompleted && (
                <>
                    <Button variant="outline" onClick={onMasterStartBreak} disabled={isTimeTrackingDisabled || clockedInWorkersCount === 0}>
                        <Coffee className="h-4 w-4 mr-2" />
                        Start Break All ({clockedInWorkersCount})
                    </Button>
                    <Button variant="destructive" onClick={onMasterEndShift} disabled={isTimeTrackingDisabled || workersForMasterEndShiftCount === 0}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        End All Shifts ({workersForMasterEndShiftCount})
                    </Button>
                    <EndShiftButton
                      shiftId={shiftId}
                      assignedPersonnel={validAssignedPersonnel}
                      disabled={isTimeTrackingDisabled}
                    />
                </>
            )}
        </div>
    );
};

interface TimeTrackingHeaderCompletedProps {
    timesheets: { id: string; status: string }[];
    userRole: UserRole;
    onRefresh: () => void;
    onRegeneratePdf: (timesheetId: string) => Promise<void>;
}

export const TimeTrackingHeaderCompleted: React.FC<TimeTrackingHeaderCompletedProps> = ({
    timesheets,
    userRole,
    onRefresh,
    onRegeneratePdf,
}) => (
    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
        <div className="flex items-center gap-2 px-3 sm:px-4 py-2 bg-green-900/20 border border-green-500/30 rounded-md text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span className="text-xs sm:text-sm font-medium">Shift Completed - Time Tracking Locked</span>
        </div>
        {timesheets && timesheets.length > 0 && (
            <div className="flex items-center gap-2">
                <TimesheetApprovalButton timesheetId={timesheets[0].id} status={timesheets[0].status as TimesheetStatus} />
                <Link href={`/timesheets/${timesheets[0].id}`} passHref><Button variant="outline" size="sm"><Eye className="h-4 w-4 mr-2" />View Timesheet</Button></Link>
                <Button variant="outline" size="sm" onClick={() => onRegeneratePdf(timesheets[0].id)}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate PDF
                </Button>
                {userRole === 'Admin' && (
                    <UnlockTimesheetDialog
                        timesheetId={timesheets[0].id}
                        onUnlock={onRefresh}
                        trigger={(<Button variant="outline" size="sm"><Unlock className="h-4 w-4 mr-2" />Unlock</Button>)}
                    />
                )}
            </div>
        )}
    </div>
);
