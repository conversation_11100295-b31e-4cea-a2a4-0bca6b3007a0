// @ts-nocheck
import { prisma } from '@/lib/prisma';
import ExcelJS from 'exceljs';
import path from 'path';
import fs from 'fs';
import { Buffer } from 'buffer';
import { getTimezoneOffset } from 'date-fns-tz';

// Enhanced configuration for Excel cell locations - aligned with PDF form fields
const config = {
  // Company Information (matching PDF form field names)
  companyName: 'B5',        // Company name field
  companyPhone: 'M6',       // Company phone field
  
  // Job Information (matching PDF form structure)
  jobNumber: 'L2',          // Job# field (matches PDF "job-number")
  jobName: 'J5',            // Job name field (matches PDF "job-value")
  jobDescription: 'I6',     // Job description field
  jobStartDate: 'M5',       // Job start date
  jobEndDate: 'N5',         // Job end date
  
  // Shift Information (matching PDF form structure)
  shiftLocation: 'J5',      // Location field (matches PDF "location")
  shiftDate: 'B6',          // Shift date field (matches PDF "date-value")
  shiftStartTime: 'C6',     // Shift start time
  shiftEndTime: 'D6',       // Shift end time
  shiftDescription: 'E9',   // Shift description
  shiftNotes: 'E10',        // Shift notes
  
  // Client Information (matching PDF signature fields)
  clientContact: 'K49',     // Client contact field
  clientSignature: 'K50',   // Client signature field
  
  // Stagehand Data Grid (aligned with PDF table structure)
  employeeData: {
    startRow: 12,           // Start stagehands at row 12 (after enhanced header)
    nameColumn: 1,          // A column - Stagehand Name (matches PDF "EMPLOYEE NAMERow")
    workerTypeColumn: 2,    // B column - Worker Type (matches PDF "SHRow")
    in1Column: 3,           // C column - Clock In #1 (matches PDF "IN1Row")
    out1Column: 4,          // D column - Clock Out #1 (matches PDF "OUT1Row")
    in2Column: 5,           // E column - Clock In #2 (matches PDF "IN2Row")
    out2Column: 6,          // F column - Clock Out #2 (matches PDF "OUT2Row")
    in3Column: 7,           // G column - Clock In #3 (matches PDF "IN3Row")
    out3Column: 8,          // H column - Clock Out #3 (matches PDF "OUT3Row")
    regularColumn: 9,       // I column - Regular Hours
    otColumn: 10,           // J column - Overtime Hours
    totalColumn: 11,        // K column - Total Hours
    notesColumn: 12,        // L column - Notes
  }
};

// Enhanced role mapping for better display
const roleMap: Record<string, string> = {
  'CC': 'Crew Chief',
  'SH': 'Stage Hand', 
  'FO': 'Fork Operator',
  'RFO': 'Reach Fork Operator',
  'RG': 'Rigger',
  'GL': 'General Labor',
  'SUP': 'Manager',
  'DEFAULT': 'Worker'
};

// Enhanced styling configuration
const styling = {
  headerFont: { name: 'Calibri', size: 12, bold: true },
  bodyFont: { name: 'Calibri', size: 10 },
  titleFont: { name: 'Calibri', size: 16, bold: true },
  colors: {
    headerBg: 'FF2F5597',      // Professional blue
    alternateRowBg: 'FFF8F9FA', // Light gray
    borderColor: 'FFD1D5DB',    // Light border
    totalRowBg: 'FFE5E7EB',     // Slightly darker for totals
  }
};

import { AuthenticatedUser } from '@/lib/types';

export async function generateTimesheetExcel(timesheetId: string, user: AuthenticatedUser): Promise<ExcelJS.Workbook> {
  const timesheet = await prisma.timesheet.findUnique({
    where: { id: timesheetId },
    include: {
      shift: {
        include: {
          job: { include: { company: true } },
          assignedPersonnel: {
            include: {
              user: true,
              timeEntries: { orderBy: { createdAt: 'asc' } },
            },
          },
        },
      },
    },
  });

  if (!timesheet || !timesheet.shift || !timesheet.shift.job) {
    throw new Error('Timesheet not found or data is incomplete');
  }

  // Manual authorization check
  const canAccess = user.role === 'Admin' || user.role === 'Staff' ||
                   (user.role === 'CompanyUser' && timesheet.shift.job.companyId === user.companyId) ||
                   (user.role === 'CrewChief' && timesheet.shift.assignedPersonnel.some(p => p.userId === user.id));

  if (!canAccess) {
    throw new Error('Access denied');
  }

  const workbook = new ExcelJS.Workbook();
  const templatePath = path.resolve(process.cwd(), 'pdf-templates/timesheet-template.xlsx');

  // Try to load existing template, create new if not available
  if (fs.existsSync(templatePath)) {
    try {
      await workbook.xlsx.readFile(templatePath);
    } catch (error) {
      console.warn('Failed to load template, creating new workbook:', error);
    }
  }
  
  const worksheet = workbook.getWorksheet(1) || workbook.addWorksheet('Timesheet');

  // Apply enhanced styling if this is a new worksheet
  if (!fs.existsSync(templatePath)) {
    setupWorksheetStyling(worksheet);
  }

  // Populate Company Information
  const company = timesheet.shift.job.company;
  worksheet.getCell(config.companyName).value = company.name;
  worksheet.getCell(config.companyPhone).value = company.phone || '';

  // Populate Job Information
  const job = timesheet.shift.job;
  worksheet.getCell(config.jobNumber).value = job.id || job.name; // Job ID or name as job number
  worksheet.getCell(config.jobName).value = job.name;
  worksheet.getCell(config.jobDescription).value = job.description || '';
  
  // Format job dates
  if (job.startDate) {
    const startDateCell = worksheet.getCell(config.jobStartDate);
    startDateCell.value = new Date(job.startDate);
    startDateCell.numFmt = 'mm/dd/yyyy';
  }
  if (job.endDate) {
    const endDateCell = worksheet.getCell(config.jobEndDate);
    endDateCell.value = new Date(job.endDate);
    endDateCell.numFmt = 'mm/dd/yyyy';
  }

  // Populate Shift Information
  const shift = timesheet.shift;
  worksheet.getCell(config.shiftLocation).value = shift.location || job.location || '';
  
  // Format shift date and times with timezone handling
  const timeZone = 'America/Los_Angeles';
  const dateCell = worksheet.getCell(config.shiftDate);
  const shiftDate = new Date(shift.date);
  dateCell.value = new Date(shiftDate.getTime() + getTimezoneOffset(timeZone, shiftDate));
  dateCell.numFmt = 'mm/dd/yyyy';
  
  if (shift.startTime) {
    const startTimeCell = worksheet.getCell(config.shiftStartTime);
    const startTime = new Date(shift.startTime);
    startTimeCell.value = new Date(startTime.getTime() + getTimezoneOffset(timeZone, startTime));
    startTimeCell.numFmt = 'h:mm AM/PM';
  }
  if (shift.endTime) {
    const endTimeCell = worksheet.getCell(config.shiftEndTime);
    const endTime = new Date(shift.endTime);
    endTimeCell.value = new Date(endTime.getTime() + getTimezoneOffset(timeZone, endTime));
    endTimeCell.numFmt = 'h:mm AM/PM';
  }
  
  worksheet.getCell(config.shiftDescription).value = shift.description || '';
  worksheet.getCell(config.shiftNotes).value = shift.notes || '';

  // Populate Client Information
  worksheet.getCell(config.clientContact).value = company.name || 'N/A';

  // Add client signature if available
  if (timesheet.company_signature && timesheet.company_signature.startsWith('data:image/')) {
    try {
      const parts = timesheet.company_signature.split(';base64,');
      const mimeType = parts[0].split('image/')[1];
      const base64Data = parts[1];
      
      if (mimeType === 'png' || mimeType === 'jpeg') {
        const imageId = workbook.addImage({
          buffer: Buffer.from(base64Data, 'base64'),
          extension: mimeType,
        });

        worksheet.addImage(imageId, {
          tl: { col: 10, row: 50 }, // Corresponds to K50
          ext: { width: 150, height: 75 }
        });
      } else {
        console.warn(`Unsupported signature image type for Excel: ${mimeType}`);
      }
    } catch (error) {
      console.warn('Failed to add signature to Excel:', error);
    }
  }

  // Calculate total hours for tracking
  let totalRegularHours = 0;
  let totalOTHours = 0;

  // Populate employee data with enhanced formatting
  timesheet.shift.assignedPersonnel.forEach((employee, index) => {
    if (!employee.user) return; // Skip if no user is assigned

    const row = config.employeeData.startRow + index;
    
    // Stagehand info with enhanced styling
    const nameCell = worksheet.getCell(row, config.employeeData.nameColumn);
    nameCell.value = employee.user.name;
    nameCell.font = styling.bodyFont;
    
    // Role mapping with better display names
    const roleCell = worksheet.getCell(row, config.employeeData.workerTypeColumn);
    roleCell.value = roleMap[employee.roleCode] || roleMap['DEFAULT'];
    roleCell.font = styling.bodyFont;

    // Sort time entries by entry number, handling nulls
    const sortedEntries = (employee.timeEntries || []).sort((a, b) => (a.entryNumber || 1) - (b.entryNumber || 1));
    
    // Calculate total hours for this employee
    let employeeTotalHours = 0;

    // Populate up to 3 time entry pairs with proper formatting
    sortedEntries.forEach((entry, entryIndex) => {
      if (entryIndex < 3 && entry.clockIn && entry.clockOut) { // Max 3 entries
        const clockInDate = new Date(entry.clockIn);
        const clockOutDate = new Date(entry.clockOut);
        const clockIn = new Date(clockInDate.getTime() + getTimezoneOffset(timeZone, clockInDate));
        const clockOut = new Date(clockOutDate.getTime() + getTimezoneOffset(timeZone, clockOutDate));
        const hours = (clockOutDate.getTime() - clockInDate.getTime()) / (1000 * 60 * 60);
        employeeTotalHours += hours;

        // Set the appropriate in/out columns based on entry number with enhanced formatting
        if (entryIndex === 0) { // First entry
          const in1Cell = worksheet.getCell(row, config.employeeData.in1Column);
          const out1Cell = worksheet.getCell(row, config.employeeData.out1Column);
          in1Cell.value = clockIn;
          in1Cell.numFmt = 'h:mm AM/PM';
          in1Cell.font = styling.bodyFont;
          out1Cell.value = clockOut;
          out1Cell.numFmt = 'h:mm AM/PM';
          out1Cell.font = styling.bodyFont;
        } else if (entryIndex === 1) { // Second entry
          const in2Cell = worksheet.getCell(row, config.employeeData.in2Column);
          const out2Cell = worksheet.getCell(row, config.employeeData.out2Column);
          in2Cell.value = clockIn;
          in2Cell.numFmt = 'h:mm AM/PM';
          in2Cell.font = styling.bodyFont;
          out2Cell.value = clockOut;
          out2Cell.numFmt = 'h:mm AM/PM';
          out2Cell.font = styling.bodyFont;
        } else if (entryIndex === 2) { // Third entry
          const in3Cell = worksheet.getCell(row, config.employeeData.in3Column);
          const out3Cell = worksheet.getCell(row, config.employeeData.out3Column);
          in3Cell.value = clockIn;
          in3Cell.numFmt = 'h:mm AM/PM';
          in3Cell.font = styling.bodyFont;
          out3Cell.value = clockOut;
          out3Cell.numFmt = 'h:mm AM/PM';
          out3Cell.font = styling.bodyFont;
        }
      }
    });

    // Calculate regular vs overtime (8 hours regular, rest OT)
    const regularHours = Math.min(employeeTotalHours, 8);
    const otHours = Math.max(employeeTotalHours - 8, 0);

    // Format hours columns with enhanced styling
    const regularCell = worksheet.getCell(row, config.employeeData.regularColumn);
    const otCell = worksheet.getCell(row, config.employeeData.otColumn);
    const totalCell = worksheet.getCell(row, config.employeeData.totalColumn);
    
    regularCell.value = Math.round(regularHours * 100) / 100;
    regularCell.numFmt = '0.00';
    regularCell.font = styling.bodyFont;
    
    otCell.value = Math.round(otHours * 100) / 100;
    otCell.numFmt = '0.00';
    otCell.font = styling.bodyFont;
    
    totalCell.value = Math.round(employeeTotalHours * 100) / 100;
    totalCell.numFmt = '0.00';
    totalCell.font = styling.bodyFont;

    // Apply alternating row colors for better readability
    if (index % 2 === 1) {
      for (let col = 1; col <= 12; col++) {
        const cell = worksheet.getCell(row, col);
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: styling.colors.alternateRowBg }
        };
      }
    }

    totalRegularHours += regularHours;
    totalOTHours += otHours;
  });

  // Add enhanced totals row
  if (timesheet.shift.assignedPersonnel.length > 0) {
    const totalsRow = config.employeeData.startRow + timesheet.shift.assignedPersonnel.length;
    
    // Total label with enhanced styling
    const labelCell = worksheet.getCell(totalsRow, config.employeeData.nameColumn);
    labelCell.value = 'TOTAL HOURS:';
    labelCell.font = { ...styling.headerFont, bold: true };
    labelCell.alignment = { horizontal: 'right', vertical: 'middle' };
    
    // Format totals with proper number formatting and styling
    const totalRegularCell = worksheet.getCell(totalsRow, config.employeeData.regularColumn);
    const totalOtCell = worksheet.getCell(totalsRow, config.employeeData.otColumn);
    const grandTotalCell = worksheet.getCell(totalsRow, config.employeeData.totalColumn);
    
    totalRegularCell.value = Math.round(totalRegularHours * 100) / 100;
    totalRegularCell.numFmt = '0.00';
    totalRegularCell.font = { ...styling.headerFont, bold: true };
    
    totalOtCell.value = Math.round(totalOTHours * 100) / 100;
    totalOtCell.numFmt = '0.00';
    totalOtCell.font = { ...styling.headerFont, bold: true };
    
    grandTotalCell.value = Math.round((totalRegularHours + totalOTHours) * 100) / 100;
    grandTotalCell.numFmt = '0.00';
    grandTotalCell.font = { ...styling.headerFont, bold: true };
    
    // Style the totals row with enhanced formatting
    for (let col = 1; col <= 12; col++) {
      const cell = worksheet.getCell(totalsRow, col);
      cell.font = { ...styling.headerFont, bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: styling.colors.totalRowBg }
      };
      cell.border = {
        top: { style: 'medium', color: { argb: styling.colors.borderColor } },
        left: { style: 'thin', color: { argb: styling.colors.borderColor } },
        bottom: { style: 'medium', color: { argb: styling.colors.borderColor } },
        right: { style: 'thin', color: { argb: styling.colors.borderColor } }
      };
    }
  }

  return workbook;
}

/**
 * Setup enhanced worksheet styling for new templates
 */
function setupWorksheetStyling(worksheet: ExcelJS.Worksheet): void {
  // Set column widths for optimal layout
  worksheet.columns = [
    { width: 20 }, // A - Stagehand Name
    { width: 15 }, // B - Role
    { width: 12 }, // C - Clock In 1
    { width: 12 }, // D - Clock Out 1
    { width: 12 }, // E - Clock In 2
    { width: 12 }, // F - Clock Out 2
    { width: 12 }, // G - Clock In 3
    { width: 12 }, // H - Clock Out 3
    { width: 12 }, // I - Regular Hours
    { width: 12 }, // J - Overtime Hours
    { width: 12 }, // K - Total Hours
    { width: 25 }, // L - Notes
    { width: 15 }, // M - Additional
    { width: 15 }, // N - Additional
  ];

  // Add title
  const titleCell = worksheet.getCell('A1');
  titleCell.value = 'PROFESSIONAL TIMESHEET REPORT';
  titleCell.font = { ...styling.titleFont, color: { argb: 'FFFFFFFF' } };
  titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
  titleCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: styling.colors.headerBg }
  };
  
  worksheet.mergeCells('A1:N1');
  worksheet.getRow(1).height = 25;

  // Set page setup
  worksheet.pageSetup = {
    paperSize: 9, // A4
    orientation: 'portrait',
    margins: {
      left: 0.7,
      right: 0.7,
      top: 0.75,
      bottom: 0.75,
      header: 0.3,
      footer: 0.3
    },
    printArea: 'A1:N50'
  };
}
