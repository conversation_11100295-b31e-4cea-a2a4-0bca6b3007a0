'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Bell, X, CheckCircle, AlertCircle, Info, AlertTriangle, WifiOff } from "lucide-react"
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { getPusherClient } from '@/lib/realtime'
import { useUser } from '@/hooks/use-user'
import { useToast } from "@/hooks/use-toast"
import { format } from 'date-fns'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useHasMounted } from '@/hooks/use-has-mounted'

// Add React JSX types
declare module 'react' {
  interface JSX {
    IntrinsicElements: {
      [elemName: string]: any;
    }
  }
}

type NotificationType = 'success' | 'error' | 'warning' | 'info'
type NotificationPriority = 'low' | 'medium' | 'high'

interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  timestamp: Date
  read: boolean
  actionUrl?: string
  category?: string
  priority?: NotificationPriority
  metadata?: {
    assignmentId?: string
    acceptUrl?: string
    declineUrl?: string
    roleCode?: string
    relatedShiftId?: string
  } | null
}

interface NotificationEventData {
  type: NotificationType
  title: string
  message: string
  priority?: NotificationPriority
  actionUrl?: string
  metadata?: Notification['metadata']
}


// CSS animations in globals.css:
// @keyframes wiggle {
//   0%, 100% { transform: rotate(0deg); }
//   25% { transform: rotate(-10deg); }
//   75% { transform: rotate(10deg); }
// }
// @keyframes pulse-ring {
//   0% { transform: scale(0.8); opacity: 0.5; }
//   100% { transform: scale(1.2); opacity: 0; }
// }

// Custom hook for realtime notifications
function useRealtimeNotifications(initialNotifications: Notification[] = []) {
  const { user } = useUser();
  const { data: fetchedNotifications, error } = useQuery({
    queryKey: ['notifications', user?.id],
    queryFn: async () => {
      if (!user) return [];
      try {
        const response = await fetch('/api/notifications');
        if (!response.ok) throw new Error('Failed to fetch');
        const data = await response.json();
        const list = Array.isArray(data)
          ? data
          : (Array.isArray((data as any)?.notifications) ? (data as any).notifications : []);
        return list;
      } catch (e) {
        return []; // Return empty array on error
      }
    },
    enabled: !!user,
  });

  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications)
  const [isConnected, setIsConnected] = useState(false);
  const { toast } = useToast()

  useEffect(() => {
    if (fetchedNotifications) {
      const source = Array.isArray(fetchedNotifications) ? fetchedNotifications : [];
      const notificationsWithDateObjects = source.map((n: any) => {
        const rawDate = n.createdAt ?? n.timestamp;
        const timestamp = new Date(rawDate);
        return {
          ...n,
          // Normalize fields from API ({ createdAt, isRead }) to UI shape ({ timestamp, read })
          timestamp: !isNaN(timestamp.getTime()) ? timestamp : new Date(),
          read: typeof n.read === 'boolean' ? n.read : !!n.isRead,
        } as any;
      });
      setNotifications(notificationsWithDateObjects);
    }
  }, [fetchedNotifications]);

  const handleNotification = useCallback((data: NotificationEventData, category?: string) => {
    const notification: Notification = {
      id: Math.random().toString(36).substr(2, 9),
      type: data.type || 'info',
      title: data.title,
      message: data.message,
      timestamp: new Date(),
      read: false,
      category,
      priority: data.priority || 'medium',
      actionUrl: data.actionUrl,
      metadata: data.metadata
    }
    
    setNotifications((prev: Notification[]) => [notification, ...prev])
    
    // Show toast for high priority notifications
    if (notification.priority === 'high') {
      toast({
        title: notification.title,
        description: notification.message,
        variant: notification.type === 'error' ? 'destructive' : 'default',
      })
    }
  }, [toast])

  useEffect(() => {
    if (!user) return;

    const client = getPusherClient();
    if (!client) return;

    const channel = client.subscribe(`user-${user.id}`);

    const handleConnectionChange = () => {
      console.log('Pusher connection state:', client.connection.state);
      setIsConnected(client.connection.state === 'connected');
    };

    client.connection.bind('connected', handleConnectionChange);
    client.connection.bind('disconnected', handleConnectionChange);
    client.connection.bind('error', (err: any) => {
      console.error('Pusher connection error:', err);
    });

    channel.bind('notification', (data: NotificationEventData) => {
      console.log('Received notification:', data);
      handleNotification(data, 'general');
    });

    // Initialize state
    setIsConnected(client.connection.state === 'connected');

    return () => {
      client.connection.unbind('connected', handleConnectionChange);
      client.connection.unbind('disconnected', handleConnectionChange);
      client.connection.unbind('error');
      client.unsubscribe(`user-${user.id}`);
    };
  }, [user, handleNotification]);

  return {
    notifications,
    setNotifications,
    isConnected
  }
}

const getNotificationIcon = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case 'error':
      return <AlertCircle className="h-4 w-4 text-red-600" />
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    case 'info':
      return <Info className="h-4 w-4 text-blue-600" />
  }
}

const getNotificationBadgeColor = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return 'bg-green-100 text-green-800'
    case 'error':
      return 'bg-red-100 text-red-800'
    case 'warning':
      return 'bg-yellow-100 text-yellow-800'
    case 'info':
      return 'bg-blue-100 text-blue-800'
  }
}

export default function EnhancedNotificationCenter() {
  const { notifications, setNotifications, isConnected } = useRealtimeNotifications([])
  const [isOpen, setIsOpen] = useState(false)
  const hasMounted = useHasMounted()
  const queryClient = useQueryClient();

  const unreadCount = notifications.filter(n => !n.read).length

  const shiftActionMutation = useMutation({
    mutationFn: ({ url, assignmentId }: { url: string, assignmentId: string }) => {
      return fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ assignmentId })
      }).then(res => {
        if (!res.ok) {
          return res.json().then(errorData => {
            throw new Error(errorData.error || 'Failed to perform action');
          });
        }
        return res.json();
      });
    },
    onSuccess: (data, variables) => {
      // Optimistically remove the notification
      setNotifications(prev => prev.filter(n => n.metadata?.assignmentId !== variables.assignmentId));
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error) => {
      console.error('Shift action failed:', error);
    }
  });

  const markReadMutation = useMutation({
    mutationFn: (notificationIds: string[]) => {
      return fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationIds }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error) => {
      console.error('Failed to mark notification as read:', error);
    }
  });

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
    markReadMutation.mutate([id]);
  }

  const markAllAsRead = () => {
    const unreadIds = notifications.filter(n => !n.read).map(n => n.id);
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    )
    markReadMutation.mutate(unreadIds);
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className="relative hover:bg-muted/50 transition-colors"
        >
          <Bell className="h-5 w-5" />
          {!isConnected && (
            <WifiOff className="h-3 w-3 text-destructive absolute -top-1 -right-1" />
          )}
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs animate-in zoom-in-50 duration-300"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg">Notifications</CardTitle>
                {!isConnected && (
                  <Badge variant="outline" className="text-destructive border-destructive">
                    <WifiOff className="h-3 w-3 mr-1" />
                    Offline
                  </Badge>
                )}
              </div>
              {unreadCount > 0 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={markAllAsRead}
                  className="text-xs hover:bg-muted/50"
                >
                  Mark all read
                </Button>
              )}
            </div>
            {unreadCount > 0 && (
              <CardDescription>
                You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </CardDescription>
            )}
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[400px]">
              {!hasMounted ? (
                <div className="p-4 text-center text-muted-foreground">
                  Loading...
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  No notifications
                </div>
              ) : (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`group p-3 border-b last:border-b-0 hover:bg-muted/50 cursor-pointer transition-colors ${
                        !notification.read ? 'bg-muted/20' : ''
                      }`}
                      onClick={() => {
                        markAsRead(notification.id)
                        if (notification.actionUrl) {
                          window.location.href = notification.actionUrl
                        }
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="text-sm font-medium truncate">
                              {notification.title}
                            </p>
                            {!notification.read && (
                              <div className="h-2 w-2 bg-blue-600 rounded-full flex-shrink-0 animate-pulse" />
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">
                            {notification.message}
                          </p>
                          {/* Action buttons for up-for-grabs shift notifications */}
                          {notification?.metadata?.acceptUrl && notification?.metadata?.assignmentId ? (
                            <div className="flex items-center gap-2 mb-2">
                              <Button 
                                size="sm" 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  shiftActionMutation.mutate({
                                    url: notification.metadata!.acceptUrl!,
                                    assignmentId: notification.metadata!.assignmentId!
                                  });
                                }}
                              >Accept Shift</Button>
                              {notification?.metadata?.declineUrl && (
                                <Button 
                                  size="sm" 
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    shiftActionMutation.mutate({
                                      url: notification.metadata!.declineUrl!,
                                      assignmentId: notification.metadata!.assignmentId!
                                    });
                                  }}
                                >Decline</Button>
                              )}
                            </div>
                          ) : null}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant="secondary" 
                                className={`text-xs ${getNotificationBadgeColor(notification.type)}`}
                              >
                                {notification.type}
                              </Badge>
                              {notification.category && (
                                <Badge variant="outline" className="text-xs">
                                  {notification.category}
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {format(notification.timestamp, 'MMM d, HH:mm')}
                            </span>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeNotification(notification.id)
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
}
