import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UserRole, PayrollPeriodStatus } from '@prisma/client';
import { z } from 'zod';

const createPeriodSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
});

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);
    if (!user || user.role !== UserRole.Admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const periods = await prisma.payrollPeriod.findMany({
      include: {
        entries: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                payrollType: true,
                payrollBaseRateCents: true,
              }
            }
          }
        }
      },
      orderBy: { startDate: 'desc' }
    });

    return NextResponse.json({ periods });
  } catch (error) {
    console.error('Error fetching payroll periods:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);
    if (!user || user.role !== UserRole.Admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await req.json();
    const { startDate, endDate } = createPeriodSchema.parse(body);

    // Check for overlapping periods
    const existing = await prisma.payrollPeriod.findFirst({
      where: {
        OR: [
          {
            AND: [
              { startDate: { lte: new Date(startDate) } },
              { endDate: { gte: new Date(startDate) } }
            ]
          },
          {
            AND: [
              { startDate: { lte: new Date(endDate) } },
              { endDate: { gte: new Date(endDate) } }
            ]
          },
          {
            AND: [
              { startDate: { gte: new Date(startDate) } },
              { endDate: { lte: new Date(endDate) } }
            ]
          }
        ]
      }
    });

    if (existing) {
      return NextResponse.json(
        { error: 'Overlapping payroll period exists' },
        { status: 400 }
      );
    }

    const period = await prisma.payrollPeriod.create({
      data: {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status: PayrollPeriodStatus.OPEN,
      }
    });

    return NextResponse.json({ period });
  } catch (error) {
    console.error('Error creating payroll period:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}