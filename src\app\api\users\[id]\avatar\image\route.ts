import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

function initialsFromName(name?: string | null) {
  if (!name) return 'U';
  const parts = name.trim().split(/\s+/).filter(Boolean);
  const first = parts[0]?.[0] ?? '';
  const last = parts.length > 1 ? parts[parts.length - 1][0] : '';
  return (first + last || first || 'U').toUpperCase();
}

function svgPlaceholder(name?: string | null) {
  const initials = initialsFromName(name);
  const bg = '#6b7280'; // gray-500
  const fg = '#ffffff';
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
  <svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 128 128">
    <rect width="128" height="128" rx="64" fill="${bg}" />
    <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="Inter, Arial, sans-serif" font-size="56" fill="${fg}">${initials}</text>
  </svg>`;
  return Buffer.from(svg);
}

async function findUserByFlexibleId(idParam: string) {
  // 1) Try by primary key id
  let user = await prisma.user.findUnique({
    where: { id: idParam },
    select: { id: true, email: true, name: true, avatarUrl: true },
  });
  if (user) return user;

  // 2) Try direct email match
  user = await prisma.user.findUnique({
    where: { email: idParam },
    select: { id: true, email: true, name: true, avatarUrl: true },
  });
  if (user) return user;

  // 3) Try matching sanitized email (email without '@')
  // We can't express REPLACE(email,'@','') in Prisma, so fetch small candidate set and compare in JS.
  const candidates = await prisma.user.findMany({
    where: {
      // Heuristic: limit to rows that could plausibly match to avoid full scans on large tables
      OR: [
        { email: { contains: '.' } },
        { email: { contains: idParam } },
      ],
    },
    select: { id: true, email: true, name: true, avatarUrl: true },
    take: 50, // keep it small
  });

  return candidates.find((u) => (u.email || '').replace('@', '') === idParam) || null;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const user = await findUserByFlexibleId(id);

    if (!user) {
      return new NextResponse('Not Found', { status: 404 });
    }

    if (!user.avatarUrl) {
      // Serve a lightweight SVG placeholder with initials to avoid broken images
      const svg = svgPlaceholder(user.name);
      return new NextResponse(svg, {
        status: 200,
        headers: {
          'Content-Type': 'image/svg+xml',
          'Cache-Control': 'public, max-age=600', // 10 minutes
        },
      });
    }

    // Serve base64 data-url stored in DB
    if (user.avatarUrl.startsWith('data:')) {
      const base64Data = user.avatarUrl.split(',')[1];
      if (!base64Data) {
        // Fallback to placeholder if data is malformed
        const svg = svgPlaceholder(user.name);
        return new NextResponse(svg, {
          status: 200,
          headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'public, max-age=600',
          },
        });
      }
      const imageBuffer = Buffer.from(base64Data, 'base64');
      const mimeType = user.avatarUrl.match(/data:(.*);base64,/)?.[1] || 'image/png';

      return new NextResponse(imageBuffer, {
        status: 200,
        headers: {
          'Content-Type': mimeType,
          'Cache-Control': 'public, max-age=31536000, immutable',
        },
      });
    }

    // If avatarUrl is an external URL, proxy it through to keep a consistent endpoint
    if (user.avatarUrl.startsWith('http')) {
      const res = await fetch(user.avatarUrl);
      if (!res.ok) {
        // Fallback to placeholder
        const svg = svgPlaceholder(user.name);
        return new NextResponse(svg, {
          status: 200,
          headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'public, max-age=600',
          },
        });
      }
      const arrayBuf = await res.arrayBuffer();
      const buf = Buffer.from(arrayBuf);
      const contentType = res.headers.get('content-type') || 'image/png';
      return new NextResponse(buf, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=600',
        },
      });
    }

    // Otherwise treat it as a relative/static path under public
    // For simplicity, redirect to it so Next can serve it directly if valid
    return NextResponse.redirect(new URL(user.avatarUrl, request.url));
  } catch (error) {
    console.error(`Error fetching avatar for user ${id}:`, error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
