import {
  Assignment,
  User,
  UserWithAssignments,
  CrewChiefPermission,
  ShiftWithDetails,
  Job,
  Company,
  TimesheetDetails,
  Notification,
  Announcement,
} from '@/lib/types';
import { RequestInit } from 'next/dist/server/web/spec-extension/request';
import { getSession } from 'next-auth/react';
import { logAPIError } from '@/lib/error-handler';

const API_BASE_URL = '/api';

async function fetchFromApi<T>(url: string, options: RequestInit = {}): Promise<T> {
  const startTime = performance.now();
  const fullUrl = `${API_BASE_URL}${url}`;
  const method = options.method || 'GET';
  
  console.log(`🌐 [API Service] Starting ${method} ${url}`, {
    url: fullUrl,
    method,
    hasBody: !!options.body,
    timestamp: new Date().toISOString()
  });

  try {
    const headers = new Headers(options.headers);

    if (typeof window !== 'undefined') {
      const session = await getSession();
      if (session?.accessToken) {
        headers.set('Authorization', `Bearer ${session.accessToken}`);
        console.log(`🔐 [API Service] Using session token for ${url}`);
      } else {
        console.warn(`⚠️ [API Service] No session token for ${url}`);
      }
    }

    if (!headers.has('Content-Type') && options.body) {
      headers.set('Content-Type', 'application/json');
    }

    const response = await fetch(fullUrl, {
      ...options,
      headers,
      credentials: 'same-origin',
    });

    const duration = performance.now() - startTime;
    
    if (!response.ok) {
      console.error(`❌ [API Service] Request failed:`, {
        url: fullUrl,
        method,
        status: response.status,
        statusText: response.statusText,
        duration: `${duration.toFixed(2)}ms`,
        timestamp: new Date().toISOString()
      });

      let errorData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        try {
          errorData = await response.json();
          console.error(`📝 [API Service] Error response body:`, errorData);
        } catch (parseError) {
          console.error(`🚫 [API Service] Failed to parse error response:`, parseError);
          errorData = { error: `API call failed: ${response.statusText}` };
        }
      } else {
        errorData = { error: `API call failed: ${response.statusText}` };
      }

      // Log with our enhanced error logger
      logAPIError(url, method, {
        status: response.status,
        statusText: response.statusText,
        message: errorData.error || `API call failed: ${response.statusText}`,
        responseData: errorData
      }, options.body ? JSON.parse(options.body as string) : undefined);

      throw new Error(errorData.error || `API call failed: ${response.statusText}`);
    }

    console.log(`✅ [API Service] Request successful:`, {
      url: fullUrl,
      method,
      status: response.status,
      duration: `${duration.toFixed(2)}ms`,
      timestamp: new Date().toISOString()
    });

    const data = await response.json();
    console.log(`📦 [API Service] Response data for ${url}:`, data);
    
    return data;
  } catch (error) {
    const duration = performance.now() - startTime;
    
    // Log network/other errors
    console.error(`🔥 [API Service] Unexpected error:`, {
      url: fullUrl,
      method,
      error: error.message,
      duration: `${duration.toFixed(2)}ms`,
      timestamp: new Date().toISOString(),
      stack: error.stack
    });

    logAPIError(url, method, error, options.body ? JSON.parse(options.body as string) : undefined);
    throw error;
  }
}

// --- API Service Definitions ---

export const apiService = {
  // Shifts
  getShift: (shiftId: string) => fetchFromApi<{ shift: ShiftWithDetails; success?: boolean }>(`/shifts/${shiftId}`)
    .then(data => {
      console.log(`[API Service] Raw shift response:`, data);
      // Handle both response formats: { shift: ... } and { success: true, shift: ... }
      return data.shift || data;
    }),
  getShifts: (filters: { 
    date?: string; 
    status?: string; 
    companyId?: string; 
    search?: string; 
    jobId?: string; 
    fetchAll?: boolean; 
    crewChiefId?: string; 
    workerId?: string; 
  } = {}) => {
    const params = new URLSearchParams();
    if (filters.date && filters.date !== 'all') params.append('date', filters.date);
    if (filters.status && filters.status !== 'all') params.append('status', filters.status);
    if (filters.companyId && filters.companyId !== 'all') params.append('companyId', filters.companyId);
    if (filters.search) params.append('search', filters.search);
    
    // Ensure jobId is properly handled
    if (filters.jobId) {
      params.append('jobId', filters.jobId);
      // Add a cache-busting parameter for job-specific queries
      params.append('_t', Date.now().toString());
      console.log(`🔍 [API Service] Requesting shifts for jobId: ${filters.jobId}`);
    }
    
    if (filters.fetchAll) params.append('fetchAll', 'true');
    if (filters.crewChiefId) params.append('crewChiefId', filters.crewChiefId);
    if (filters.workerId) params.append('workerId', filters.workerId);
    
    const url = `/shifts?${params.toString()}`;
    console.log(`🌐 [API Service] Making request to: ${url}`);
    console.log(`📋 [API Service] Filters:`, filters);
    
    return fetchFromApi<{ shifts: ShiftWithDetails[] }>(`/shifts?${params.toString()}`)
      .then(data => {
        console.log(`✅ [API Service] Received ${data.shifts.length} shifts`);
        if (filters.jobId) {
          console.log(`🎯 [API Service] Shifts for job ${filters.jobId}:`, data.shifts.map(s => ({ id: s.id, jobId: s.jobId || 'N/A', status: s.status })));
        }
        return data.shifts;
      })
      .catch(error => {
        console.error(`❌ [API Service] Error fetching shifts:`, error);
        throw error;
      });
  },
  getShiftAssignments: (shiftId: string) => fetchFromApi<{ assignments: Assignment[] }>(`/shifts/${shiftId}/assigned`).then(data => data.assignments),
  updateShiftNotes: (shiftId: string, notes: string) => fetchFromApi<void>(`/shifts/${shiftId}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ notes }) }),
  assignWorker: (
    shiftId: string,
    userId: string,
    roleCode: string,
    ignoreConflicts = false,
    replaceAssignmentId?: string
  ) =>
    fetchFromApi<void>(`/shifts/${shiftId}/assign-worker`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId, roleCode, ignoreConflicts, replaceAssignmentId })
    }),
  checkSchedulingConflicts: (shiftId: string, employeeId: string) => fetchFromApi<{ hasConflicts: boolean; conflicts: any[] }>(`/shifts/${shiftId}/check-conflicts`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ employeeId }) }),
  unassignWorker: (shiftId: string, assignmentId: string) => fetchFromApi<void>(`/shifts/${shiftId}/assigned/${assignmentId}`, { method: 'DELETE' }),
  clockIn: (shiftId: string, assignmentId: string) => fetchFromApi<void>(`/shifts/${shiftId}/assigned/${assignmentId}/clock`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ action: 'clock_in' }) }),
  clockOut: (shiftId: string, assignmentId: string) => fetchFromApi<void>(`/shifts/${shiftId}/assigned/${assignmentId}/clock`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ action: 'clock_out' }) }),
  endWorkerShift: (shiftId: string, assignmentId: string) => fetchFromApi<void>(`/shifts/${shiftId}/end-worker-shift`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ workerId: assignmentId }) }),
  endShift: (shiftId: string) => fetchFromApi<void>(`/shifts/${shiftId}/end-shift`, { method: 'POST' }),

  markNoShow: (shiftId: string, assignmentId: string) => fetchFromApi<void>(`/shifts/${shiftId}/assigned/${assignmentId}/no-show`, { method: 'POST' }),
  masterStartBreak: (shiftId: string) => fetchFromApi<void>(`/shifts/${shiftId}/master-start-break`, { method: 'POST' }),
  masterEndShift: (shiftId: string) => fetchFromApi<void>(`/shifts/${shiftId}/master-end-shift`, { method: 'POST' }),
  finalizeTimesheet: (shiftId: string) => fetchFromApi<{timesheetId: string}>(`/timesheets/${shiftId}/finalize`, { method: 'POST' }),

  // Users & Stagehands
  getUsers: (params?: { page?: number; pageSize?: number; fetchAll?: boolean; role?: string; search?: string; status?: 'active' | 'inactive'; excludeCompanyUsers?: boolean }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.pageSize) searchParams.append('limit', params.pageSize.toString());
    if (params?.fetchAll) searchParams.append('fetchAll', 'true');
    if (params?.role) searchParams.append('role', params.role);
    if (params?.search) searchParams.append('search', params.search);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.excludeCompanyUsers) searchParams.append('excludeCompanyUsers', 'true');
    return fetchFromApi<{ users: User[]; pagination: any }>(`/users?${searchParams.toString()}`);
  },
  getUserById: (id: string) => fetchFromApi<{ user: UserWithAssignments }>(`/users/${id}`).then(data => data.user),
  getAvailableStageHands: () => fetchFromApi<{ users: User[] }>('/users?role=StageHand').then(data => data.users),

  // Crew Chief
  getCrewChiefPermissions: (shiftId: string) => fetchFromApi<{ permissions: CrewChiefPermission[] }>(`/crew-chief-permissions/manage?permissionType=shift&targetId=${shiftId}`).then(data => data.permissions),

  // Jobs
  getJob: (id: string) => fetchFromApi<{ job: Job }>(`/jobs/${id}`).then(data => data.job),
  getJobs: (filters: { status?: string; companyId?: string; search?: string; sortBy?: string; fetchAll?: boolean; } = {}) => {
    const params = new URLSearchParams();
    if (filters.status && filters.status !== 'all') params.append('status', filters.status);
    if (filters.companyId && filters.companyId !== 'all') params.append('companyId', filters.companyId);
    if (filters.search) params.append('search', filters.search);
    if (filters.sortBy) params.append('sortBy', filters.sortBy);
    if (filters.fetchAll) params.append('fetchAll', 'true');
    return fetchFromApi<{ success: boolean; jobs: Job[], pagination: any; timestamp: string; count: number }>(`/jobs?${params.toString()}`)
      .then(data => ({
        jobs: data.jobs,
        pagination: data.pagination
      }));
  },
  deleteJob: (id: string) => fetchFromApi<{ success: boolean }>(`/jobs/${id}`, { method: 'DELETE' }),

  // Companies
  getCompanies: (filters?: { page?: number; pageSize?: number; search?: string; fetchAll?: boolean; }) => {
    const params = new URLSearchParams();
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.pageSize) params.append('pageSize', filters.pageSize.toString());
    if (filters?.search) params.append('search', filters.search);
    if (filters?.fetchAll) params.append('fetchAll', 'true');
    return fetchFromApi<{ companies: Company[]; pagination: any }>(`/companies?${params.toString()}`);
  },
  getCompany: (id: string) => fetchFromApi<{ company: Company }>(`/companies/${id}`).then(data => data.company),

  // Timesheets
  getTimesheet: (id: string) => fetchFromApi<{ timesheet: TimesheetDetails }>(`/timesheets/${id}`).then(data => data.timesheet),
  getTimesheets: (filters?: { status?: string; fetchAll?: boolean; companyId?: string; userId?: string; }) => {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.fetchAll) params.append('fetchAll', 'true');
    if (filters?.companyId) params.append('companyId', filters.companyId);
    if (filters?.userId) params.append('userId', filters.userId);
    return fetchFromApi<{ success: boolean; timesheets: TimesheetDetails[] }>(`/timesheets?${params.toString()}`).then(data => data.timesheets);
  },

  // Notifications
  getNotifications: () => fetchFromApi<{ notifications: Notification[] }>('/notifications').then(data => data.notifications),

  // Announcements
  getAnnouncements: () => fetchFromApi<{ announcements: Announcement[] }>('/announcements').then(data => data.announcements),

  // Dashboards
  getCompanyDashboard: (companyId: string) => fetchFromApi<any>(`/companies/${companyId}/dashboard`),
  getCrewChiefDashboard: (userId: string) => fetchFromApi<any>(`/crew-chief/${userId}/dashboard`),
  getStageHandDashboard: (userId: string) => fetchFromApi<any>(`/stagehands/${userId}/dashboard`),
  getAdminDashboard: () => fetchFromApi<any>('/admin/dashboard'),
};

export type ApiService = typeof apiService;
