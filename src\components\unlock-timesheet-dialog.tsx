"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { Unlock, AlertTriangle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useMutation } from '@tanstack/react-query';

interface UnlockTimesheetDialogProps {
  timesheetId: string
  onUnlock?: () => void
  trigger?: React.ReactNode
}

export function UnlockTimesheetDialog({ 
  timesheetId, 
  onUnlock,
  trigger 
}: UnlockTimesheetDialogProps) {
  const [open, setOpen] = useState(false)
  const [reason, setReason] = useState('')
  const [notes, setNotes] = useState('')
  const { toast } = useToast()

  const mutation = useMutation({
    mutationFn: () => {
      return fetch(`/api/timesheets/${timesheetId}/unlock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: reason.trim(),
          notes: notes.trim() || undefined,
        }),
      }).then(res => {
        if (!res.ok) {
          return res.json().then(errorData => {
            throw new Error(errorData.error || 'Failed to unlock timesheet');
          });
        }
        return res.json();
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Timesheet has been unlocked successfully.",
      });
      setOpen(false);
      setReason('');
      setNotes('');
      onUnlock?.();
    },
    onError: (error: any) => {
      console.error('Error unlocking timesheet:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to unlock timesheet",
        variant: "destructive",
      });
    }
  });

  const handleUnlock = () => {
    if (!reason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for unlocking this timesheet.",
        variant: "destructive",
      })
      return
    }
    mutation.mutate();
  }

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Unlock className="h-4 w-4 mr-2" />
      Unlock
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Unlock className="h-5 w-5" />
            Unlock Timesheet
          </DialogTitle>
          <DialogDescription>
            This will revert the timesheet back to draft status, clearing all approvals and signatures.
          </DialogDescription>
        </DialogHeader>

        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Warning:</strong> This action will remove all approvals, signatures, and generated PDFs. 
            The timesheet will need to go through the approval process again.
          </AlertDescription>
        </Alert>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="reason">
              Reason for unlocking <span className="text-red-500">*</span>
            </Label>
            <Input
              id="reason"
              placeholder="e.g., Incorrect hours, missing break time, etc."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={mutation.isPending}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Any additional context or instructions..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              disabled={mutation.isPending}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => setOpen(false)}
            disabled={mutation.isPending}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleUnlock}
            disabled={mutation.isPending || !reason.trim()}
          >
            {mutation.isPending ? 'Unlocking...' : 'Unlock Timesheet'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
