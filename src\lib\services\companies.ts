import { prisma } from '../prisma';
import type { Prisma, Company, User } from '@prisma/client';
import { UserRole } from '@/lib/types';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { hasAnyRole } from '../role-check';

export async function getAllCompanies(
  user: User,
  { page = 1, pageSize = 10, search = '', companyId, userId }: { page?: number; pageSize?: number; search?: string, companyId?: string, userId?: string }
) {
  const where: Prisma.CompanyWhereInput = {};

  if (search) {
    where.name = {
      contains: search,
      mode: 'insensitive',
    };
  }

  if (user.role === UserRole.CompanyUser) {
    where.id = user.companyId;
  } else if (user.role === UserRole.CrewChief || user.role === UserRole.StageHand) {
    const assignedShifts = await prisma.assignedPersonnel.findMany({
      where: { userId: user.id },
      select: { shift: { select: { job: { select: { companyId: true } } } } },
    });
    const companyIds = Array.from(new Set(assignedShifts.map(s => s.shift.job.companyId)));
    where.id = { in: companyIds };
  }

  const total = await prisma.company.count({ where });
  const companies = await prisma.company.findMany({
    where,
    orderBy: {
      name: 'asc',
    },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      company_logo_url: true,
      _count: {
        select: { users: true, jobs: true },
      },
    },
    skip: (page - 1) * pageSize,
    take: pageSize,
  });

  return {
    companies,
    pagination: {
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    },
  };
}

export async function getCompanyById(id: string): Promise<Partial<Company> | null> {
  return prisma.company.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      address: true,
      phone: true,
      email: true,
      users: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      },
      jobs: {
        select: {
          id: true,
          name: true,
          status: true,
          startDate: true,
        },
        orderBy: {
          startDate: 'desc',
        },
      },
    },
  });
}

export async function createCompany(
  user: User,
  data: {
    name: string;
    address?: string;
    email?: string;
    phone?: string;
    company_logo_url?: string; // Add company_logo_url
    contact_name: string;
    contact_email: string;
    contact_phone?: string;
  }
): Promise<Company> {
  if (!hasAnyRole({ ...user, company: null }, [UserRole.Admin])) {
    throw new Error('Not authorized to create a company');
  }
  return prisma.$transaction(async (tx: Prisma.TransactionClient) => {
    const company = await tx.company.create({
      data: {
        name: data.name,
        address: data.address,
        email: data.email,
        phone: data.phone,
        company_logo_url: data.company_logo_url, // Save company_logo_url
      },
    });

    // Generate a secure, random password for the new company user
    const temporaryPassword = crypto.randomBytes(12).toString('hex');
    const hashedPassword = await bcrypt.hash(temporaryPassword, 12);

    await tx.user.create({
      data: {
        name: data.contact_name,
        email: data.contact_email,
        passwordHash: hashedPassword,
        role: UserRole.CompanyUser,
        companyId: company.id,
      },
    });

    return company;
  });
}

export async function updateCompany(id: string, data: Partial<Company>): Promise<Company> {
  return prisma.company.update({
    where: { id },
    data: {
      ...data,
      // Ensure company_logo_url can be explicitly set to null if needed
      company_logo_url: data.company_logo_url === undefined ? undefined : data.company_logo_url,
    },
  });
}

export async function deleteCompany(user: User, id: string): Promise<void> {
  if (!hasAnyRole({ ...user, company: null }, [UserRole.Admin])) {
    throw new Error('Not authorized to delete a company');
  }
  // The cascading delete rule in the schema will handle deleting related users.
  await prisma.company.delete({ where: { id } });
}

export async function getCompanyDashboardData(companyId: string) {
  const activeJobsCount = await prisma.job.count({
    where: {
      companyId,
      status: 'Active' 
    }
  });

  const upcomingShiftsCount = await prisma.shift.count({
    where: {
      job: { companyId },
      date: { gte: new Date() }
    }
  });

  const completedShiftsCount = await prisma.shift.count({
    where: {
      job: { companyId },
      date: { lt: new Date() }
    }
  });

  const recentJobs = await prisma.job.findMany({
    where: { companyId },
    include: { company: { select: { name: true } } },
    orderBy: { createdAt: 'desc' },
    take: 3
  });

  const upcomingShifts = await prisma.shift.findMany({
    where: {
      job: { companyId },
      date: { gte: new Date() }
    },
    include: {
      job: { include: { company: { select: { name: true } } } }
    },
    orderBy: { date: 'asc' },
    take: 3
  });

  // Convert Date objects to ISO strings
  const serializedUpcomingShifts = upcomingShifts.map(shift => ({
    ...shift,
    date: shift.date.toISOString(),
    createdAt: shift.createdAt.toISOString(),
    updatedAt: shift.updatedAt.toISOString(),
    startTime: shift.startTime.toISOString()
  }));

  const serializedRecentJobs = recentJobs.map(job => ({
    ...job,
    createdAt: job.createdAt.toISOString(),
    updatedAt: job.updatedAt.toISOString(),
    startDate: job.startDate?.toISOString(),
    endDate: job.endDate?.toISOString()
  }));

  return {
    activeJobsCount,
    upcomingShiftsCount,
    completedShiftsCount,
    recentJobs: serializedRecentJobs,
    upcomingShifts: serializedUpcomingShifts
  };
}
