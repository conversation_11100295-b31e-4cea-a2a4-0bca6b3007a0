import { describe, it, expect, beforeEach } from 'vitest';
import { UserRole } from '@prisma/client';
import { 
  applyShiftScope,
  applyJobScope,
  applyTimesheetScope,
  applyTimeEntryScope
} from '@/lib/permissions';
import { AuthenticatedUser } from '@/lib/types';

describe('Database Query Scoping', () => {
  let adminUser: AuthenticatedUser;
  let managerUser: AuthenticatedUser;
  let crewChiefUser: AuthenticatedUser;
  let stageHandUser: AuthenticatedUser;
  let companyUser: AuthenticatedUser;

  beforeEach(() => {
    adminUser = {
      id: 'admin-1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: UserRole.Admin,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;

    managerUser = {
      id: 'manager-1',
      name: 'Manager User',
      email: '<EMAIL>',
      role: UserRole.Manager,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;

    crewChiefUser = {
      id: 'crew-1',
      name: 'Crew Chief',
      email: '<EMAIL>',
      role: UserRole.CrewChief,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;

    stageHandUser = {
      id: 'stage-1',
      name: 'Stage Hand',
      email: '<EMAIL>',
      role: UserRole.StageHand,
      companyId: null,
      company: null,
    } as AuthenticatedUser;

    companyUser = {
      id: 'company-1',
      name: 'Company User',
      email: '<EMAIL>',
      role: UserRole.CompanyUser,
      companyId: 'company-1',
      company: { id: 'company-1', name: 'Test Company' },
    } as AuthenticatedUser;
  });

  describe('Shift Scoping', () => {
    it('should not apply scoping for admin and manager users', () => {
      const baseWhere = { status: 'Active' };
      
      expect(applyShiftScope(adminUser, baseWhere)).toEqual(baseWhere);
      expect(applyShiftScope(managerUser, baseWhere)).toEqual(baseWhere);
    });

    it('should scope shifts by company for company users', () => {
      const baseWhere = { status: 'Active' };
      const result = applyShiftScope(companyUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          { job: { companyId: 'company-1' } }
        ]
      });
    });

    it('should scope shifts by company history for crew chiefs', () => {
      const baseWhere = { status: 'Active' };
      const result = applyShiftScope(crewChiefUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          {
            job: {
              shifts: {
                some: {
                  assignedPersonnel: {
                    some: {
                      userId: 'crew-1',
                    },
                  },
                },
              },
            },
          },
        ]
      });
    });

    it('should scope shifts by assignment for stage hands', () => {
      const baseWhere = { status: 'Active' };
      const result = applyShiftScope(stageHandUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          { assignedPersonnel: { some: { userId: 'stage-1' } } }
        ]
      });
    });

    it('should deny access for unknown roles', () => {
      const unknownUser = { ...stageHandUser, role: 'UnknownRole' as UserRole };
      const baseWhere = { status: 'Active' };
      const result = applyShiftScope(unknownUser, baseWhere);
      
      expect(result).toEqual({
        AND: [baseWhere, { id: { equals: '__DENY__' } }]
      });
    });
  });

  describe('Job Scoping', () => {
    it('should not apply scoping for admin and manager users', () => {
      const baseWhere = { status: 'Active' };
      
      expect(applyJobScope(adminUser, baseWhere)).toEqual(baseWhere);
      expect(applyJobScope(managerUser, baseWhere)).toEqual(baseWhere);
    });

    it('should scope jobs by company for company users', () => {
      const baseWhere = { status: 'Active' };
      const result = applyJobScope(companyUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          { companyId: 'company-1' }
        ]
      });
    });

    it('should scope jobs by shift assignments for crew chiefs', () => {
      const baseWhere = { status: 'Active' };
      const result = applyJobScope(crewChiefUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          {
            shifts: {
              some: {
                assignedPersonnel: { some: { userId: 'crew-1' } },
              },
            },
          },
        ]
      });
    });

    it('should scope jobs by shift assignments for stage hands', () => {
      const baseWhere = { status: 'Active' };
      const result = applyJobScope(stageHandUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          {
            shifts: {
              some: {
                assignedPersonnel: { some: { userId: 'stage-1' } },
              },
            },
          },
        ]
      });
    });
  });

  describe('Timesheet Scoping', () => {
    it('should not apply scoping for admin and manager users', () => {
      const baseWhere = { status: 'COMPLETED' };
      
      expect(applyTimesheetScope(adminUser, baseWhere)).toEqual(baseWhere);
      expect(applyTimesheetScope(managerUser, baseWhere)).toEqual(baseWhere);
    });

    it('should scope timesheets by company for company users', () => {
      const baseWhere = { status: 'COMPLETED' };
      const result = applyTimesheetScope(companyUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          { shift: { job: { companyId: 'company-1' } } }
        ]
      });
    });

    it('should scope timesheets by company history for crew chiefs', () => {
      const baseWhere = { status: 'COMPLETED' };
      const result = applyTimesheetScope(crewChiefUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          {
            shift: {
              job: {
                shifts: {
                  some: {
                    assignedPersonnel: {
                      some: {
                        userId: 'crew-1',
                      },
                    },
                  },
                },
              },
            },
          },
        ]
      });
    });

    it('should scope timesheets by shift assignment for stage hands', () => {
      const baseWhere = { status: 'COMPLETED' };
      const result = applyTimesheetScope(stageHandUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          {
            shift: { assignedPersonnel: { some: { userId: 'stage-1' } } },
          },
        ]
      });
    });
  });

  describe('Time Entry Scoping', () => {
    it('should not apply scoping for admin and manager users', () => {
      const baseWhere = { verified: true };
      
      expect(applyTimeEntryScope(adminUser, baseWhere)).toEqual(baseWhere);
      expect(applyTimeEntryScope(managerUser, baseWhere)).toEqual(baseWhere);
    });

    it('should scope time entries for crew chiefs (own entries + crew chief shifts)', () => {
      const baseWhere = { verified: true };
      const result = applyTimeEntryScope(crewChiefUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          {
            OR: [
              // Their own time entries
              { assignedPersonnel: { userId: 'crew-1' } },
              // Time entries for shifts where they are crew chief
              { 
                assignedPersonnel: { 
                  shift: { 
                    assignedPersonnel: { 
                      some: { 
                        userId: 'crew-1', 
                        roleCode: 'CC' 
                      } 
                    } 
                  } 
                } 
              },
            ],
          },
        ]
      });
    });

    it('should scope time entries to own entries only for stage hands', () => {
      const baseWhere = { verified: true };
      const result = applyTimeEntryScope(stageHandUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          { assignedPersonnel: { userId: 'stage-1' } }
        ]
      });
    });

    it('should scope time entries by company for company users', () => {
      const baseWhere = { verified: true };
      const result = applyTimeEntryScope(companyUser, baseWhere);
      
      expect(result).toEqual({
        AND: [
          baseWhere,
          { assignedPersonnel: { shift: { job: { companyId: 'company-1' } } } }
        ]
      });
    });

    it('should deny access for unknown roles', () => {
      const unknownUser = { ...stageHandUser, role: 'UnknownRole' as UserRole };
      const baseWhere = { verified: true };
      const result = applyTimeEntryScope(unknownUser, baseWhere);
      
      expect(result).toEqual({
        AND: [baseWhere, { id: { equals: '__DENY__' } }]
      });
    });
  });

  describe('Empty Base Where Handling', () => {
    it('should handle empty base where clauses correctly', () => {
      expect(applyShiftScope(companyUser)).toEqual({
        AND: [
          {},
          { job: { companyId: 'company-1' } }
        ]
      });

      expect(applyJobScope(companyUser)).toEqual({
        AND: [
          {},
          { companyId: 'company-1' }
        ]
      });
    });
  });
});
