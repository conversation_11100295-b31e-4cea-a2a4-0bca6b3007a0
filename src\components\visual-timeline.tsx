"use client";

import React, { useMemo, useState, useCallback } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format, addDays, startOfWeek, isSameDay, parseISO } from "date-fns";
import { formatPacificTime, formatPacificTimeRange } from "@/lib/time-utils";
import { Dialog } from "@/components/ui/dialog";
import WorkerAssignmentsPanel from "@/components/worker-assignments-panel";
import { WORKER_TYPES } from "@/constants/worker-types";
import { RoleCode, ShiftWithDetails, User } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";

interface ShiftLite {
  id: string;
  startTime: string;
  endTime: string;
  date: string;
  assignedPersonnel?: Array<{ userId?: string; roleCode?: string; status?: string }>;
  [key: string]: any;
}

interface Props {
  job: { id: string; name: string; companyId: string; startDate?: string; endDate?: string };
  shifts: ShiftLite[];
  onRefresh: () => void;
}

// Lightweight lane assignment to avoid overlaps
function assignLanes(shifts: ShiftLite[]): ShiftLite[] {
  const sorted = [...shifts].sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
  const laneEnds = new Map<number, number>();
  sorted.forEach(s => {
    const sStart = new Date(s.startTime).getTime();
    const sEnd = new Date(s.endTime).getTime();
    for (let lane = 0; ; lane++) {
      const lastEnd = laneEnds.get(lane);
      if (lastEnd === undefined || sStart >= lastEnd) {
        (s as any).lane = lane;
        laneEnds.set(lane, sEnd);
        break;
      }
    }
  });
  return sorted;
}

export default function VisualTimeline({ job, shifts, onRefresh }: Props) {
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'day' | '3days' | 'week' | 'full'>('full');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedShiftId, setSelectedShiftId] = useState<string | null>(null);
  const [selectedShiftDetails, setSelectedShiftDetails] = useState<ShiftWithDetails | null>(null);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [panelOpen, setPanelOpen] = useState(false);

  const displayDays = useMemo(() => {
    try {
      const jobStart = job.startDate ? parseISO(job.startDate) : new Date();
      const jobEnd = job.endDate ? parseISO(job.endDate) : addDays(jobStart, 7);
      let start: Date, end: Date;
      switch (viewMode) {
        case 'day': start = new Date(currentDate); end = new Date(currentDate); break;
        case '3days': start = new Date(currentDate); end = addDays(currentDate, 2); break;
        case 'week': start = startOfWeek(currentDate, { weekStartsOn: 1 }); end = addDays(start, 6); break;
        case 'full': default: start = jobStart; end = jobEnd; break;
      }
      if (viewMode !== 'full') {
        if (start < jobStart) start = jobStart;
        if (end > jobEnd) end = jobEnd;
      }
      const days = [] as Date[]; let cur = new Date(start);
      while (cur <= end) { days.push(new Date(cur)); cur = addDays(cur, 1); }
      return days;
    } catch {
      return Array.from({ length: 7 }, (_, i) => addDays(new Date(), i));
    }
  }, [job.startDate, job.endDate, viewMode, currentDate]);

  const byDay = useMemo(() => {
    const map: Record<string, ShiftLite[]> = {};
    shifts.forEach(s => {
      const key = format(new Date(s.date), 'yyyy-MM-dd');
      map[key] = map[key] || [];
      map[key].push(s);
    });
    Object.keys(map).forEach(k => { map[k] = assignLanes(map[k]); });
    return map;
  }, [shifts]);

  const getTotals = (s: any) => {
    const required = Object.values(WORKER_TYPES).reduce((sum, cfg: any) => sum + (s[cfg.dbField] || 0), 0);
    const assigned = (s.assignedPersonnel || []).filter((p: any) => p.userId && p.status !== 'NoShow').length;
    return { required, assigned };
  };

  const handleOpenShift = useCallback(async (shiftId: string) => {
    try {
      setSelectedShiftId(shiftId);
      setPanelOpen(true);
      const [shiftRes, usersRes] = await Promise.all([
        fetch(`/api/shifts/${shiftId}?_t=${Date.now()}`, { cache: 'no-store', headers: { 'Cache-Control': 'no-store' } }),
        fetch(`/api/users?fetchAll=true${job.companyId ? `&companyId=${job.companyId}` : ''}`)
      ]);
      if (!shiftRes.ok) throw new Error('Failed to fetch shift details');
      if (!usersRes.ok) throw new Error('Failed to fetch users');
      const shiftJson = await shiftRes.json();
      const usersJson = await usersRes.json();
      setSelectedShiftDetails(shiftJson.shift);
      setAvailableUsers(usersJson.users || []);
    } catch (e: any) {
      toast({ title: 'Failed to open shift', description: e?.message || 'Please try again', variant: 'destructive' });
      setPanelOpen(false);
    }
  }, [job.companyId, toast]);

  const handleAssign = useCallback(async (assignmentIdOrPlaceholder: string, userId: string | null, roleCode: RoleCode) => {
    if (!selectedShiftId) return;
    try {
      if (userId) {
        await fetch(`/api/shifts/${selectedShiftId}/assign-worker`, {
          method: 'POST', headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ roleCode, userId, placeholderId: assignmentIdOrPlaceholder.startsWith('placeholder-') ? assignmentIdOrPlaceholder : undefined })
        });
      } else if (assignmentIdOrPlaceholder && !assignmentIdOrPlaceholder.startsWith('placeholder-')) {
        await fetch(`/api/shifts/${selectedShiftId}/assigned/${assignmentIdOrPlaceholder}`, { method: 'DELETE' });
      }
      onRefresh();
      // Refresh panel data
      const res = await fetch(`/api/shifts/${selectedShiftId}?_t=${Date.now()}`, { cache: 'no-store' });
      const json = await res.json();
      setSelectedShiftDetails(json.shift);
    } catch (e: any) {
      toast({ title: 'Assignment failed', description: e?.message || 'Please try again', variant: 'destructive' });
    }
  }, [selectedShiftId, onRefresh, toast]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Scheduling Timeline</h2>
          <p className="text-muted-foreground">{job.name}</p>
        </div>
        <div className="flex gap-2">
          <Button variant={viewMode === 'day' ? 'default' : 'outline'} onClick={() => setViewMode('day')}>Day</Button>
          <Button variant={viewMode === '3days' ? 'default' : 'outline'} onClick={() => setViewMode('3days')}>3 Days</Button>
          <Button variant={viewMode === 'week' ? 'default' : 'outline'} onClick={() => setViewMode('week')}>Week</Button>
          <Button variant={viewMode === 'full' ? 'default' : 'outline'} onClick={() => setViewMode('full')}>Full</Button>
        </div>
      </div>

      {displayDays.map(day => {
        const key = format(day, 'yyyy-MM-dd');
        const dayShifts = byDay[key] || [];
        return (
          <Card key={key} className="overflow-hidden">
            <CardHeader className="py-3">
              <CardTitle className="text-base">{format(day, 'EEEE, MMMM d, yyyy')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {dayShifts.map(shift => {
                  const { required, assigned } = getTotals(shift);
                  const widthPct = Math.min(100, required > 0 ? (assigned / required) * 100 : 0);
                  return (
                    <div key={shift.id} className="p-2 border rounded hover:bg-accent/50 cursor-pointer" onClick={() => handleOpenShift(shift.id)}>
                      <div className="flex justify-between text-sm mb-1">
                        <div className="font-medium">{shift.description || job.name}</div>
                        <div className="text-muted-foreground">{formatPacificTimeRange(shift.startTime, shift.endTime)}</div>
                      </div>
                      <div className="h-3 w-full bg-muted rounded relative overflow-hidden">
                        <div className="h-3 bg-primary rounded" style={{ width: `${widthPct}%` }} />
                      </div>
                      <div className="flex justify-between text-xs mt-1 text-muted-foreground">
                        <span>{assigned}/{required} workers</span>
                        <span>Click to manage assignments</span>
                      </div>
                    </div>
                  );
                })}
                {dayShifts.length === 0 && (
                  <div className="text-sm text-muted-foreground">No shifts</div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}

      {selectedShiftDetails && (
        <WorkerAssignmentsPanel
          open={panelOpen}
          onClose={() => setPanelOpen(false)}
          shift={selectedShiftDetails}
          users={availableUsers}
          onAssign={handleAssign}
        />
      )}
    </div>
  );
}