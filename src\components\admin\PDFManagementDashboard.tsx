'use client';

import React from 'react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RefreshCw, AlertTriangle } from 'lucide-react';

import { usePDFManagement } from '@/hooks/usePDFManagement';
import { DashboardHeader } from './pdf-dashboard-components/DashboardHeader';
import { SystemHealthAlert } from './pdf-dashboard-components/SystemHealthAlert';
import { OverviewStats } from './pdf-dashboard-components/OverviewStats';
import { PerformanceTab } from './pdf-dashboard-components/PerformanceTab';
import { CacheTab } from './pdf-dashboard-components/CacheTab';
import { RecommendationsTab } from './pdf-dashboard-components/RecommendationsTab';
import { ActionsTab } from './pdf-dashboard-components/ActionsTab';

export default function PDFManagementDashboard() {
  const {
    stats,
    loading,
    actionLoading,
    lastRefresh,
    fetchStats,
    performAction,
    exportMetrics,
  } = usePDFManagement();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading PDF management dashboard...</span>
      </div>
    );
  }

  if (!stats) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Failed to load PDF management statistics.</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <DashboardHeader lastRefresh={lastRefresh} onRefresh={fetchStats} />
      <SystemHealthAlert systemHealth={stats.systemHealth} />
      <OverviewStats overview={stats.overview} systemHealth={stats.systemHealth} />

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="cache">Cache Management</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="actions">System Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <PerformanceTab performance={stats.performance} />
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <CacheTab cache={stats.cache} actionLoading={actionLoading} performAction={performAction} />
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <RecommendationsTab recommendations={stats.recommendations} />
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <ActionsTab 
            actionLoading={actionLoading} 
            performAction={performAction} 
            exportMetrics={exportMetrics} 
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
