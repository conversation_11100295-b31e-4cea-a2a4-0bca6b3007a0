"use client"

import { useRouter } from 'next/navigation';
import { useSession, signIn, signOut } from 'next-auth/react';
import { UserRole, User as AuthUser } from '@/lib/types';
import { useMemo } from 'react';

export const useUser = () => {
  const router = useRouter();
  const { data: session, status } = useSession();

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });
      return !result?.error;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      await signOut({ redirect: false });
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const user: AuthUser | null = useMemo(() => {
    if (status === 'loading') return null;
    if (!session?.user) {
      return null;
    }

    const sessionUser = session.user as any;

    return {
      id: sessionUser.id,
      name: sessionUser.name ?? 'Unknown User',
      email: sessionUser.email ?? '',
      avatarUrl: sessionUser.avatarUrl || sessionUser.image || '',
      role: sessionUser.role as UserRole,
      isActive: true, // Assuming active if session exists
      companyId: sessionUser.companyId,
      company: sessionUser.company,
      upForGrabsNotifications: sessionUser.upForGrabsNotifications,
      OSHA_10_Certifications: sessionUser.OSHA_10_Certifications ?? false,
      location: sessionUser.location ?? null,
      passwordHash: sessionUser.passwordHash ?? null,
      crew_chief_eligible: sessionUser.crew_chief_eligible ?? false,
      fork_operator_eligible: sessionUser.fork_operator_eligible ?? false,
      certifications: sessionUser.certifications ?? [],
      performance: sessionUser.performance ?? null,
      phone: sessionUser.phone ?? null,
      payrollType: sessionUser.payrollType ?? 'HOURLY',
      payrollBaseRateCents: sessionUser.payrollBaseRateCents ?? 0,
      ssnEncrypted: sessionUser.ssnEncrypted ?? null,
      ssnLast4: sessionUser.ssnLast4 ?? null,
      addressLine1: sessionUser.addressLine1 ?? null,
      addressLine2: sessionUser.addressLine2 ?? null,
      city: sessionUser.city ?? null,
      state: sessionUser.state ?? null,
      postalCode: sessionUser.postalCode ?? null,
      createdAt: sessionUser.createdAt ? new Date(sessionUser.createdAt) : new Date(),
      updatedAt: sessionUser.updatedAt ? new Date(sessionUser.updatedAt) : new Date(),
    };
  }, [JSON.stringify(session?.user), status]);

  return {
    user,
    status,
    login,
    logout,
    isLoading: status === 'loading'
  };
};
