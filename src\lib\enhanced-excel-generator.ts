/**
 * Enhanced Excel Generator for Timesheets
 * 
 * This module provides an improved Excel timesheet generator with:
 * - Better field mapping alignment with PDF forms
 * - Enhanced styling and professional appearance
 * - Improved data validation and formatting
 * - Responsive layout that adapts to content
 */

import { prisma } from '@/lib/prisma';
import ExcelJS from 'exceljs';
import path from 'path';
import fs from 'fs';
import { Buffer } from 'buffer';
import { getTimezoneOffset } from 'date-fns-tz';
import { formatDateForDisplay, formatTimeForDisplay } from './date-utils';

// Enhanced configuration with better field mapping to match PDF form fields
const ENHANCED_CONFIG = {
  // Header Information (matching PDF form fields)
  header: {
    jobNumber: 'L2',        // Job# field
    jobName: 'B5',          // Job name/description
    customerName: 'B6',     // Customer/Company name
    location: 'J6',         // Location field
    shiftDate: 'B7',        // Date field
    startTime: 'E7',        // Start time
    endTime: 'G7',          // End time
    shiftDescription: 'B8', // Shift description/notes
  },
  
  // Stagehand data grid (aligned with PDF table structure)
  employeeGrid: {
    startRow: 11,           // Start after header section
    columns: {
      employeeName: 1,      // A - Stagehand Name (matches "EMPLOYEE NAME" in PDF)
      roleCode: 2,          // B - Role/Position (matches "SH" field in PDF)
      clockIn1: 3,          // C - First clock in (matches "IN1" in PDF)
      clockOut1: 4,         // D - First clock out (matches "OUT1" in PDF)
      clockIn2: 5,          // E - Second clock in (matches "IN2" in PDF)
      clockOut2: 6,         // F - Second clock out (matches "OUT2" in PDF)
      clockIn3: 7,          // G - Third clock in (matches "IN3" in PDF)
      clockOut3: 8,         // H - Third clock out (matches "OUT3" in PDF)
      regularHours: 9,      // I - Regular hours
      overtimeHours: 10,    // J - Overtime hours
      totalHours: 11,       // K - Total hours
      notes: 12,            // L - Stagehand notes/comments
    }
  },
  
  // Footer information
  footer: {
    companySignature: 'B45', // Company signature area
    managerSignature: 'H45', // Manager signature area
    totalRegularHours: 'I43', // Total regular hours
    totalOvertimeHours: 'J43', // Total overtime hours
    grandTotalHours: 'K43',   // Grand total hours
  },
  
  // Styling configuration
  styling: {
    headerFont: { name: 'Calibri', size: 12, bold: true },
    bodyFont: { name: 'Calibri', size: 10 },
    titleFont: { name: 'Calibri', size: 16, bold: true },
    colors: {
      headerBg: 'FF2F5597',      // Professional blue
      alternateRowBg: 'FFF8F9FA', // Light gray
      borderColor: 'FFD1D5DB',    // Light border
      totalRowBg: 'FFE5E7EB',     // Slightly darker for totals
    }
  }
};

// Role code mapping for better display
const ROLE_DISPLAY_MAP: Record<string, string> = {
  'CC': 'Crew Chief',
  'SH': 'Stage Hand',
  'FO': 'Fork Operator',
  'RFO': 'Reach Fork Operator',
  'RG': 'Rigger',
  'GL': 'General Labor',
  'SUP': 'Manager',
  'DEFAULT': 'Worker'
};

export class EnhancedExcelGenerator {
  private workbook: ExcelJS.Workbook;
  private worksheet: ExcelJS.Worksheet;
  private timeZone = 'America/Los_Angeles';

  constructor() {
    this.workbook = new ExcelJS.Workbook();
    this.worksheet = this.workbook.addWorksheet('Timesheet', {
      pageSetup: {
        paperSize: 9, // A4
        orientation: 'portrait',
        margins: {
          left: 0.7,
          right: 0.7,
          top: 0.75,
          bottom: 0.75,
          header: 0.3,
          footer: 0.3
        }
      }
    });
  }

  /**
   * Load existing template if available, otherwise create new workbook
   */
  private async loadTemplate(): Promise<void> {
    const templatePath = path.resolve(process.cwd(), 'pdf-templates/timesheet-template.xlsx');
    
    if (fs.existsSync(templatePath)) {
      try {
        await this.workbook.xlsx.readFile(templatePath);
        this.worksheet = this.workbook.getWorksheet(1) || this.workbook.addWorksheet('Timesheet');
      } catch (error) {
        console.warn('Failed to load existing template, creating new one:', error);
        // Continue with new workbook
      }
    }
  }

  /**
   * Set up the worksheet structure and styling
   */
  private setupWorksheetStructure(): void {
    // Set column widths for better layout
    this.worksheet.columns = [
      { width: 20 }, // A - Stagehand Name
      { width: 15 }, // B - Role
      { width: 12 }, // C - Clock In 1
      { width: 12 }, // D - Clock Out 1
      { width: 12 }, // E - Clock In 2
      { width: 12 }, // F - Clock Out 2
      { width: 12 }, // G - Clock In 3
      { width: 12 }, // H - Clock Out 3
      { width: 12 }, // I - Regular Hours
      { width: 12 }, // J - Overtime Hours
      { width: 12 }, // K - Total Hours
      { width: 25 }, // L - Notes
    ];

    // Create title section
    this.createTitleSection();
    
    // Create header information section
    this.createHeaderSection();
    
    // Create employee data table headers
    this.createTableHeaders();
  }

  /**
   * Create the title section
   */
  private createTitleSection(): void {
    const titleCell = this.worksheet.getCell('A1');
    titleCell.value = 'TIMESHEET REPORT';
    titleCell.font = ENHANCED_CONFIG.styling.titleFont;
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    
    // Merge cells for title
    this.worksheet.mergeCells('A1:L1');
    
    // Add border and background
    const titleRange = this.worksheet.getCell('A1');
    titleRange.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: ENHANCED_CONFIG.styling.colors.headerBg }
    };
    titleRange.font = { ...ENHANCED_CONFIG.styling.titleFont, color: { argb: 'FFFFFFFF' } };
    titleRange.border = {
      top: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
      left: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
      bottom: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
      right: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } }
    };
  }

  /**
   * Create header information section
   */
  private createHeaderSection(): void {
    // Job Number label and field
    this.worksheet.getCell('A3').value = 'Job #:';
    this.worksheet.getCell('A3').font = ENHANCED_CONFIG.styling.headerFont;
    
    // Customer label and field
    this.worksheet.getCell('A4').value = 'Customer:';
    this.worksheet.getCell('A4').font = ENHANCED_CONFIG.styling.headerFont;
    
    // Location label and field
    this.worksheet.getCell('G4').value = 'Location:';
    this.worksheet.getCell('G4').font = ENHANCED_CONFIG.styling.headerFont;
    
    // Date label and field
    this.worksheet.getCell('A5').value = 'Date:';
    this.worksheet.getCell('A5').font = ENHANCED_CONFIG.styling.headerFont;
    
    // Time labels and fields
    this.worksheet.getCell('D5').value = 'Start Time:';
    this.worksheet.getCell('D5').font = ENHANCED_CONFIG.styling.headerFont;
    
    this.worksheet.getCell('F5').value = 'End Time:';
    this.worksheet.getCell('F5').font = ENHANCED_CONFIG.styling.headerFont;
    
    // Description label and field
    this.worksheet.getCell('A6').value = 'Description:';
    this.worksheet.getCell('A6').font = ENHANCED_CONFIG.styling.headerFont;
  }

  /**
   * Create table headers for employee data
   */
  private createTableHeaders(): void {
    const headerRow = ENHANCED_CONFIG.employeeGrid.startRow - 1;
    const headers = [
      'Stagehand Name',
      'Role',
      'Clock In 1',
      'Clock Out 1', 
      'Clock In 2',
      'Clock Out 2',
      'Clock In 3',
      'Clock Out 3',
      'Regular Hrs',
      'Overtime Hrs',
      'Total Hrs',
      'Notes'
    ];

    headers.forEach((header, index) => {
      const cell = this.worksheet.getCell(headerRow, index + 1);
      cell.value = header;
      cell.font = ENHANCED_CONFIG.styling.headerFont;
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: ENHANCED_CONFIG.styling.colors.headerBg }
      };
      cell.font = { ...ENHANCED_CONFIG.styling.headerFont, color: { argb: 'FFFFFFFF' } };
      cell.border = {
        top: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        left: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        bottom: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        right: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } }
      };
    });
  }

  /**
   * Populate header information
   */
  private populateHeaderInfo(timesheet: any): void {
    const { shift } = timesheet;
    const { job } = shift;
    const { company } = job;

    // Job information
    this.worksheet.getCell(ENHANCED_CONFIG.header.jobNumber).value = job.name || job.id;
    this.worksheet.getCell(ENHANCED_CONFIG.header.customerName).value = company.name;
    this.worksheet.getCell(ENHANCED_CONFIG.header.location).value = shift.location || job.location || '';
    
    // Date and time information
    const shiftDate = new Date(shift.date);
    const dateCell = this.worksheet.getCell(ENHANCED_CONFIG.header.shiftDate);
    dateCell.value = shiftDate;
    dateCell.numFmt = 'mm/dd/yyyy';
    
    if (shift.startTime) {
      const startTimeCell = this.worksheet.getCell(ENHANCED_CONFIG.header.startTime);
      const startTime = new Date(shift.startTime);
      startTimeCell.value = startTime;
      startTimeCell.numFmt = 'h:mm AM/PM';
    }
    
    if (shift.endTime) {
      const endTimeCell = this.worksheet.getCell(ENHANCED_CONFIG.header.endTime);
      const endTime = new Date(shift.endTime);
      endTimeCell.value = endTime;
      endTimeCell.numFmt = 'h:mm AM/PM';
    }
    
    // Description
    this.worksheet.getCell(ENHANCED_CONFIG.header.shiftDescription).value = 
      shift.description || shift.notes || '';
  }

  /**
   * Populate stagehand data with enhanced formatting
   */
  private populateEmployeeData(timesheet: any): { totalRegular: number; totalOvertime: number } {
    const employees = timesheet.shift.assignedPersonnel || [];
    let totalRegularHours = 0;
    let totalOvertimeHours = 0;

    employees.forEach((employee: any, index: number) => {
      if (!employee.user) return;

      const row = ENHANCED_CONFIG.employeeGrid.startRow + index;
      const cols = ENHANCED_CONFIG.employeeGrid.columns;
      
      // Stagehand basic info
      this.worksheet.getCell(row, cols.employeeName).value = employee.user.name;
      this.worksheet.getCell(row, cols.roleCode).value = 
        ROLE_DISPLAY_MAP[employee.roleCode] || ROLE_DISPLAY_MAP.DEFAULT;
      
      // Sort time entries
      const sortedEntries = (employee.timeEntries || [])
        .sort((a: any, b: any) => (a.entryNumber || 1) - (b.entryNumber || 1));
      
      let employeeTotalHours = 0;
      
      // Populate time entries (up to 3 pairs)
      sortedEntries.slice(0, 3).forEach((entry: any, entryIndex: number) => {
        if (entry.clockIn && entry.clockOut) {
          const clockInDate = new Date(entry.clockIn);
          const clockOutDate = new Date(entry.clockOut);
          const hours = (clockOutDate.getTime() - clockInDate.getTime()) / (1000 * 60 * 60);
          employeeTotalHours += hours;

          // Set clock in/out times based on entry index
          const clockInCol = entryIndex === 0 ? cols.clockIn1 : 
                           entryIndex === 1 ? cols.clockIn2 : cols.clockIn3;
          const clockOutCol = entryIndex === 0 ? cols.clockOut1 : 
                            entryIndex === 1 ? cols.clockOut2 : cols.clockOut3;
          
          const clockInCell = this.worksheet.getCell(row, clockInCol);
          const clockOutCell = this.worksheet.getCell(row, clockOutCol);
          
          clockInCell.value = clockInDate;
          clockInCell.numFmt = 'h:mm AM/PM';
          clockOutCell.value = clockOutDate;
          clockOutCell.numFmt = 'h:mm AM/PM';
        }
      });

      // Calculate regular vs overtime hours
      const regularHours = Math.min(employeeTotalHours, 8);
      const overtimeHours = Math.max(employeeTotalHours - 8, 0);
      
      // Set hours with proper formatting
      const regularCell = this.worksheet.getCell(row, cols.regularHours);
      const overtimeCell = this.worksheet.getCell(row, cols.overtimeHours);
      const totalCell = this.worksheet.getCell(row, cols.totalHours);
      
      regularCell.value = Math.round(regularHours * 100) / 100;
      regularCell.numFmt = '0.00';
      overtimeCell.value = Math.round(overtimeHours * 100) / 100;
      overtimeCell.numFmt = '0.00';
      totalCell.value = Math.round(employeeTotalHours * 100) / 100;
      totalCell.numFmt = '0.00';
      
      // Add row styling
      this.styleEmployeeRow(row, index % 2 === 1);
      
      totalRegularHours += regularHours;
      totalOvertimeHours += overtimeHours;
    });

    return { totalRegular: totalRegularHours, totalOvertime: totalOvertimeHours };
  }

  /**
   * Style stagehand data rows
   */
  private styleEmployeeRow(row: number, isAlternate: boolean): void {
    const cols = ENHANCED_CONFIG.employeeGrid.columns;
    const maxCol = Math.max(...Object.values(cols));
    
    for (let col = 1; col <= maxCol; col++) {
      const cell = this.worksheet.getCell(row, col);
      cell.font = ENHANCED_CONFIG.styling.bodyFont;
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      
      if (isAlternate) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: ENHANCED_CONFIG.styling.colors.alternateRowBg }
        };
      }
      
      cell.border = {
        top: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        left: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        bottom: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        right: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } }
      };
    }
  }

  /**
   * Add totals row
   */
  private addTotalsRow(employeeCount: number, totalRegular: number, totalOvertime: number): void {
    const totalsRow = ENHANCED_CONFIG.employeeGrid.startRow + employeeCount;
    const cols = ENHANCED_CONFIG.employeeGrid.columns;
    
    // Total label
    const labelCell = this.worksheet.getCell(totalsRow, cols.employeeName);
    labelCell.value = 'TOTAL HOURS:';
    labelCell.font = { ...ENHANCED_CONFIG.styling.headerFont, bold: true };
    labelCell.alignment = { horizontal: 'right', vertical: 'middle' };
    
    // Total values
    const totalRegularCell = this.worksheet.getCell(totalsRow, cols.regularHours);
    const totalOvertimeCell = this.worksheet.getCell(totalsRow, cols.overtimeHours);
    const grandTotalCell = this.worksheet.getCell(totalsRow, cols.totalHours);
    
    totalRegularCell.value = Math.round(totalRegular * 100) / 100;
    totalRegularCell.numFmt = '0.00';
    totalOvertimeCell.value = Math.round(totalOvertime * 100) / 100;
    totalOvertimeCell.numFmt = '0.00';
    grandTotalCell.value = Math.round((totalRegular + totalOvertime) * 100) / 100;
    grandTotalCell.numFmt = '0.00';
    
    // Style totals row
    const maxCol = Math.max(...Object.values(cols));
    for (let col = 1; col <= maxCol; col++) {
      const cell = this.worksheet.getCell(totalsRow, col);
      cell.font = { ...ENHANCED_CONFIG.styling.headerFont, bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: ENHANCED_CONFIG.styling.colors.totalRowBg }
      };
      cell.border = {
        top: { style: 'medium', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        left: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        bottom: { style: 'medium', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } },
        right: { style: 'thin', color: { argb: ENHANCED_CONFIG.styling.colors.borderColor } }
      };
    }
  }

  /**
   * Add signature areas
   */
  private addSignatureAreas(timesheet: any): void {
    const signatureRow = ENHANCED_CONFIG.employeeGrid.startRow + 
                        (timesheet.shift.assignedPersonnel?.length || 0) + 3;
    
    // Company signature
    this.worksheet.getCell(signatureRow, 2).value = 'Company Representative:';
    this.worksheet.getCell(signatureRow, 2).font = ENHANCED_CONFIG.styling.headerFont;
    
    // Manager signature  
    this.worksheet.getCell(signatureRow, 8).value = 'Manager Signature:';
    this.worksheet.getCell(signatureRow, 8).font = ENHANCED_CONFIG.styling.headerFont;
    
    // Add signature lines
    const signatureLineRow = signatureRow + 2;
    this.worksheet.getCell(signatureLineRow, 2).value = '________________________';
    this.worksheet.getCell(signatureLineRow, 8).value = '________________________';
    
    // Add date lines
    const dateLineRow = signatureRow + 3;
    this.worksheet.getCell(dateLineRow, 2).value = 'Date: _______________';
    this.worksheet.getCell(dateLineRow, 8).value = 'Date: _______________';
  }

  /**
   * Generate the complete Excel timesheet
   */
  async generateTimesheetExcel(timesheetId: string): Promise<ExcelJS.Workbook> {
    // Fetch timesheet data
    const timesheet = await prisma.timesheet.findUnique({
      where: { id: timesheetId },
      include: {
        shift: {
          include: {
            job: { include: { company: true } },
            assignedPersonnel: {
              include: {
                user: true,
                timeEntries: { orderBy: { createdAt: 'asc' } },
              },
            },
          },
        },
      },
    });

    if (!timesheet || !timesheet.shift || !timesheet.shift.job) {
      throw new Error('Timesheet not found or data is incomplete');
    }

    // Load template if available
    await this.loadTemplate();
    
    // Setup worksheet structure
    this.setupWorksheetStructure();
    
    // Populate data
    this.populateHeaderInfo(timesheet);
    const { totalRegular, totalOvertime } = this.populateEmployeeData(timesheet);
    
    // Add totals and signatures
    this.addTotalsRow(timesheet.shift.assignedPersonnel?.length || 0, totalRegular, totalOvertime);
    this.addSignatureAreas(timesheet);
    
    return this.workbook;
  }
}

/**
 * Main function to generate enhanced Excel timesheet
 */
export async function generateEnhancedTimesheetExcel(timesheetId: string): Promise<ExcelJS.Workbook> {
  const generator = new EnhancedExcelGenerator();
  return await generator.generateTimesheetExcel(timesheetId);
}
