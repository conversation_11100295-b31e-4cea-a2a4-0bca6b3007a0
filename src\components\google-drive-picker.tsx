"use client";

import Image from 'next/image';
import React from "react";
import { useQuery } from '@tanstack/react-query';

interface DriveFile {
  id: string;
  name: string;
  mimeType: string;
  modifiedTime: string;
  webViewLink: string;
  thumbnailLink?: string;
}

interface GoogleDrivePickerProps {
  accessToken: string;
  onFileSelect: (file: DriveFile) => void;
}

export default function GoogleDrivePicker({ accessToken, onFileSelect }: GoogleDrivePickerProps) {
  const { data: files = [], isLoading: loading, error } = useQuery<DriveFile[], Error>({
    queryKey: ['googleDriveFiles', accessToken],
    queryFn: async () => {
      const res = await fetch("/api/import/google-drive/files", {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!res.ok) {
        const errorText = await res.text().catch(() => 'Failed to read error message');
        throw new Error(`HTTP ${res.status}: ${errorText.slice(0, 200)}`);
      }

      const contentType = res.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        throw new Error('Invalid response format from server');
      }

      const data = await res.json();
      return data.files || [];
    },
    enabled: !!accessToken,
  });

  if (loading) return <div>Loading files...</div>;
  if (error) return <div className="text-red-600">Error: {error.message}</div>;

  return (
    <div>
      <h3 className="mb-2 font-semibold">Select a Google Sheets file to import</h3>
      <ul className="space-y-2 max-h-64 overflow-y-auto">
        {files.map((file) => (
          <li key={file.id} className="flex items-center space-x-4 p-2 border rounded hover:bg-gray-100 cursor-pointer" onClick={() => onFileSelect(file)}>
            {file.thumbnailLink ? (
              <div className="relative w-8 h-8">
                <Image
                  src={file.thumbnailLink}
                  alt={file.name}
                  fill
                  sizes="32px"
                  className="object-cover rounded"
                />
              </div>
            ) : (
              <div className="w-8 h-8 bg-gray-300 rounded flex items-center justify-center text-sm font-bold text-gray-600">S</div>
            )}
            <div>
              <div className="font-medium">{file.name}</div>
              <div className="text-xs text-gray-500">{new Date(file.modifiedTime).toLocaleString()}</div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
