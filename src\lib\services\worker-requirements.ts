import { prisma } from '@/lib/prisma';

export async function updateWorkerRequirements(shiftId: string, requirements: any) {
  try {
    console.log('updateWorkerRequirements called with:', { shiftId, requirements });

    const processedRequirements = Array.isArray(requirements) ? requirements : requirements || [];

    console.log('Processed requirements:', processedRequirements);

    // Normalize: ensure one WorkerRequirement row per roleCode for this shift
    // Strategy: delete existing reqs for the shift, then bulk create the new set
    await prisma.workerRequirement.deleteMany({ where: { shiftId } });

    if (processedRequirements.length > 0) {
      await prisma.workerRequirement.createMany({
        data: processedRequirements.map((req: any) => ({
          shiftId,
          workerTypeCode: req.roleCode, // roleCode maps to workerTypeCode
          requiredCount: Number(req.requiredCount || 0),
        })),
      });
    }

    // Return updated shift with workerRequirements for consumers
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        job: { include: { company: true } },
        assignedPersonnel: true,
        workerRequirements: true,
      },
    });

    return shift;
  } catch (error) {
    console.error('Error updating worker requirements:', error);
    throw error;
  }
}

// Helper function to get role name from code
function getRoleNameFromCode(roleCode: string): string {
  const roleMap: Record<string, string> = {
    'CC': 'Crew Chief',
    'SH': 'Stage Hand',
    'FO': 'Fork Operator',
    'RFO': 'Reach Fork Operator',
    'RG': 'Rigger'
  };
  return roleMap[roleCode] || roleCode;
}

// Helper function to get role color from code
function getRoleColorFromCode(roleCode: string): string {
  const colorMap: Record<string, string> = {
    'CC': 'purple',
    'SH': 'blue',
    'FO': 'green',
    'RFO': 'yellow',
    'RG': 'red'
  };
  return colorMap[roleCode] || 'gray';
}
