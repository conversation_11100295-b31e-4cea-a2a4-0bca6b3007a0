Write-Host "🔄 Cleaning project environment..."

# Kill stray processes
Write-Host "⚡ Killing Node.js and VSCode processes..."
taskkill /F /IM node.exe /T 2>$null
taskkill /F /IM Code.exe /T 2>$null

# Give Windows a moment to release locks
Start-Sleep -Seconds 2

# Remove .next build folder
if (Test-Path ".next") {
    Write-Host "🗑️ Removing .next..."
    try {
        Remove-Item -Recurse -Force ".next"
    } catch {
        Write-Host "⚠️ .next folder locked, forcing delete..."
        rmdir /s /q ".next"
    }
}

# Remove cache folders
$cachePaths = @(
    "node_modules\.cache",
    "package-lock.json",
    "yarn.lock",
    "pnpm-lock.yaml"
)

foreach ($path in $cachePaths) {
    if (Test-Path $path) {
        Write-Host "🗑️ Removing $path..."
        try {
            Remove-Item -Recurse -Force $path
        } catch {
            Write-Host "⚠️ Could not remove $path, forcing..."
            rmdir /s /q $path
        }
    }
}

# Clear npm cache
Write-Host "🧹 Clearing npm cache..."
npm cache clean --force | Out-Null

Write-Host "✅ Cleanup complete!"
