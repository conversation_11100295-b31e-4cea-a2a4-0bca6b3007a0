import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [AUTH/ME] Starting authentication check');

    // First try NextAuth session
    console.log('🔍 [AUTH/ME] Attempting getServerSession');
    const session = await getServerSession(authOptions);
    console.log('🔍 [AUTH/ME] getServerSession completed:', !!session);

    if (session?.user) {
      // User is authenticated via NextAuth
      return NextResponse.json({
        success: true,
        user: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          role: session.user.role,
          avatarUrl: session.user.image,
          companyId: session.user.companyId,
        },
      });
    }

    if (!session?.user) {
      return NextResponse.json(
        { error: 'No authentication found' },
        { status: 401 }
      );
    }

    // User is authenticated via NextAuth
    const response = NextResponse.json({
      success: true,
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
        avatarUrl: session.user.image,
        companyId: session.user.companyId,
      },
    });

    // Add cache control headers to prevent caching of auth responses
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
