"use client"

import React, { useState, useMemo } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useUserById } from '@/hooks/use-api';
import { UserRole } from '@prisma/client';
import { withAuth } from '@/lib/withAuth';
import {
  ArrowLeft,
  Edit,
  User,
  Crown,
  Truck,
  HardHat,
  DollarSign,
  Phone,
  MapPin,
  Calendar,
  Clock,
  TrendingUp,
  Shield,
  AlertTriangle,
  CheckCircle2,
  FileText,
  Award,
  Home,
  Mail
} from "lucide-react";
import { DashboardPage } from '@/components/DashboardPage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

function EnhancedEmployeeDetail() {
  const { id } = useParams();
  const router = useRouter();
  const { data: employee, isLoading, isError } = useUserById(id as string);
  const [activeTab, setActiveTab] = useState("overview");

  if (isLoading) {
    return (
      <DashboardPage title="Loading Employee...">
        <div className="flex justify-center items-center h-64">
          <User className="h-12 w-12 animate-spin" />
        </div>
      </DashboardPage>
    );
  }

  if (isError || !employee) {
    return (
      <DashboardPage title="Error">
        <div className="text-center">
          <p className="text-destructive">Could not load employee profile.</p>
          <Button onClick={() => router.back()} variant="outline" className="mt-4">
            Go Back
          </Button>
        </div>
      </DashboardPage>
    );
  }

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'Admin': return 'destructive';
      case 'CrewChief': return 'default';
      case 'StageHand': return 'secondary';
      case 'CompanyUser': return 'outline';
      default: return 'secondary';
    }
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(cents / 100);
  };

  // Calculate compliance score
  const complianceScore = useMemo(() => {
    let score = 0;
    let maxScore = 100;

    // Payroll setup (40 points)
    if (employee.payrollBaseRateCents && employee.payrollBaseRateCents > 0) score += 10;
    if (employee.addressLine1) score += 10;
    if (employee.city && employee.state) score += 10;
    if (employee.ssnLast4) score += 10;

    // Contact info (30 points)
    if (employee.phone) score += 15;
    if (employee.email) score += 15;

    // Certifications (30 points)
    if (employee.OSHA_10_Certifications) score += 15;
    if (employee.certifications && employee.certifications.length > 0) score += 15;

    return Math.round(score);
  }, [employee]);

  const getComplianceColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  // Mock recent payroll data (in real app, this would come from API)
  const recentPayrollEntries = employee.payrollEntries || [];

  return (
    <DashboardPage
      title={`${employee.name}`}
      description="Employee profile with payroll and compliance details"
      buttonText="Edit Employee"
      buttonAction={() => router.push(`/admin/stagehands/${id}/edit`)}
    >
      <div className="space-y-6">
        {/* Header Section */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card className="md:col-span-1">
            <CardContent className="pt-6 flex flex-col items-center text-center">
              <Avatar className="w-24 h-24 mb-4">
                <AvatarImage src={employee.avatarUrl} />
                <AvatarFallback>
                  {employee.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <h2 className="text-xl font-semibold">{employee.name}</h2>
              <p className="text-sm text-muted-foreground flex items-center gap-1">
                <Mail className="h-3 w-3" />
                {employee.email}
              </p>
              <Badge variant={getRoleBadgeVariant(employee.role)} className="mt-2">
                {employee.role}
              </Badge>
              {employee.phone && (
                <p className="text-sm text-muted-foreground mt-2 flex items-center gap-1">
                  <Phone className="h-3 w-3" />
                  {employee.phone}
                </p>
              )}
              {employee.location && (
                <p className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {employee.location}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Compliance Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getComplianceColor(complianceScore)}`}>
                {complianceScore}%
              </div>
              <p className="text-xs text-muted-foreground">CA Requirements</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Hourly Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {employee.payrollBaseRateCents ? 
                  formatCurrency(employee.payrollBaseRateCents) : 
                  'Not Set'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {employee.payrollType || 'Not Set'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <Badge variant={employee.isActive ? "secondary" : "outline"}>
                  {employee.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">Employment</p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">
              <User className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="payroll">
              <DollarSign className="h-4 w-4 mr-2" />
              Payroll
            </TabsTrigger>
            <TabsTrigger value="certifications">
              <Award className="h-4 w-4 mr-2" />
              Certifications
            </TabsTrigger>
            <TabsTrigger value="compliance">
              <Shield className="h-4 w-4 mr-2" />
              Compliance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <p className="font-medium">Full Name:</p>
                    <p className="text-muted-foreground">{employee.name}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="font-medium">Email:</p>
                    <p className="text-muted-foreground">{employee.email}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="font-medium">Phone:</p>
                    <p className="text-muted-foreground">{employee.phone || 'Not provided'}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="font-medium">Primary Location:</p>
                    <p className="text-muted-foreground">{employee.location || 'Not specified'}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="font-medium">Role:</p>
                    <Badge variant={getRoleBadgeVariant(employee.role)}>
                      {employee.role}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <p className="font-medium">Performance Rating:</p>
                    <p className="text-muted-foreground">{employee.performance ?? 'Not rated'}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Address Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {employee.addressLine1 ? (
                    <div className="space-y-2">
                      <div className="flex items-start gap-2">
                        <Home className="h-4 w-4 mt-1 text-muted-foreground" />
                        <div>
                          <p className="text-sm">{employee.addressLine1}</p>
                          {employee.addressLine2 && (
                            <p className="text-sm">{employee.addressLine2}</p>
                          )}
                          <p className="text-sm">
                            {employee.city}, {employee.state} {employee.postalCode}
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <Home className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No address on file</p>
                      <p className="text-xs text-muted-foreground">Required for payroll processing</p>
                    </div>
                  )}
                  
                  {employee.ssnLast4 && (
                    <div className="flex justify-between">
                      <p className="font-medium">SSN:</p>
                      <p className="text-muted-foreground">****{employee.ssnLast4}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="payroll">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Payroll Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <p className="font-medium">Payroll Type:</p>
                        <Badge variant={employee.payrollType === 'HOURLY' ? 'default' : 'secondary'}>
                          {employee.payrollType || 'Not Set'}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <p className="font-medium">Base Rate:</p>
                        <p className="text-muted-foreground font-medium">
                          {employee.payrollBaseRateCents ? 
                            `${formatCurrency(employee.payrollBaseRateCents)}${employee.payrollType === 'HOURLY' ? '/hr' : '/yr'}` : 
                            'Not Set'
                          }
                        </p>
                      </div>
                      {employee.payrollBaseRateCents && employee.payrollType === 'HOURLY' && (
                        <div className="space-y-2 p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm font-medium text-blue-900">California Rates</p>
                          <div className="text-xs space-y-1 text-blue-800">
                            <p>Overtime: {formatCurrency(Math.round(employee.payrollBaseRateCents * 1.5))}/hr</p>
                            <p>Double Time: {formatCurrency(employee.payrollBaseRateCents * 2)}/hr</p>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <p className="font-medium">Address Status:</p>
                        {employee.addressLine1 && employee.city && employee.state ? (
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Complete
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-red-600 border-red-600">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Incomplete
                          </Badge>
                        )}
                      </div>
                      <div className="flex justify-between">
                        <p className="font-medium">SSN Status:</p>
                        {employee.ssnLast4 ? (
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            On File
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-red-600 border-red-600">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Missing
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Payroll Entries */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Payroll History</CardTitle>
                </CardHeader>
                <CardContent>
                  {recentPayrollEntries.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Period</TableHead>
                          <TableHead>Regular Hours</TableHead>
                          <TableHead>Overtime Hours</TableHead>
                          <TableHead>Double Time</TableHead>
                          <TableHead className="text-right">Gross Pay</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {recentPayrollEntries.map((entry: any) => (
                          <TableRow key={entry.id}>
                            <TableCell>
                              <div className="text-sm">
                                {new Date(entry.period.startDate).toLocaleDateString()} - {' '}
                                {new Date(entry.period.endDate).toLocaleDateString()}
                              </div>
                            </TableCell>
                            <TableCell>{entry.regularHours.toFixed(1)}h</TableCell>
                            <TableCell>{entry.overtimeHours.toFixed(1)}h</TableCell>
                            <TableCell>{entry.doubletimeHours.toFixed(1)}h</TableCell>
                            <TableCell className="text-right font-medium">
                              {formatCurrency(entry.grossPayCents)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8">
                      <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No payroll history available</p>
                      <p className="text-sm text-muted-foreground">Payroll entries will appear here after processing</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="certifications">
            <Card>
              <CardHeader>
                <CardTitle>Certifications & Qualifications</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Crown className={`h-5 w-5 ${employee.crew_chief_eligible ? 'text-blue-500' : 'text-gray-400'}`} />
                      <h4 className="font-medium">Crew Chief</h4>
                    </div>
                    <Badge variant={employee.crew_chief_eligible ? 'default' : 'secondary'}>
                      {employee.crew_chief_eligible ? 'Eligible' : 'Not Eligible'}
                    </Badge>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Truck className={`h-5 w-5 ${employee.fork_operator_eligible ? 'text-green-500' : 'text-gray-400'}`} />
                      <h4 className="font-medium">Fork Operator</h4>
                    </div>
                    <Badge variant={employee.fork_operator_eligible ? 'default' : 'secondary'}>
                      {employee.fork_operator_eligible ? 'Certified' : 'Not Certified'}
                    </Badge>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <HardHat className={`h-5 w-5 ${employee.OSHA_10_Certifications ? 'text-orange-500' : 'text-gray-400'}`} />
                      <h4 className="font-medium">OSHA 10-Hour</h4>
                    </div>
                    <Badge variant={employee.OSHA_10_Certifications ? 'default' : 'secondary'}>
                      {employee.OSHA_10_Certifications ? 'Certified' : 'Not Certified'}
                    </Badge>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Additional Certifications</h3>
                  <div className="flex flex-wrap gap-2">
                    {employee.certifications && employee.certifications.length > 0 ? (
                      employee.certifications.map((cert: string) => (
                        <Badge key={cert} variant="outline" className="text-sm">
                          <Award className="h-3 w-3 mr-1" />
                          {cert}
                        </Badge>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground">No additional certifications on file</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="compliance">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  California Compliance Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className={`p-4 rounded-lg border-2 ${complianceScore >= 80 ? 'border-green-200 bg-green-50' : complianceScore >= 60 ? 'border-yellow-200 bg-yellow-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="text-center">
                      <div className={`text-3xl font-bold ${getComplianceColor(complianceScore)}`}>
                        {complianceScore}%
                      </div>
                      <p className="text-sm text-muted-foreground">Overall Compliance</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Payroll Setup</span>
                      <div className="flex items-center gap-2">
                        {employee.payrollBaseRateCents && employee.addressLine1 && employee.ssnLast4 ? (
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        )}
                        <span className="text-sm font-medium">
                          {employee.payrollBaseRateCents && employee.addressLine1 && employee.ssnLast4 ? 'Complete' : 'Incomplete'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Safety Certifications</span>
                      <div className="flex items-center gap-2">
                        {employee.OSHA_10_Certifications ? (
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        )}
                        <span className="text-sm font-medium">
                          {employee.OSHA_10_Certifications ? 'OSHA Certified' : 'Not Certified'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Contact Information</span>
                      <div className="flex items-center gap-2">
                        {employee.phone && employee.email ? (
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        )}
                        <span className="text-sm font-medium">
                          {employee.phone && employee.email ? 'Complete' : 'Incomplete'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Required Actions</h3>
                  <div className="space-y-2">
                    {!employee.payrollBaseRateCents && (
                      <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm">Set hourly rate for payroll processing</span>
                      </div>
                    )}
                    {!employee.addressLine1 && (
                      <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm">Add complete address for tax compliance</span>
                      </div>
                    )}
                    {!employee.ssnLast4 && (
                      <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="text-sm">SSN required for payroll and tax reporting</span>
                      </div>
                    )}
                    {!employee.OSHA_10_Certifications && (
                      <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <HardHat className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">OSHA 10-Hour certification recommended for safety compliance</span>
                      </div>
                    )}
                  </div>

                  {complianceScore === 100 && (
                    <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Employee is fully compliant with California requirements!</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardPage>
  );
}

export default withAuth(EnhancedEmployeeDetail, UserRole.Admin);
