"use client"

export const dynamic = 'force-dynamic';

import React, { useState, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { format, isToday, isTomorrow, isYesterday, differenceInDays, isWithinInterval } from "date-fns"
import { useUser } from "@/hooks/use-user"
import { useEnhancedPerformance } from "@/hooks/use-enhanced-performance"
import { useCacheManagement } from "@/hooks/use-cache-management"
import { useUnifiedShifts, useUnifiedCompanies, useUnifiedMutation } from "@/hooks/use-unified-api"
import { useNavigationPerformance } from "@/hooks/use-navigation-performance"
import { PageErrorBoundary } from "@/components/error-boundary"
import { ShiftsPageLoader } from "@/components/loading-states"
import { AdvancedFilters, type AdvancedFilters as AdvancedFiltersType, type DateRange } from "@/components/advanced-filters"
import { MobileCardGrid, MobileFilterDrawer, MobileSearchBar, ResponsiveGrid } from "@/components/mobile-responsive"
import { BulkOperations, BulkOperationsProvider, SelectableItem, type BulkAction } from "@/components/bulk-operations"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"

import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Users,
  UserCheck,
  Calendar,
  Clock,
  MapPin,
  Building,
  Building2,
  AlertCircle,
  RefreshCw,
  Calendar as CalendarIcon,
  Briefcase,
  FileText,
  Eye,
  ExternalLink,
  MoreHorizontal,
  Download,
  Upload,
  Copy
} from "lucide-react"
import { CompanyAvatar } from "@/components/CompanyAvatar"
import { EnhancedStatusBadge as UnifiedStatusBadge, getFulfillmentStatus, getPriorityBadge } from "@/components/ui/enhanced-status-badge"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import Link from 'next/link'

import { calculateShiftRequirements, calculateAssignedWorkers } from "@/lib/worker-count-utils"
import { getShiftStatus, getShiftStatusDisplay } from '@/lib/shift-status';
import { UserRole, ShiftStatus, ShiftWithDetails } from '@/lib/types'
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils";
import { withPageAuthRequired } from "@/lib/auth/with-page-auth-required";

// Update the date and time formatting functions
const formatSimpleDate = (date: string | Date) => {
  return format(new Date(date), 'MM/dd/yyyy')
}

const formatSimpleTime = (time: string | Date) => {
  return format(new Date(time), 'hh:mm a')
}

function ShiftsPageContent() {
  const { user } = useUser()
  const router = useRouter()
  const { toast } = useToast()
  const { smartPrefetch, prefetchForPage } = useEnhancedPerformance()
  const { refreshShifts, isDevelopment } = useCacheManagement()
  const { navigateWithPrefetch, handleHover, cancelHover } = useNavigationPerformance({
    enableHoverPrefetch: true,
    enableRoutePreloading: true,
  })

  // State for advanced filtering
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFiltersType>({
    search: '',
    dateRange: { from: undefined, to: undefined },
    status: [],
    companies: [],
    workers: [],
    locations: [],
    customFilters: {}
  })

  // State for bulk operations
  const [selectedShiftIds, setSelectedShiftIds] = useState<string[]>([])
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted on client side to prevent hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  const { data: companiesData } = useUnifiedCompanies()
  const companies = companiesData?.companies || []

  // Fetch shifts data using the unified hook
  const { data: shiftsData, isLoading, isError, error, refetch: refetchShifts } = useUnifiedShifts(
    user?.role === 'CompanyUser' ? { companyId: user.companyId } : {}
  )
  const shifts: ShiftWithDetails[] = shiftsData || [];

  // Bulk delete mutation
  const bulkDeleteMutation = useUnifiedMutation(
    async (shiftIds: string[]) => {
      const results = await Promise.allSettled(
        shiftIds.map(id => 
          fetch(`/api/shifts/${id}`, { method: 'DELETE' }).then(res => {
            if (!res.ok) throw new Error(`Failed to delete shift ${id}`);
            return { id, success: true };
          })
        )
      );
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      return {
        success: failed === 0,
        successCount: successful,
        errorCount: failed,
        message: `Deleted ${successful} shift${successful !== 1 ? 's' : ''}${failed > 0 ? `, ${failed} failed` : ''}`
      };
    },
    {
      entityType: 'shifts',
      mutationType: 'delete',
      onSuccess: () => {
        setSelectedShiftIds([]);
      }
    }
  );

  // Bulk update status mutation
  const bulkUpdateStatusMutation = useUnifiedMutation(
    async (data: { shiftIds: string[], status: string }) => {
      const results = await Promise.allSettled(
        data.shiftIds.map(id => 
          fetch(`/api/shifts/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: data.status })
          }).then(res => {
            if (!res.ok) throw new Error(`Failed to update shift ${id}`);
            return { id, success: true };
          })
        )
      );
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      return {
        success: failed === 0,
        successCount: successful,
        errorCount: failed,
        message: `Updated ${successful} shift${successful !== 1 ? 's' : ''}${failed > 0 ? `, ${failed} failed` : ''}`
      };
    },
    {
      entityType: 'shifts',
      mutationType: 'update',
      onSuccess: () => {
        setSelectedShiftIds([]);
      }
    }
  );

  // Filter and sort shifts with advanced filtering
  const filteredShifts = useMemo(() => {
    let filtered = shifts.filter((shift) => {
      // Text search
      if (advancedFilters.search) {
        const searchLower = advancedFilters.search.toLowerCase();
        const matchesSearch = 
          shift.job?.name?.toLowerCase().includes(searchLower) ||
          shift.job?.company?.name?.toLowerCase().includes(searchLower) ||
          shift.location?.toLowerCase().includes(searchLower) ||
          shift.description?.toLowerCase().includes(searchLower) ||
          shift.notes?.toLowerCase().includes(searchLower);
        
        if (!matchesSearch) return false;
      }

      // Date range filter
      if (advancedFilters.dateRange.from || advancedFilters.dateRange.to) {
        const shiftDate = new Date(shift.date);
        if (advancedFilters.dateRange.from && advancedFilters.dateRange.to) {
          if (!isWithinInterval(shiftDate, {
            start: advancedFilters.dateRange.from,
            end: advancedFilters.dateRange.to
          })) return false;
        } else if (advancedFilters.dateRange.from) {
          if (shiftDate < advancedFilters.dateRange.from) return false;
        } else if (advancedFilters.dateRange.to) {
          if (shiftDate > advancedFilters.dateRange.to) return false;
        }
      }

      // Status filter
      if (advancedFilters.status.length > 0) {
        if (!advancedFilters.status.includes(shift.status)) return false;
      }

      // Company filter
      if (advancedFilters.companies.length > 0) {
        if (!advancedFilters.companies.includes(shift.job?.companyId)) return false;
      }

      // Location filter
      if (advancedFilters.locations.length > 0) {
        if (!shift.location || !advancedFilters.locations.includes(shift.location)) return false;
      }

      return true;
    });

    // Sort shifts by date (most recent first)
    filtered.sort((a: any, b: any) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });

    return filtered;
  }, [shifts, advancedFilters]);

  // Prepare filter options
  const statusOptions = useMemo(() => 
    Object.values(ShiftStatus).map((status: string) => ({
      value: status,
      label: status,
      count: shifts.filter(s => s.status === status).length || 0
    })), [shifts]
  );

  const companyOptions = useMemo(() => 
    companies.map(company => ({
      value: company.id,
      label: company.name,
      count: shifts.filter(s => s.job?.companyId === company.id).length || 0
    })), [companies, shifts]
  );

  const locationOptions = useMemo(() => {
    const locations = new Set<string>();
    shifts.forEach(shift => {
      if (shift.location) locations.add(shift.location);
    });
    return Array.from(locations).map(location => ({
      value: location,
      label: location,
      count: shifts.filter(s => s.location === location).length || 0
    }));
  }, [shifts]);

  // Bulk actions
  const bulkActions: BulkAction[] = [
    {
      id: 'delete',
      label: 'Delete',
      icon: <Trash2 className="h-4 w-4 mr-1" />,
      variant: 'destructive',
      requiresConfirmation: true,
      confirmationTitle: 'Delete Shifts',
      confirmationDescription: 'Are you sure you want to delete the selected shifts? This action cannot be undone.',
      execute: async (selectedIds) => bulkDeleteMutation.mutateAsync(selectedIds)
    },
    {
      id: 'update-status',
      label: 'Update Status',
      icon: <Edit className="h-4 w-4 mr-1" />,
      requiresConfirmation: true,
      confirmationTitle: 'Update Shift Status',
      fields: [
        {
          name: 'status',
          label: 'New Status',
          type: 'select',
          required: true,
          options: Object.values(ShiftStatus).map((status: string) => ({
            value: status,
            label: status
          }))
        }
      ],
      execute: async (selectedIds, data) => 
        bulkUpdateStatusMutation.mutateAsync({ shiftIds: selectedIds, status: data?.status })
    },
    {
      id: 'export',
      label: 'Export',
      icon: <Download className="h-4 w-4 mr-1" />,
      execute: async (selectedIds) => {
        const selectedShifts = shifts?.filter((s: any) => selectedIds.includes(s.id));
        const csvContent = generateShiftCSV(selectedShifts || []);
        downloadCSV(csvContent, `shifts-${format(new Date(), 'yyyy-MM-dd')}.csv`);
        return {
          success: true,
          successCount: selectedIds.length,
          errorCount: 0,
          message: `Exported ${selectedIds.length} shifts`
        };
      }
    },
    {
      id: 'duplicate',
      label: 'Duplicate',
      icon: <Copy className="h-4 w-4 mr-1" />,
      requiresConfirmation: true,
      confirmationTitle: 'Duplicate Shifts',
      confirmationDescription: 'This will create copies of the selected shifts with new dates.',
      fields: [
        {
          name: 'newDate',
          label: 'New Date',
          type: 'date',
          required: true
        }
      ],
      execute: async (selectedIds, data) => {
        // Implementation for duplicating shifts
        return {
          success: true,
          successCount: selectedIds.length,
          errorCount: 0,
          message: `Duplicated ${selectedIds.length} shifts`
        };
      }
    }
  ];

  const canManage = user?.role === 'Admin' || user?.role === 'CrewChief'

  // Performance optimization on mount
  useEffect(() => {
    if (user) {
      smartPrefetch('/shifts');
    }
  }, [user, smartPrefetch]);

  // Show loading state if user is not loaded yet
  if (!user) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto text-center py-12">
          <p className="text-muted-foreground">Please log in to view shifts.</p>
        </div>
      </main>
    )
  }

  const getDateBadge = (date: string | Date) => {
    const shiftDate = new Date(date)
    if (isToday(shiftDate)) {
      return <Badge className="bg-success text-white border-success text-xs">Today</Badge>
    }
    if (isTomorrow(shiftDate)) {
      return <Badge className="bg-info text-white border-info text-xs">Tomorrow</Badge>
    }
    if (isYesterday(shiftDate)) {
      return <Badge variant="secondary" className="text-xs">Yesterday</Badge>
    }
    return <Badge variant="outline" className="text-xs">{format(shiftDate, 'MMM d')}</Badge>
  }

  const handleShiftView = (shiftId: string) => {
    navigateWithPrefetch(`/shifts/${shiftId}`)
  }

  const handleShiftEdit = (shiftId: string) => {
    navigateWithPrefetch(`/shifts/${shiftId}/edit`)
  }

  const handleShiftHover = (shiftId: string) => {
    handleHover(`/shifts/${shiftId}`)
  }

  const handleJobClick = (jobId: string) => {
    navigateWithPrefetch(`/jobs/${jobId}`)
  }

  const handleCompanyClick = (companyId: string) => {
    navigateWithPrefetch(`/companies/${companyId}`)
  }

  const handleJobHover = (jobId: string) => {
    handleHover(`/jobs/${jobId}`)
  }

  const handleCompanyHover = (companyId: string) => {
    handleHover(`/companies/${companyId}`)
  }

  const refetch = () => {
    refetchShifts();
  }

  if (!mounted || isLoading) {
    return <ShiftsPageLoader />
  }

  if (isError) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[60vh]">
            <Alert className="max-w-md bg-destructive/20 border-destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Error loading shifts: {error instanceof Error ? error.message : String(error) || 'Unknown error'}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={refetch}
                  className="mt-2 w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </main>
    )
  }

  const renderShiftCard = (shift: any, index: number) => {
    const required = calculateShiftRequirements(shift);
    const assigned = calculateAssignedWorkers(shift);
    const fulfillmentStatus = getFulfillmentStatus(assigned, required);
    const daysUntil = differenceInDays(new Date(shift.date), new Date());
    const priorityStatus = getPriorityBadge(daysUntil);
    
    // Get actual shift status based on timing and completion
    const shiftStatus = getShiftStatus(shift);
    const displayStatus = getShiftStatusDisplay(shiftStatus);
    
    const isCompleted = shiftStatus.isCompleted;
    const hasTimesheet = shift.timesheets && shift.timesheets.length > 0;
    const timesheet = hasTimesheet ? shift.timesheets[0] : null;
    const isSelected = selectedShiftIds.includes(shift.id);

    return (
      <Card
        key={shift.id}
        className={cn(
          'group hover:shadow-lg transition-all duration-300 cursor-pointer card-consistent hover:border-primary/30',
          shiftStatus.isLive
            ? 'bg-error/10 border-error/40 hover:border-error/60 hover:shadow-error/20'
            : isCompleted
            ? 'bg-success/10 border-success/40 hover:border-success/60 hover:shadow-success/20'
            : 'hover:border-primary/30 hover:shadow-primary/10'
        )}
        onClick={() => handleShiftView(shift.id)}
        onMouseEnter={() => handleShiftHover(shift.id)}
        onMouseLeave={cancelHover}
      >
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  {getDateBadge(shift.date)}
                  <UnifiedStatusBadge status={shiftStatus.status as any} size="sm" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center text-sm text-secondary-consistent">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="font-medium">{formatSimpleDate(shift.date)}</span>
                  </div>
                  <div className="flex items-center text-sm text-secondary-consistent">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{formatSimpleTime(shift.startTime)} - {formatSimpleTime(shift.endTime)}</span>
                  </div>
                  {shift.location && (
                    <div className="flex items-center text-sm text-secondary-consistent">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="truncate">{shift.location}</span>
                    </div>
                  )}
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button variant="ghost" className="h-8 w-8 p-0 interactive-hover">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-popover border-border">
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleShiftView(shift.id); }}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  {canManage && (
                    <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleShiftEdit(shift.id); }}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Shift
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Job Info */}
            <div className="space-y-2 bg-surface rounded-lg p-3 border border-border/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm text-secondary-consistent">
                  <Briefcase className="h-4 w-4 mr-2 text-primary" />
                  <span className="font-medium truncate">{shift.job?.name}</span>
                </div>
                <Link
                  href={`/jobs/${shift.job?.id}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleJobClick(shift.job?.id);
                  }}
                  onMouseEnter={() => handleJobHover(shift.job?.id)}
                  onMouseLeave={cancelHover}
                  className="text-xs text-primary hover:text-primary/80 transition-colors flex items-center gap-1"
                >
                  View Job
                  <ExternalLink className="h-3 w-3" />
                </Link>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CompanyAvatar
                    src={shift.job?.company?.company_logo_url}
                    name={shift.job?.company?.name || ''}
                    className="w-5 h-5"
                  />
                  <span className="text-sm text-secondary-consistent truncate">{shift.job?.company?.name}</span>
                </div>
                <Link
                  href={`/companies/${shift.job?.company?.id}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCompanyClick(shift.job?.company?.id);
                  }}
                  onMouseEnter={() => handleCompanyHover(shift.job?.company?.id)}
                  onMouseLeave={cancelHover}
                  className="text-xs text-primary hover:text-primary/80 transition-colors flex items-center gap-1"
                >
                  View Company
                  <ExternalLink className="h-3 w-3" />
                </Link>
              </div>
            </div>

            {/* Worker Status */}
            <div className="flex items-center justify-between pt-2 border-t border-border/30">
              <div className="flex items-center text-sm gap-2">
                <Users className="h-4 w-4 text-primary" />
                <span className="text-secondary-consistent font-medium">
                  {assigned} of {required} Workers
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                {isCompleted && hasTimesheet ? (
                  <Link
                    href={`/timesheets/${timesheet.id}`}
                    onClick={(e) => e.stopPropagation()}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-white bg-success/80 border border-success rounded-md hover:bg-success transition-colors"
                  >
                    <FileText className="h-3 w-3" />
                    <span>Timesheet</span>
                  </Link>
                ) : !isCompleted ? (
                  <UnifiedStatusBadge
                    status={fulfillmentStatus as any}
                    size="sm"
                    showCount={true}
                    count={assigned}
                    total={required}
                  />
                ) : null}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <BulkOperationsProvider
      items={filteredShifts}
      selectedIds={selectedShiftIds}
      onSelectionChange={setSelectedShiftIds}
    >
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                All Shifts
              </h1>
              <p className="text-muted-foreground">
                {filteredShifts.length} shift{filteredShifts.length !== 1 ? 's' : ''} found • Sorted by most recent
              </p>
            </div>
            <div className="flex gap-2">
              {canManage && (
                <Button 
                  onClick={() => navigateWithPrefetch('/admin/shifts/new')}
                  onMouseEnter={() => handleHover('/admin/shifts/new')}
                  onMouseLeave={cancelHover}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Shift
                </Button>
              )}
            </div>
          </div>

          {/* Mobile Search Bar */}
          <div className="block md:hidden">
            <MobileSearchBar
              value={advancedFilters.search}
              onChange={(value) => setAdvancedFilters(prev => ({ ...prev, search: value }))}
              placeholder="Search shifts, jobs, or companies..."
            />
          </div>

          {/* Advanced Filters */}
          <div className="hidden md:block">
            <AdvancedFilters
              filters={advancedFilters}
              onFiltersChange={setAdvancedFilters}
              statusOptions={statusOptions}
              companyOptions={companyOptions}
              locationOptions={locationOptions}
            />
          </div>

          {/* Mobile Filter Drawer */}
          <div className="block md:hidden">
            <MobileFilterDrawer title="Filter Shifts" description="Refine your shift search">
              <AdvancedFilters
                filters={advancedFilters}
                onFiltersChange={setAdvancedFilters}
                statusOptions={statusOptions}
                companyOptions={companyOptions}
                locationOptions={locationOptions}
              />
            </MobileFilterDrawer>
          </div>

          {/* Bulk Operations */}
          {canManage && (
            <BulkOperations
              items={filteredShifts}
              selectedIds={selectedShiftIds}
              onSelectionChange={setSelectedShiftIds}
              actions={bulkActions}
              maxSelectableItems={50}
            />
          )}

          {/* Shifts Grid */}
          {filteredShifts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="relative">
                <CalendarIcon className="h-16 w-16 text-muted-foreground/50 mb-4" />
                <Clock className="h-8 w-8 text-blue-400 absolute -top-2 -right-2" />
              </div>
              <h3 className="text-lg font-medium mb-2">No shifts found</h3>
              <p className="text-muted-foreground text-center max-w-md">
                {advancedFilters.search || advancedFilters.status.length > 0 || advancedFilters.companies.length > 0
                  ? "Try adjusting your filters to see more shifts."
                  : "No shifts have been scheduled yet."}
              </p>
            </div>
          ) : (
            <MobileCardGrid
              items={filteredShifts}
              renderCard={renderShiftCard}
              loading={isLoading}
              emptyState={
                <div className="flex flex-col items-center justify-center py-12">
                  <CalendarIcon className="h-16 w-16 text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No shifts found</h3>
                  <p className="text-muted-foreground text-center max-w-md">
                    Try adjusting your filters to see more shifts.
                  </p>
                </div>
              }
            />
          )}
        </div>
      </main>
    </BulkOperationsProvider>
  )
}

// Utility functions for CSV export
const generateShiftCSV = (shifts: any[]): string => {
  const headers = [
    'Date', 'Start Time', 'End Time', 'Job', 'Company', 'Location', 
    'Status', 'Required Workers', 'Assigned Workers', 'Description'
  ];
  
  const rows = shifts.map(shift => [
    formatSimpleDate(shift.date),
    formatSimpleTime(shift.startTime),
    formatSimpleTime(shift.endTime),
    shift.job?.name || '',
    shift.job?.company?.name || '',
    shift.location || '',
    shift.status,
    calculateShiftRequirements(shift),
    calculateAssignedWorkers(shift),
    shift.description || ''
  ]);
  
  return [headers, ...rows].map(row => 
    row.map(cell => `"${cell}"`).join(',')
  ).join('\n');
};

const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Export with error boundary
function ShiftsPage() {
  return (
    <PageErrorBoundary>
      <ShiftsPageContent />
    </PageErrorBoundary>
  )
}

export default withPageAuthRequired(ShiftsPage, {
  allowedRoles: [
    UserRole.Admin,
    UserRole.Manager,
    UserRole.CrewChief,
    UserRole.StageHand,
    UserRole.CompanyUser,
  ],
});
