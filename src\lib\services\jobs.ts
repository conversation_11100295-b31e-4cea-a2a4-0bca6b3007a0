import { prisma } from '../prisma';
import { Job, Prisma, UserRole, JobStatus } from '@prisma/client';
import { hasMinimumRole } from '../authorization';
import { AuthenticatedUser } from '../types';

// Define a type for the job with its relations
export type JobWithDetails = Prisma.JobGetPayload<{
  include: {
    company: true;
    shifts: {
      include: {
        assignedPersonnel: {
          include: {
            user: true;
          };
        };
      };
    };
  };
}>;

export type JobForList = Prisma.JobGetPayload<{
  select: {
    id: true;
    name: true;
    status: true;
    startDate: true;
    endDate: true;
    company: {
      select: {
        name: true;
      };
    };
  };
}>;

export async function getAllJobs(user: AuthenticatedUser): Promise<JobForList[]> {
  if (!hasMinimumRole(user, UserRole.CrewChief)) {
    throw new Error('Not authorized to view all jobs');
  }
  return prisma.job.findMany({
    select: {
      id: true,
      name: true,
      status: true,
      startDate: true,
      endDate: true,
      company: {
        select: {
          name: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
}

export async function getJobById(
  user: AuthenticatedUser,
  id: string
): Promise<JobWithDetails | null> {
  const job = await prisma.job.findUnique({
    where: { id },
    include: {
      company: true,
      shifts: {
        orderBy: {
          date: 'asc',
        },
        include: {
          assignedPersonnel: {
            include: {
              user: true,
            },
          },
        },
      },
    },
  });

  if (!job) return null;

  // Security: Ensure user is either an Admin or belongs to the company that owns the job
  const isOwner = user.companyId === job.companyId;
  if (!hasMinimumRole(user, UserRole.Admin) && !isOwner) {
    throw new Error('Not authorized to view this job');
  }

  return job;
}

export async function getJobsByCompanyId(id: string): Promise<JobForList[]> {
  return prisma.job.findMany({
    where: { companyId: id },
    select: {
      id: true,
      name: true,
      status: true,
      startDate: true,
      endDate: true,
      company: {
        select: {
          name: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });
}

export async function updateJob(
  user: AuthenticatedUser,
  id: string,
  data: {
    name?: string;
    description?: string | null;
    companyId?: string;
    status?: string;
    isCompleted?: boolean;
    startDate?: string;
    endDate?: string;
    location?: string | null;
    budget?: string | null;
    notes?: string | null;
  }
): Promise<JobWithDetails> {
  const job = await prisma.job.findUnique({ where: { id } });
  if (!job) throw new Error('Job not found');

  // Security: Ensure user is either an Admin or belongs to the company that owns the job
  const isOwner = user.companyId === job.companyId;
  if (!hasMinimumRole(user, UserRole.Admin) && !isOwner) {
    throw new Error('Not authorized to update this job');
  }

  const { companyId, status, isCompleted, startDate, endDate, ...restData } = data;
  
  // Process date fields to ensure they're proper timestamps
  const updateData: any = { ...restData };
  
  if (status) {
    updateData.status = status as JobStatus;
  }
  
  if (typeof isCompleted === 'boolean') {
    updateData.isCompleted = isCompleted;
  }
  
  if (startDate) {
    const date = new Date(startDate);
    if (!isNaN(date.getTime())) {
      updateData.startDate = date.toISOString();
    }
  }
  
  if (endDate) {
    const date = new Date(endDate);
    if (!isNaN(date.getTime())) {
      updateData.endDate = date.toISOString();
    }
  }

  return prisma.job.update({
    where: { id },
    data: updateData,
    include: {
      company: true,
      shifts: {
        include: {
          assignedPersonnel: {
            include: {
              user: true,
            },
          },
        },
      },
    },
  });
}
export async function deleteJob(user: AuthenticatedUser, id: string): Promise<void> {
  const job = await prisma.job.findUnique({ where: { id } });
  if (!job) throw new Error('Job not found');

  // Security: Ensure user is either an Admin or belongs to the company that owns the job
  const isOwner = user.companyId === job.companyId;
  if (!hasMinimumRole(user, UserRole.Admin) && !isOwner) {
    throw new Error('Not authorized to delete this job');
  }

  await prisma.job.delete({
    where: { id },
  });
}

export async function createJob(
  user: AuthenticatedUser,
  data: {
    name: string;
    description?: string;
    companyId: string;
    startDate?: Date;
  }
): Promise<Job> {
  // Security: Ensure user is either an Admin or belongs to the company that owns the job
  const isOwner = user.companyId === data.companyId;
  if (!hasMinimumRole(user, UserRole.Admin) && !isOwner) {
    throw new Error('Not authorized to create a job for this company');
  }
  return prisma.job.create({
    data: {
      name: data.name,
      description: data.description,
      companyId: data.companyId,
      startDate: data.startDate,
    },
  });
}
