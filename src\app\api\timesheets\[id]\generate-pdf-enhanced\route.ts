import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UnifiedPDFGenerator, PDFGenerationOptions } from '@/lib/unified-pdf-generator';
import { pdfPerformanceMonitor } from '@/lib/pdf-performance-monitor';

// Ensure Node.js runtime for PDF generation
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// POST /api/timesheets/[id]/generate-pdf-enhanced - Enhanced PDF generation with performance monitoring
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let tracker: any = null;
  
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: timesheetId } = params;
    const body = await request.json().catch(() => ({}));

    // Parse options from request body
    const options: PDFGenerationOptions = {
      includeSignature: body.includeSignature || false,
      signatureType: body.signatureType || 'company',
      uploadToCloud: body.uploadToCloud || false,
      templateId: body.templateId || 'system-default',
      format: body.format || 'letter',
      orientation: body.orientation || 'portrait',
      quality: body.quality || 'standard',
      watermark: body.watermark,
      customFields: body.customFields || {}
    };

    // Start performance tracking
    tracker = pdfPerformanceMonitor.startTracking(timesheetId, options.templateId || 'system-default');

    // Check if timesheet exists and user has permission
    const timesheet = await prisma.timesheet.findUnique({
      where: { id: timesheetId },
      include: {
        shift: {
          include: {
            job: { 
              include: { company: true } 
            },
            assignedPersonnel: {
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    if (!timesheet) {
      tracker?.recordError();
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const canAccess = user.role === 'Admin' || user.role === 'Staff' ||
                     (user.role === 'CompanyUser' && timesheet.shift.job.companyId === user.companyId) ||
                     (user.role === 'CrewChief' && timesheet.shift.assignedPersonnel.some(p => p.userId === user.id));

    if (!canAccess) {
      tracker?.recordError();
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Validate signature requirements
    if (options.includeSignature) {
      if ((options.signatureType === 'company' || options.signatureType === 'both') && !timesheet.company_signature) {
        tracker?.recordError();
        return NextResponse.json(
          { error: 'Company signature required but not available' },
          { status: 400 }
        );
      }
      
      if ((options.signatureType === 'manager' || options.signatureType === 'both') && !timesheet.manager_signature) {
        tracker?.recordError();
        return NextResponse.json(
          { error: 'Manager signature required but not available' },
          { status: 400 }
        );
      }
    }

    // Generate PDF using unified generator
    const generator = new UnifiedPDFGenerator(timesheetId);
    const pdfBuffer = await generator.generatePDF(options);
    const pdfUrl = await generator.generateAndStore(options);

    // Generate filename
    const companyName = timesheet.shift.job.company.name.replace(/\s+/g, '-');
    const date = new Date(timesheet.shift.date).toISOString().split('T')[0];
    const signatureStatus = options.includeSignature ? 'signed' : 'unsigned';
    const filename = `timesheet-${companyName}-${date}-${signatureStatus}.pdf`;

    // Update timesheet record based on signature status
    const updateData: any = {};
    if (options.includeSignature) {
      updateData.signed_pdf_url = pdfUrl;
    } else {
      updateData.unsigned_pdf_url = pdfUrl;
    }

    await prisma.timesheet.update({
      where: { id: timesheetId },
      data: updateData
    });

    // Use actual PDF buffer for performance tracking
    tracker?.finish(pdfBuffer, options.includeSignature);

    return NextResponse.json({
      success: true,
      message: 'PDF generated successfully',
      filename: filename,
      pdfUrl: pdfUrl,
      options: {
        includeSignature: options.includeSignature,
        signatureType: options.signatureType,
        templateId: options.templateId,
        format: options.format,
        quality: options.quality
      },
      metadata: {
        generatedAt: new Date().toISOString(),
        generatedBy: user.id,
        timesheetId: timesheetId
      }
    });

  } catch (error) {
    tracker?.recordError();
    console.error('Enhanced PDF generation error:', error);
    
    return NextResponse.json(
      { 
        error: 'PDF generation failed', 
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// GET /api/timesheets/[id]/generate-pdf-enhanced - Get PDF generation status and options
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: timesheetId } = params;

    // Get timesheet with signature status
    const timesheet = await prisma.timesheet.findUnique({
      where: { id: timesheetId },
      select: {
        id: true,
        company_signature: true,
        manager_signature: true,
        unsigned_pdf_url: true,
        signed_pdf_url: true,
        status: true,
        shift: {
          select: {
            job: {
              select: {
                companyId: true,
                company: {
                  select: {
                    name: true
                  }
                }
              }
            },
            assignedPersonnel: {
              select: {
                userId: true
              }
            }
          }
        }
      }
    });

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const canAccess = user.role === 'Admin' || user.role === 'Staff' ||
                     (user.role === 'CompanyUser' && timesheet.shift.job.companyId === user.companyId) ||
                     (user.role === 'CrewChief' && timesheet.shift.assignedPersonnel.some(p => p.userId === user.id));

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get performance metrics for this timesheet
    const performanceMetrics = pdfPerformanceMonitor.getTimesheetMetrics(timesheetId);

    return NextResponse.json({
      timesheetId: timesheetId,
      status: {
        hasCompanySignature: !!timesheet.company_signature,
        hasManagerSignature: !!timesheet.manager_signature,
        hasUnsignedPdf: !!timesheet.unsigned_pdf_url,
        hasSignedPdf: !!timesheet.signed_pdf_url,
        timesheetStatus: timesheet.status
      },
      availableOptions: {
        signatureTypes: [
          ...(timesheet.company_signature ? ['company'] : []),
          ...(timesheet.manager_signature ? ['manager'] : []),
          ...(timesheet.company_signature && timesheet.manager_signature ? ['both'] : [])
        ],
        formats: ['letter', 'a4'],
        orientations: ['portrait', 'landscape'],
        qualities: ['draft', 'standard', 'high']
      },
      performanceMetrics: {
        totalGenerations: performanceMetrics.length,
        lastGeneration: performanceMetrics[performanceMetrics.length - 1] || null,
        averageTime: performanceMetrics.length > 0 
          ? performanceMetrics.reduce((sum, m) => sum + m.generationTime, 0) / performanceMetrics.length 
          : 0
      }
    });

  } catch (error) {
    console.error('Error getting PDF generation status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}