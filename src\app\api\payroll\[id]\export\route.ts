import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@prisma/client';
import * as XLSX from 'xlsx';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(req);
    if (!user || user.role !== UserRole.Admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(req.url);
    const format = url.searchParams.get('format') || 'xlsx';
    const userId = url.searchParams.get('userId');

    const whereClause: any = { periodId: id };
    if (userId) {
      whereClause.userId = userId;
    }

    const entries = await prisma.payrollEntry.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            payrollType: true,
            ssnLast4: true,
            addressLine1: true,
            addressLine2: true,
            city: true,
            state: true,
            postalCode: true,
          }
        },
        period: {
          select: {
            id: true,
            startDate: true,
            endDate: true,
            status: true,
          }
        }
      }
    });

    const period = entries[0]?.period;
    if (!period) {
      return NextResponse.json({ error: 'Period not found' }, { status: 404 });
    }

    // Format data for export
    const exportData = entries.map(entry => ({
      'Employee Name': entry.user.name,
      'Employee Email': entry.user.email,
      'SSN Last 4': entry.user.ssnLast4 ? `****${entry.user.ssnLast4}` : '',
      'Address': [
        entry.user.addressLine1,
        entry.user.addressLine2,
        entry.user.city,
        entry.user.state,
        entry.user.postalCode
      ].filter(Boolean).join(', '),
      'Payroll Type': entry.user.payrollType,
      'Hourly Rate': (entry.hourlyRateCents / 100).toFixed(2),
      'Regular Hours': entry.regularHours.toFixed(2),
      'Overtime Hours': entry.overtimeHours.toFixed(2),
      'Double Time Hours': entry.doubletimeHours.toFixed(2),
      'Total Hours': entry.totalHours.toFixed(2),
      'Gross Pay': (entry.grossPayCents / 100).toFixed(2),
      'Period Start': period.startDate.toISOString().split('T')[0],
      'Period End': period.endDate.toISOString().split('T')[0],
    }));

    if (format === 'xlsx') {
      // Create Excel workbook
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);
      
      // Add summary row
      const summaryData = [{
        'Employee Name': 'TOTALS',
        'Employee Email': '',
        'SSN Last 4': '',
        'Address': '',
        'Payroll Type': '',
        'Hourly Rate': '',
        'Regular Hours': entries.reduce((sum, e) => sum + e.regularHours, 0).toFixed(2),
        'Overtime Hours': entries.reduce((sum, e) => sum + e.overtimeHours, 0).toFixed(2),
        'Double Time Hours': entries.reduce((sum, e) => sum + e.doubletimeHours, 0).toFixed(2),
        'Total Hours': entries.reduce((sum, e) => sum + e.totalHours, 0).toFixed(2),
        'Gross Pay': (entries.reduce((sum, e) => sum + e.grossPayCents, 0) / 100).toFixed(2),
        'Period Start': '',
        'Period End': '',
      }];
      
      XLSX.utils.sheet_add_json(ws, summaryData, { origin: -1 });
      XLSX.utils.book_append_sheet(wb, ws, 'Payroll');
      
      const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename=payroll-${id}${userId ? '-' + userId : ''}.xlsx`,
        },
      });
    } else {
      // CSV format
      const headers = Object.keys(exportData[0] || {});
      const csvRows = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => {
            const value = (row as any)[header] || '';
            return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
          }).join(',')
        )
      ];
      
      // Add totals row
      const totalsRow = [
        'TOTALS', '', '', '', '', '',
        entries.reduce((sum, e) => sum + e.regularHours, 0).toFixed(2),
        entries.reduce((sum, e) => sum + e.overtimeHours, 0).toFixed(2),
        entries.reduce((sum, e) => sum + e.doubletimeHours, 0).toFixed(2),
        entries.reduce((sum, e) => sum + e.totalHours, 0).toFixed(2),
        (entries.reduce((sum, e) => sum + e.grossPayCents, 0) / 100).toFixed(2),
        '', ''
      ];
      csvRows.push(totalsRow.join(','));
      
      const csvContent = csvRows.join('\n');
      
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename=payroll-${id}${userId ? '-' + userId : ''}.csv`,
        },
      });
    }
  } catch (error) {
    console.error('Error exporting payroll:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}