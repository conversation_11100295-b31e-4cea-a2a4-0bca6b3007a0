"use client";

import React, { useState, useContext, createContext, useCallback } from 'react';
import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
  MutationCache,
  useQueryClient,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { toast } from '@/hooks/use-toast';
import { QUERY_CONFIG } from '@/lib/query-config';

interface PerformanceMetrics {
  cacheHitRate: string;
  averageQueryTime: number;
  activeQueries: number;
  backgroundRefreshes: number;
  memoryUsage: number;
  errorRate: string;
}

interface UltraPerformanceContextType {
  performanceMetrics: PerformanceMetrics;
  optimizationLevel: 'conservative' | 'balanced' | 'aggressive';
  setOptimizationLevel: (level: 'conservative' | 'balanced' | 'aggressive') => void;
  triggerMemoryCleanup: () => void;
  pauseBackgroundRefresh: () => void;
  resumeBackgroundRefresh: () => void;
  getDetailedStats: () => any;
}

const UltraPerformanceContext = createContext<UltraPerformanceContextType | null>(null);

const createUltraPerformanceQueryClient = (optimizationLevel: 'conservative' | 'balanced' | 'aggressive') => {
  const queryCache = new QueryCache({
    onError: (error: any, query) => {
      if (error instanceof Error) {
        const isUserFacingError = !error.message.includes('fetch') &&
                                  !error.message.includes('Network') &&
                                  !error.message.includes('AbortError') &&
                                  !error.message.includes('background');

        const isImportantQuery = query.queryKey.some(key =>
          typeof key === 'string' &&
          ['shifts', 'users', 'timesheets', 'companies'].includes(key)
        );

        if (isUserFacingError && isImportantQuery) {
         console.error(error);
        }
      }
    },
  });

  const mutationCache = new MutationCache({
    onError: (error: any, variables, context, mutation) => {
      if (error instanceof Error) {
        toast({
          title: "Operation Failed",
          description: error.message || "Please try again",
          variant: "destructive",
        });
      }
    },
    onSuccess: (data, variables, context, mutation: any) => {
      const mutationKey = mutation.options.mutationKey;
      if (mutationKey && Array.isArray(mutationKey)) {
        const entityType = mutationKey[0];
        const queryClient = new QueryClient();
        queryClient.invalidateQueries({ queryKey: [entityType] });
      }
    },
  });

  const getOptimizedConfig = () => {
    switch (optimizationLevel) {
      case 'conservative':
        return {
          staleTime: QUERY_CONFIG.STALE_TIMES.SEMI_STATIC,
          gcTime: QUERY_CONFIG.CACHE_TIMES.LONG,
          refetchOnWindowFocus: true,
          refetchOnMount: true,
          refetchOnReconnect: true,
          retry: 2,
        };
      case 'aggressive':
        return {
          staleTime: QUERY_CONFIG.STALE_TIMES.STATIC,
          gcTime: QUERY_CONFIG.CACHE_TIMES.LONG,
          refetchOnWindowFocus: false,
          refetchOnMount: false,
          refetchOnReconnect: true,
          retry: 0,
        };
      default: // balanced
        return {
          staleTime: QUERY_CONFIG.STALE_TIMES.DYNAMIC,
          gcTime: QUERY_CONFIG.CACHE_TIMES.MEDIUM,
          refetchOnWindowFocus: false,
          refetchOnMount: false,
          refetchOnReconnect: true,
          retry: 1,
        };
    }
  };

  const config = getOptimizedConfig();

  return new QueryClient({
    queryCache,
    mutationCache,
    defaultOptions: {
      queries: {
        ...config,
        networkMode: 'online',
        retryDelay: (attemptIndex) => {
          const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000);
          const jitter = Math.random() * 2500;
          return baseDelay + jitter;
        },
      },
      mutations: {
        retry: 1,
        networkMode: 'online',
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      },
    },
  });
};

export const UltraPerformanceQueryProvider = ({
  children,
  initialOptimizationLevel = 'balanced'
}: {
  children: React.ReactNode;
  initialOptimizationLevel?: 'conservative' | 'balanced' | 'aggressive';
}) => {
  const [optimizationLevel, setOptimizationLevel] = useState(initialOptimizationLevel);
  const [queryClient] = useState(() => createUltraPerformanceQueryClient(optimizationLevel));
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    cacheHitRate: '0%',
    averageQueryTime: 0,
    activeQueries: 0,
    backgroundRefreshes: 0,
    memoryUsage: 0,
    errorRate: '0%',
  });

  const contextValue: UltraPerformanceContextType = {
    performanceMetrics,
    optimizationLevel,
    setOptimizationLevel,
    triggerMemoryCleanup: () => {},
    pauseBackgroundRefresh: () => {},
    resumeBackgroundRefresh: () => {},
    getDetailedStats: () => ({}),
  };

  return (
    <UltraPerformanceContext.Provider value={contextValue}>
      <QueryClientProvider client={queryClient}>
        {children}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools
            initialIsOpen={false}
            position="bottom"
          />
        )}
      </QueryClientProvider>
    </UltraPerformanceContext.Provider>
  );
};

export const useUltraPerformance = () => {
  const context = useContext(UltraPerformanceContext);
  if (!context) {
    throw new Error('useUltraPerformance must be used within UltraPerformanceQueryProvider');
  }
  return context;
};

export const PerformanceMonitor = () => null;

export const useEnhancedCacheManagement = () => {
  const queryClient = useQueryClient();
  return {
    invalidateByPattern: useCallback(async (pattern: string) => {
      await queryClient.invalidateQueries({ predicate: (query: any) => query.queryKey[0].includes(pattern) });
    }, [queryClient]),
    preloadPageData: useCallback(async (pagePath: string) => {}, [queryClient]),
    emergencyCacheClear: useCallback(async () => {}, [queryClient]),
    intelligentPrefetch: useCallback(async (userActions: string[]) => {}, [queryClient]),
  };
};
