"use client";

import { EnhancedStatusBadge as UnifiedStatusBadge } from "@/components/ui/enhanced-status-badge";

interface WorkerStatusBadgeProps {
  status: string | undefined;
}

export function WorkerStatusBadge({ status }: WorkerStatusBadgeProps) {
  if (!status) return null;
  
  const s = status.toLowerCase();
  
  // Map status strings to our unified status keys
  let unifiedStatus = 'assigned'; // default
  
  if (s.includes('clocked in')) {
    unifiedStatus = 'in-progress';
  } else if (s.includes('not clocked in') || s.includes('assigned')) {
    unifiedStatus = 'pending';
  } else if (s.includes('ended')) {
    unifiedStatus = 'completed';
  } else if (s.includes('break')) {
    unifiedStatus = 'draft';
  } else if (s.includes('no show')) {
    unifiedStatus = 'cancelled';
  }
  
  return <UnifiedStatusBadge status={unifiedStatus as any} size="sm" />;
}
