import { PrismaClient, UserRole } from '@prisma/client';

type WorkerSeed = { code: string; name: string; color: string; sortOrder: number };
const WORKER_TYPES_SEED: WorkerSeed[] = [
  { code: '<PERSON>', name: 'Crew Chief', color: '#0ea5e9', sortOrder: 0 },
  { code: 'RG', name: '<PERSON>ig<PERSON>', color: '#ef4444', sortOrder: 1 },
  { code: 'RFO', name: 'Reach Fork Operator', color: '#f59e0b', sortOrder: 2 },
  { code: 'FO', name: 'Fork Operator', color: '#22c55e', sortOrder: 3 },
  { code: 'SH', name: 'Stage Hand', color: '#3b82f6', sortOrder: 4 },
];
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

// Helper function to parse time strings like "8a", "1130p", "11p"
const parseTime = (date: Date, timeStr: string): Date => {
  const newDate = new Date(date);
  // Standardize time format by removing colons for parsing
  const time = timeStr.toLowerCase().replace('a', '').replace('p', '').replace(':', '');
  let hour = 0;
  let minute = 0;

  if (time.length > 2) {
    const hourStr = time.slice(0, -2);
    const minuteStr = time.slice(-2);
    hour = parseInt(hourStr);
    minute = parseInt(minuteStr);
  } else {
    hour = parseInt(time);
  }

  if (timeStr.toLowerCase().includes('p') && hour !== 12) {
    hour += 12;
  }
  if (timeStr.toLowerCase().includes('a') && hour === 12) {
    hour = 0; // Midnight case for 12a
  }

  newDate.setHours(hour, minute, 0, 0);
  return newDate;
};

// Helper function to create shifts from time strings (handles splits)
const createShiftsFromTime = (date: Date, timeStr: string) => {
  const shifts = [];
  const timeParts = timeStr.split(',').map(s => s.trim());

  for (const part of timeParts) {
    const [startStr, endStr] = part.split('-');
    const startTime = parseTime(date, startStr);
    const endTime = parseTime(date, endStr);

    // Handle overnight shifts
    if (endTime <= startTime) {
      endTime.setDate(endTime.getDate() + 1);
    }
    shifts.push({ startTime, endTime });
  }
  return shifts;
};

async function main() {
  console.log('🌱 Starting database seeding...');

  // 1. Clear existing data
  console.log('🧹 Clearing existing data...');
  await prisma.timeEntry.deleteMany({});
  await prisma.assignedPersonnel.deleteMany({});
  await prisma.timesheet.deleteMany({});
  await prisma.workerRequirement.deleteMany({});
  await prisma.shift.deleteMany({});
  await prisma.job.deleteMany({});
  await prisma.user.deleteMany({ where: { role: { notIn: [UserRole.Admin] } } });
  await prisma.company.deleteMany({});
  await prisma.workerType.deleteMany({});
  console.log('🎯 Data cleared successfully!');

  // 2. Setup common data
  const password = 'password123';
  const hashedPassword = await bcrypt.hash(password, 12);
  const company = await prisma.company.create({
    data: { name: 'Show Imaging' },
  });
  console.log(`🏢 Created Company: ${company.name}`);

  // Seed worker types
  console.log('🔧 Seeding Worker Types...');
  for (const wt of WORKER_TYPES_SEED) {
    await prisma.workerType.upsert({
      where: { code: wt.code },
      update: { name: wt.name, color: wt.color, sortOrder: wt.sortOrder, isActive: true },
      create: { code: wt.code, name: wt.name, color: wt.color, sortOrder: wt.sortOrder, isActive: true },
    });
  }
  console.log('✅ Worker Types seeded.');

  const job = await prisma.job.create({
    data: {
      name: 'CRSSD 2023',
      description: 'Job #: L012-23',
      location: 'Waterfront Park, 939 W Kalmia St, San Diego, CA 92101',
      status: 'Pending',
      companyId: company.id,
    },
  });
  console.log(`📄 Created Job: ${job.name}`);

  // 3. Create users
  console.log('👥 Creating users...');
  const createUser = (name: string, email: string, role: UserRole, phone?: string) =>
    prisma.user.create({
      data: {
        name,
        email,
        phone,
        passwordHash: hashedPassword,
        role,
        crew_chief_eligible: role === UserRole.CrewChief || role === UserRole.Manager,
        companyId: company.id,
        avatarUrl: `https://i.pravatar.cc/150?u=${email}`,
      },
    });

  const crewChiefs = {
    tyrone: await createUser('Tyrone CC', '<EMAIL>', UserRole.CrewChief),
    jeff: await createUser('Jeff CC', '<EMAIL>', UserRole.CrewChief),
    amy: await createUser('Amy CC', '<EMAIL>', UserRole.CrewChief),
    darrell: await createUser('Darrell CC', '<EMAIL>', UserRole.CrewChief),
    allison: await createUser('Allison CC', '<EMAIL>', UserRole.CrewChief),
    union: await createUser('Union CC', '<EMAIL>', UserRole.CrewChief),
  };
  
  await createUser('Mark Gonzales', '<EMAIL>', UserRole.CompanyUser, '************');

  console.log('✅ Users created.');

  const colorToCrewChief: { [key: string]: any } = {
    blue: crewChiefs.union,
    yellow: crewChiefs.allison,
    green: crewChiefs.jeff,
    red: crewChiefs.amy,
    orange: crewChiefs.darrell,
    grey: crewChiefs.tyrone,
  };

  // 4. Define schedule from all images
  const schedule = [
    {
        date: '2023-02-27',
        shifts: [
            { name: 'CC Darrell', color: 'yellow', time: '5p-11p', osha: 1, forks: 3 },
            { name: 'KLEEGE', color: 'green', time: '8a-7p', mgr: 1, osha: 12, forks: 2 },
            { name: 'KLEEGE - CROSSOVER/FOH', color: 'red', time: '12p-6p', osha: 12 },
            { name: 'SNOW IMAGING - PRE-RIG', color: 'red', time: '1p-6p', osha: 6 },
            { name: 'ETCP RIGGER', color: 'blue', time: '8a-6p', riggers: 2 },
            { name: 'PALMS & STEPS', color: 'yellow', time: '5p-10p', osha: 1 },
            { name: '3 trucks, bldg (2) SL100 SP-11P', color: 'yellow', time: '5p-11p', osha: 12, forks: 2 },
            { name: 'IT', color: 'red', time: '8a-7p', osha: 2 },
            { name: 'PowerTrip', color: 'grey', time: '8a-7p', osha: 3 },
        ],
    },
    {
        date: '2023-03-01',
        shifts: [
            { name: 'SUP', color: 'grey', time: '8a-7p', mgr: 1 },
            { name: 'ETCP RIGGER', color: 'blue', time: '8a-7p', riggers: 6 },
            { name: 'MAIN STAGE', color: 'green', time: '8a-7p', osha: 10, forks: 2 },
            { name: 'KLEEGE', color: 'green', time: '8a-7p', osha: 6 },
            { name: 'PALMS', color: 'yellow', time: '8a-7p', osha: 14, forks: 1 },
            { name: 'IT', color: 'red', time: '8a-7p', osha: 4 },
            { name: 'PowerTrip', color: 'grey', time: '8a-7p', osha: 3 },
            { name: 'SITE VIDEO', color: 'red', time: '4p-9p', osha: 6, forks: 1 },
        ],
    },
    {
        date: '2023-03-02',
        shifts: [
            { name: 'SUP', color: 'grey', time: '8a-7p', mgr: 1 },
            { name: 'ETCP RIGGER', color: 'blue', time: '8a-7p', riggers: 6 },
            { name: 'MAIN STAGE', color: 'green', time: '8a-7p', osha: 14, forks: 2 },
            { name: 'PALMS', color: 'yellow', time: '8a-7p', osha: 6, forks: 1 },
            { name: 'SITE LIGHTING / VIDEO', color: 'red', time: '8a-7p', osha: 12 },
            { name: 'IT', color: 'red', time: '8a-7p', osha: 4 },
            { name: 'SIGNAGE', color: 'red', time: '8a-7p', osha: 4 },
            { name: 'PowerTrip', color: 'grey', time: '8a-7p', osha: 3 },
        ],
    },
    {
        date: '2023-03-03',
        shifts: [
            { name: 'MAIN STAGE', color: 'grey', time: '8a-7p', osha: 1 },
            { name: 'PALMS', color: 'yellow', time: '8a-7p', osha: 6, forks: 2 },
            { name: 'SITE LIGHTING / VIDEO', color: 'red', time: '8a-7p', osha: 6 },
            { name: 'ADA VIEWING PLATFORMS', color: 'yellow', time: '8a-1p', osha: 1 },
            { name: 'IT', color: 'red', time: '8a-1p', osha: 4 },
            { name: 'SIGNAGE', color: 'red', time: '8a-7p', osha: 4 },
            { name: 'PowerTrip', color: 'grey', time: '8a-7p', osha: 2 },
            { name: 'MAIN STAGE - DECK HANDS / BARRICADE', color: 'green', time: '12p-9p', osha: 6, forks: 1 },
        ],
    },
    {
        date: '2023-03-04',
        shifts: [
            { name: 'MAIN STAGE', color: 'green', time: '11:30a-11p', osha: 6, forks: 1 },
            { name: 'MAIN STAGE - HEADLINER', color: 'red', time: '7a-1a', osha: 8, forks: 1 },
            { name: 'PRE-SHOW TASK CREW', color: 'red', time: '8a-1p', osha: 8 },
            { name: 'PALMS', color: 'yellow', time: '8a-1p', osha: 6 },
            { name: 'MAIN STAGE - ODESZA IN', color: 'red', time: '11p-3a', osha: 12 },
            { name: 'ETCP RIGGER', color: 'blue', time: '11p-3a', riggers: 2 },
        ],
    },
    {
      date: '2023-03-05',
      shifts: [
        { name: 'MAIN STAGE', color: 'green', time: '11:30a-11:30p', osha: 8, forks: 1 },
        { name: 'MAIN STAGE - HEADLINER', color: 'red', time: '7a-12p,6p-1a', osha: 8, forks: 1 },
      ],
    },
    {
      date: '2023-03-06',
      shifts: [
        { name: 'SUP', color: 'grey', time: '8a-7p', mgr: 1 },
        { name: 'KLEEGE - OUT SAM 575', color: 'green', time: '8a-7p', shands: 12, forks: 2 },
        { name: 'KLEEGE - OUT CROSSOVER / FOH', color: 'red', time: '8a-7p', shands: 12 },
        { name: 'KLEEGE - OUT SL100 / ADA', color: 'yellow', time: '8a-7p', shands: 1, osha: 8, forks: 1 },
        { name: 'PALMS - OUT', color: 'yellow', time: '8a-7p', osha: 10, forks: 1 },
        { name: 'ETCP RIGGER - OUT', color: 'blue', time: '8a-7p', riggers: 4 },
        { name: 'PowerTrip - OUT', color: 'grey', time: '8a-7p', osha: 4 },
        { name: 'SITE LIGHTING / VIDEO - OUT', color: 'yellow', time: '8a-7p', osha: 6 },
        { name: 'IT - OUT', color: 'yellow', time: '8a-7p', osha: 3 },
        { name: 'FINAL SWEEP CREW', color: 'red', time: '3p-11p', osha: 6, forks: 2 },
      ],
    },
  ];

  // 5. Process schedule
  console.log('🗓️ Processing schedule...');
  for (const day of schedule) {
    const shiftDate = new Date(day.date);
    console.log(`\n--- Processing Date: ${shiftDate.toDateString()} ---`);

    for (const shiftInfo of day.shifts) {
      const shiftTimes = createShiftsFromTime(shiftDate, shiftInfo.time);
      const cc = colorToCrewChief[shiftInfo.color] || (shiftInfo as any).cc;

      for (const { startTime, endTime } of shiftTimes) {
        const isSupShift = shiftInfo.name === 'SUP';
        const shift = await prisma.shift.create({
          data: {
            jobId: job.id,
            date: shiftDate,
            startTime,
            endTime,
            description: shiftInfo.name,
            status: 'Pending',
            requiredCrewChiefs: 1,
            requiredStagehands: ((shiftInfo as any).shands || 0) + ((shiftInfo as any).osha || 0),
            requiredForkOperators: (shiftInfo as any).forks || 0,
            requiredReachForkOperators: (shiftInfo as any).reachForks || 0,
            requiredRiggers: (shiftInfo as any).riggers || 0,
          },
        });
        
        // Seed normalized WorkerRequirements from counts
        const reqs: Array<{ workerTypeCode: string; requiredCount: number }> = [
          { workerTypeCode: 'CC', requiredCount: 1 },
          { workerTypeCode: 'SH', requiredCount: (((shiftInfo as any).shands || 0) + ((shiftInfo as any).osha || 0)) },
          { workerTypeCode: 'FO', requiredCount: ((shiftInfo as any).forks || 0) },
          { workerTypeCode: 'RFO', requiredCount: ((shiftInfo as any).reachForks || 0) },
          { workerTypeCode: 'RG', requiredCount: ((shiftInfo as any).riggers || 0) },
        ];

        for (const r of reqs) {
          if (r.requiredCount > 0) {
            await prisma.workerRequirement.upsert({
              where: { shiftId_workerTypeCode: { shiftId: shift.id, workerTypeCode: r.workerTypeCode } },
              update: { requiredCount: r.requiredCount },
              create: { shiftId: shift.id, workerTypeCode: r.workerTypeCode, requiredCount: r.requiredCount },
            });
          }
        }
        console.log(`    → 🗓️ Created Shift: ${shift.description} @ ${startTime.toISOString()}`);

        if (cc) {
          await prisma.assignedPersonnel.create({
            data: { shiftId: shift.id, userId: cc.id, roleCode: 'CC', status: 'Assigned' },
          });
          console.log(`      → 👷 Assigned ${isSupShift ? 'Manager' : 'MGR'}: ${cc.name}`);
        }
      }
    }
  }

  console.log('✅ Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
