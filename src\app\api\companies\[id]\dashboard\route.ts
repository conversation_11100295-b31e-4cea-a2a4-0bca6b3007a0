import { NextResponse } from 'next/server';
import { dbQueryService } from '@/lib/services/database-query-service';
import { getAuthenticatedUser } from '@/lib/auth-server';
import { UserRole } from '@/lib/types';

export async function GET(req: Request, { params }: { params: { id: string } }) {
  const { id } = params;

  if (!id || typeof id !== 'string') {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
  }

  try {
    const user = await getAuthenticatedUser();
    if (!user || (user.role !== UserRole.Admin && user.companyId !== id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Use optimized database query service
    const dashboardData = await dbQueryService.getCompanyDashboardOptimized(id);

    return NextResponse.json({
      success: true,
      ...dashboardData,
    });
  } catch (error) {
    console.error(`Error fetching company dashboard data for ${id}:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
