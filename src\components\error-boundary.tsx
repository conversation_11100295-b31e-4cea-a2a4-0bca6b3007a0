/**
 * Error Boundary Components
 * 
 * Provides graceful error handling for React components with:
 * - Automatic error recovery
 * - User-friendly error messages
 * - Error reporting
 * - Fallback UI components
 */

"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw, Home, Bug, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  maxRetries?: number;
  showErrorDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface ErrorFallbackProps {
  error: Error;
  errorInfo: ErrorInfo;
  onRetry: () => void;
  onGoHome: () => void;
  retryCount: number;
  maxRetries: number;
  showErrorDetails: boolean;
  level: 'page' | 'component' | 'critical';
}

// ============================================================================
// ERROR REPORTING UTILITY
// ============================================================================

const reportError = async (error: Error, errorInfo: ErrorInfo, errorId: string) => {
  try {
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          errorId,
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        }),
      });
    } else {
      // In development, log to console
      console.group(`🚨 Error Boundary Caught Error [${errorId}]`);
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  } catch (reportingError) {
    console.error('Failed to report error:', reportingError);
  }
};

// ============================================================================
// ERROR FALLBACK COMPONENTS
// ============================================================================

const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  onRetry,
  onGoHome,
  retryCount,
  maxRetries,
  showErrorDetails,
  level,
}) => {
  const [showDetails, setShowDetails] = React.useState(false);
  
  const getErrorTitle = () => {
    switch (level) {
      case 'critical':
        return 'Critical System Error';
      case 'page':
        return 'Page Error';
      case 'component':
        return 'Component Error';
      default:
        return 'Something went wrong';
    }
  };
  
  const getErrorDescription = () => {
    switch (level) {
      case 'critical':
        return 'A critical error has occurred that prevents the application from functioning properly.';
      case 'page':
        return 'This page encountered an error and cannot be displayed properly.';
      case 'component':
        return 'A component on this page encountered an error.';
      default:
        return 'An unexpected error occurred.';
    }
  };
  
  const canRetry = retryCount < maxRetries;
  
  return (
    <div className={`flex items-center justify-center p-4 ${
      level === 'critical' ? 'min-h-screen bg-background' : 'min-h-[400px]'
    }`}>
      <Card className={`w-full max-w-2xl ${
        level === 'critical' ? 'border-destructive' : 'border-border'
      }`}>
        <CardHeader>
          <div className="flex items-center gap-3">
            <AlertCircle className={`h-6 w-6 ${
              level === 'critical' ? 'text-destructive' : 'text-warning'
            }`} />
            <div>
              <CardTitle className="text-lg">{getErrorTitle()}</CardTitle>
              <CardDescription className="mt-1">
                {getErrorDescription()}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error Message */}
          <Alert className={level === 'critical' ? 'border-destructive bg-destructive/10' : ''}>
            <AlertDescription>
              <strong>Error:</strong> {error.message || 'Unknown error occurred'}
            </AlertDescription>
          </Alert>
          
          {/* Retry Information */}
          {retryCount > 0 && (
            <Alert>
              <AlertDescription>
                This error has occurred {retryCount} time{retryCount !== 1 ? 's' : ''}.
                {canRetry ? ' You can try again.' : ' Maximum retry attempts reached.'}
              </AlertDescription>
            </Alert>
          )}
          
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            {canRetry && (
              <Button onClick={onRetry} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            )}
            
            <Button 
              variant="outline" 
              onClick={onGoHome}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Go to Dashboard
            </Button>
            
            <Button 
              variant="ghost" 
              onClick={() => window.location.reload()}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Reload Page
            </Button>
          </div>
          
          {/* Error Details (Collapsible) */}
          {showErrorDetails && (
            <Collapsible open={showDetails} onOpenChange={setShowDetails}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <Bug className="h-4 w-4" />
                  Technical Details
                  {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="mt-3">
                <div className="space-y-3">
                  <div className="bg-muted p-3 rounded-md">
                    <h4 className="font-medium text-sm mb-2">Error Stack:</h4>
                    <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                      {error.stack}
                    </pre>
                  </div>
                  
                  {errorInfo.componentStack && (
                    <div className="bg-muted p-3 rounded-md">
                      <h4 className="font-medium text-sm mb-2">Component Stack:</h4>
                      <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                        {errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// ============================================================================
// SIMPLE ERROR FALLBACKS
// ============================================================================

export const SimpleErrorFallback: React.FC<{
  message?: string;
  onRetry?: () => void;
}> = ({ message = "Something went wrong", onRetry }) => (
  <Alert className="border-destructive bg-destructive/10">
    <AlertCircle className="h-4 w-4" />
    <AlertDescription className="flex items-center justify-between">
      <span>{message}</span>
      {onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

export const ComponentErrorFallback: React.FC<{
  componentName?: string;
  onRetry?: () => void;
}> = ({ componentName = "Component", onRetry }) => (
  <div className="flex items-center justify-center p-8 border border-dashed border-muted-foreground/30 rounded-lg bg-muted/20">
    <div className="text-center">
      <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
      <h3 className="font-medium text-sm mb-2">{componentName} Error</h3>
      <p className="text-xs text-muted-foreground mb-4">
        This component encountered an error and cannot be displayed.
      </p>
      {onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      )}
    </div>
  </div>
);

// ============================================================================
// ERROR BOUNDARY CLASS COMPONENT
// ============================================================================

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;
  
  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
    };
  }
  
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });
    
    // Report error
    reportError(error, errorInfo, this.state.errorId);
    
    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }
  
  handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
      }));
      
      // Auto-retry with exponential backoff for component-level errors
      if (this.props.level === 'component') {
        const delay = Math.min(1000 * Math.pow(2, this.state.retryCount), 10000);
        this.retryTimeoutId = setTimeout(() => {
          this.forceUpdate();
        }, delay);
      }
    }
  };
  
  handleGoHome = () => {
    window.location.href = '/dashboard';
  };
  
  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }
  
  render() {
    if (this.state.hasError && this.state.error && this.state.errorInfo) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Use default fallback
      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onRetry={this.handleRetry}
          onGoHome={this.handleGoHome}
          retryCount={this.state.retryCount}
          maxRetries={this.props.maxRetries || 3}
          showErrorDetails={this.props.showErrorDetails ?? process.env.NODE_ENV === 'development'}
          level={this.props.level || 'component'}
        />
      );
    }
    
    return this.props.children;
  }
}

// ============================================================================
// FUNCTIONAL ERROR BOUNDARY WRAPPER
// ============================================================================

export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// ============================================================================
// SPECIALIZED ERROR BOUNDARIES
// ============================================================================

export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="page" maxRetries={2} showErrorDetails>
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{ 
  children: ReactNode;
  componentName?: string;
}> = ({ children, componentName }) => (
  <ErrorBoundary 
    level="component" 
    maxRetries={3}
    fallback={<ComponentErrorFallback componentName={componentName} />}
  >
    {children}
  </ErrorBoundary>
);

export const CriticalErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="critical" maxRetries={1} showErrorDetails>
    {children}
  </ErrorBoundary>
);

// ============================================================================
// QUERY ERROR BOUNDARY (for React Query errors)
// ============================================================================

export const QueryErrorBoundary: React.FC<{
  children: ReactNode;
  fallback?: (error: Error, retry: () => void) => ReactNode;
}> = ({ children, fallback }) => {
  return (
    <ErrorBoundary
      level="component"
      fallback={
        fallback ? (
          <div>
            {/* This will be replaced by the fallback function */}
          </div>
        ) : undefined
      }
      onError={(error) => {
        // Log query errors specifically
        console.error('Query Error:', error);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;