"use strict";
(
  () => {
    var a = {
      486: (e, t, r) => {
        var n = r(259);
        var s = r(628);
        var i = r(172);
        var o = r(769);
        var c = r(935);
        var l = r(434);
        var u = r(582);
        var d = r(523);
        var f = r(879);
        var h = r(976);
        var p = r(999);
        var m = r(117);
        var g = r(241);
        var v = r(294);
        var y = r(166);
        var b = r(468);
        var w = r(898);
        var k = r(672);
        var _ = r(216);
        const C = "5.0.120";
        const S = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && typeof navigator !== "undefined" && (navigator.userAgent.includes("Trident") || navigator.userAgent.includes("Edge"));
        const A = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && typeof navigator !== "undefined" && /Mobi|Tablet|Android|iPad|iPhone/.test(navigator.userAgent);
        const P = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && (() => {
          let e = false;
          if (typeof navigator !== "undefined" && navigator.userAgent) {
            const t = /AppleWebKit\/([0-9]+)\.([0-9]+)\.([0-9]+)/.exec(navigator.userAgent);
            if (t) {
              const r = parseInt(t[1], 10);
              const n = parseInt(t[2], 10);
              const s = parseInt(t[3], 10);
              e = r < 605 || r === 605 && n < 1 || r === 605 && n === 1 && s < 15;
            }
          }
          return e;
        })();
        const x = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && typeof navigator !== "undefined" && navigator.userAgent.includes("Safari") && !navigator.userAgent.includes("Chrome");
        const T = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && x && (typeof navigator !== "undefined" && /Version\/[1-9][0-9]\./.test(navigator.userAgent));
        const F = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && x && (typeof navigator !== "undefined" && /Version\/1([6-9])\./.test(navigator.userAgent));
        const O = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && (typeof navigator === "undefined" || !navigator.userAgent.includes("jsdom"));
        const E = typeof Response !== "undefined" && "body" in Response.prototype && typeof ReadableStream !== "undefined";
        const D = typeof AbortController !== "undefined";
        const I = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && globalThis.performance ? .now ? ? Date.now;
        const R = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && typeof BigInt64Array === "undefined";
        class j {
          constructor() {
            this._map = new Map;
            this.initialValue = undefined;
          }
          has(e) {
            return this._map.has(e);
          }
          get(e) {
            return this._map.get(e);
          }
          set(e, t) {
            this._map.set(e, t);
          }
          setForAll(e) {
            this.initialValue = e;
          }
          getArray() {
            const e = this.initialValue;
            if (e === undefined) {
              return undefined;
            }
            const t = [];
            for (let r = 0; r < e; r++) {
              t[r] = this._map.get(r) ? ? 1;
            }
            return t;
          }
        }(typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && Object.freeze(j);
        const L = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        class M {
          constructor(e) {
            this._id = `cl_${e}`;
            this._set = new Set;
          }
          add(e) {
            this._set.add(e);
          }
          remove(e) {
            this._set.delete(e);
          }
          isDone() {
            return this._set.size === 0;
          }
          cleanup() {}
        }(typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && Object.freeze(M);
        class N {
          constructor(e, t) {
            this.name = e;
            this.code = t;
          }
          static get values() {
            return Object.freeze({
              SUCCESS: new N("Success", 0),
              UNKNOWN_ERROR: new N("Unknown error", 1),
              UNSUPPORTED_FEATURE: new N("Unsupported feature", 2),
              INVALID_SIGNATURE: new N("Invalid signature", 3)
            });
          }
        }
        Object.freeze(N);
        class B {}
        class z {}
        class U {}
        const G = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
        const V = "Helvetica";
        const H = new WeakMap;
        let W = 1;

        function K(e) {
          const t = H.get(e);
          if (t) {
            return t;
          }
          const r = `unregistered_fn${W++}`;
          return H.set(e, r), r;
        }
        let q = 1;
        const X = new WeakMap;

        function Y(e) {
          const t = X.get(e);
          if (t) {
            return t;
          }
          const r = `unregistered_img${q++}`;
          return X.set(e, r), r;
        }
        let Z = 1;
        const Q = new WeakMap;

        function J(e) {
          const t = Q.get(e);
          if (t) {
            return t;
          }
          const r = `unregistered_smask${Z++}`;
          return Q.set(e, r), r;
        }
        let $ = 1;
        const ee = new WeakMap;

        function te(e) {
          const t = ee.get(e);
          if (t) {
            return t;
          }
          const r = `unregistered_gstate${$++}`;
          return ee.set(e, r), r;
        }
        const re = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ne = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const se = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ie = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const oe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ae = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ce = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const le = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ue = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const de = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const fe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const he = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const pe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const me = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ge = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ve = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ye = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const be = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const we = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const ke = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const _e = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Ce = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Se = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Ae = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Pe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const xe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Te = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Fe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
        const Oe = (typeof PDFJSDev === "undefined" || PDFJSDev.test("!PRODUCTION || GENERIC")) && class extends(String) {};
