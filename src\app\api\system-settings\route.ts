import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { UserRole } from '@prisma/client';

export async function POST(req: NextRequest) {
  const user = await getCurrentUser(req);
  if (!user || user.role !== UserRole.Admin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  const body = await req.json();
  const { key, value } = body || {};
  if (!key) return NextResponse.json({ error: 'key required' }, { status: 400 });
  const setting = await prisma.systemSetting.upsert({
    where: { key },
    update: { value },
    create: { key, value },
  });
  return NextResponse.json({ setting });
}

export async function GET(req: NextRequest) {
  const user = await getCurrentUser(req);
  if (!user || user.role !== UserRole.Admin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  const url = new URL(req.url);
  const key = url.searchParams.get('key') || undefined;
  if (key) {
    const setting = await prisma.systemSetting.findUnique({ where: { key } });
    return NextResponse.json({ setting });
  }
  const settings = await prisma.systemSetting.findMany();
  return NextResponse.json({ settings });
}
