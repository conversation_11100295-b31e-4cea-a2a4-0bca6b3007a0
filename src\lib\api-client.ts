import { NextResponse } from 'next/server';
import { getServerSession, type AuthOptions } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

// #region API Client
export interface ApiError extends Error {
  status?: number;
  details?: unknown;
  code?: string;
  statusCode?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
  };
}

interface RequestOptions {
  useCache?: boolean;
  cacheTime?: number;
  revalidate?: boolean;
}

// Unified cache for both server and client
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
const DEFAULT_CACHE_TTL = 5 * 60 * 1000;

function getFromCache<T>(key: string): T | null {
  const entry = cache.get(key);
  if (!entry) return null;

  const isStale = Date.now() - entry.timestamp > entry.ttl;
  if (isStale) {
    cache.delete(key);
    return null;
  }

  return entry.data;
}

function setToCache<T>(key: string, data: T, ttl: number): void {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  });

  // Clean up old entries periodically
  if (cache.size > 1000) {
    const now = Date.now();
    for (const [k, v] of Array.from(cache.entries())) {
      if (now - v.timestamp > v.ttl) {
        cache.delete(k);
      }
    }
  }
}

function invalidateCacheByPattern(pattern: RegExp): number {
  let count = 0;
  for (const key of Array.from(cache.keys())) {
    if (pattern.test(key)) {
      cache.delete(key);
      count++;
    }
  }
  return count;
}

async function apiRequest<T>(
  endpoint: string,
  options: any = {},
  cacheTTL: number = DEFAULT_CACHE_TTL
): Promise<T> {
  let token = null;
  if (typeof window !== 'undefined') {
    const getCookie = (name: string) => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop()?.split(';').shift();
      return null;
    }
    token = getCookie('__Secure-next-auth.session-token') || getCookie('next-auth.session-token');
  } else {
    const session = await getServerSession(authOptions as AuthOptions);
    token = session?.accessToken;
  }

  const url = endpoint.startsWith('/') ? `/api${endpoint}` : `/api/${endpoint}`;
  const cacheKey = `${options.method || 'GET'}:${url}:${JSON.stringify(options.body || {})}`;

  if (!options.method || options.method === 'GET') {
    const cached = getFromCache<T>(cacheKey);
    if (cached) {
      return cached;
    }
  }

  try {
    const baseUrl = typeof window === 'undefined'
      ? process.env.NEXTAUTH_URL || process.env.CLOUD_RUN_URL || 'http://localhost:3000'
      : '';

    const response = await fetch(`${baseUrl}${url}`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate, br',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      compress: true,
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error: ApiError = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      error.status = response.status;
      error.details = errorData;
      throw error;
    }

    const data: ApiResponse<T> = await response.json();
    if (data.success === false) { // Check for explicit false
      throw new Error(data.error?.message || 'API returned success: false');
    }

    if (!options.method || options.method === 'GET') {
      setToCache(cacheKey, data.data!, cacheTTL);
    }

    return data.data!;
  } catch (error) {
    console.error(`API request failed for ${url}:`, error);
    throw error;
  }
}

export const api = {
  get: <T>(endpoint: string, cacheTTL?: number): Promise<T> => apiRequest<T>(endpoint, { method: 'GET' }, cacheTTL),
  post: <T>(endpoint: string, data?: any): Promise<T> => apiRequest<T>(endpoint, { method: 'POST', body: data ? JSON.stringify(data) : undefined }),
  put: <T>(endpoint: string, data?: any): Promise<T> => apiRequest<T>(endpoint, { method: 'PUT', body: data ? JSON.stringify(data) : undefined }),
  delete: <T>(endpoint: string): Promise<T> => apiRequest<T>(endpoint, { method: 'DELETE' }),
  patch: <T>(endpoint: string, data?: any): Promise<T> => apiRequest<T>(endpoint, { method: 'PATCH', body: data ? JSON.stringify(data) : undefined }),
  getUserById: (id: string) => api.get(`/users/${id}`),
  getCompany: (id: string) => api.get(`/companies/${id}`),
  getTimesheet: (id: string) => api.get(`/timesheets/${id}`),
  getNotifications: () => api.get('/notifications'),
  getAnnouncements: () => api.get('/announcements'),
};

export const avatarApi = {
  getAvatarUrl: (userId: string, timestamp?: number): string => {
    const baseUrl = `/api/users/${userId}/avatar/image`;
    return timestamp ? `${baseUrl}?t=${timestamp}` : baseUrl;
  },
  uploadAvatar: async (userId: string, file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('avatar', file);
    const response = await fetch(`/api/users/${userId}/avatar`, { method: 'POST', credentials: 'same-origin', body: formData });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Avatar upload failed');
    }
    const result = await response.json();
    invalidateCacheByPattern(new RegExp(`/api/users/${userId}/avatar`));
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('avatarUpdated', { detail: { userId, deleted: false } }));
    }
    return result;
  },
  deleteAvatar: async (userId: string): Promise<any> => {
    const response = await fetch(`/api/users/${userId}/avatar`, { method: 'DELETE', credentials: 'same-origin' });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Avatar deletion failed');
    }
    const result = await response.json();
    invalidateCacheByPattern(new RegExp(`/api/users/${userId}/avatar`));
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('avatarUpdated', { detail: { userId, deleted: true } }));
    }
    return result;
  },
  checkAvatar: async (userId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/users/${userId}/avatar/image`, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  },
};

export const cacheUtils = {
  clear: (pattern?: string) => {
    if (pattern) {
      invalidateCacheByPattern(new RegExp(pattern));
    } else {
      cache.clear();
    }
  },
  getStats: () => ({ size: cache.size, keys: Array.from(cache.keys()) }),
  set: (key: string, data: any, ttl: number = DEFAULT_CACHE_TTL) => setToCache(key, data, ttl),
  get: (key: string) => getFromCache(key),
};

if (typeof window !== 'undefined') {
  window.addEventListener('avatar-updated', (event: any) => {
    const { userId } = event.detail || {};
    if (userId) {
      cacheUtils.clear(`/api/users/${userId}/avatar`);
    }
  });
  window.addEventListener('user-updated', (event: any) => {
    const { userId } = event.detail || {};
    if (userId) {
      cacheUtils.clear(`/api/users/${userId}`);
    }
  });
}

export const dispatchCacheInvalidation = (eventType: string, data: any) => {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent(eventType, { detail: data }));
  }
};

export function setupInterceptors(api: any) {
  // Stub for future request/response interceptors
}

export default api;
