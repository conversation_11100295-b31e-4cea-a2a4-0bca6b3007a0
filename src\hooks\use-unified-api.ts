/**
 * Unified API Hooks System
 * 
 * This consolidates all API hooks into a single, optimized system that:
 * - Eliminates redundancy between multiple hook files
 * - Provides consistent caching strategies
 * - Implements smart invalidation
 * - Includes error boundaries and retry logic
 * - Supports optimistic updates
 */

"use client";

import { 
  useQuery, 
  useMutation, 
  useQueryClient, 
  useInfiniteQuery,
  UseQueryOptions, 
  UseMutationOptions,
  UseInfiniteQueryOptions
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { apiService } from '@/lib/services/api';
import { useUser } from './use-user';
import { logHookError, logError, classifyError } from '@/lib/error-handler';
import { 
  ShiftWithDetails, 
  Job, 
  Company, 
  Timesheet, 
  TimesheetDetails, 
  User, 
  UserWithAssignments,
  Notification,
  Announcement
} from '@/lib/types';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface CacheConfig {
  staleTime: number;
  gcTime: number;
  refetchOnMount: boolean;
  refetchOnWindowFocus: boolean;
  retry: number | ((failureCount: number, error: Error) => boolean);
  retryDelay?: (attemptIndex: number) => number;
}

interface EntityFilters {
  shifts?: {
    date?: string;
    status?: string;
    companyId?: string;
    search?: string;
    jobId?: string;
    userId?: string;
    page?: number;
    limit?: number;
  };
  jobs?: {
    status?: string;
    companyId?: string;
    search?: string;
    sortBy?: string;
    userId?: string;
  };
  users?: {
    page?: number;
    pageSize?: number;
    fetchAll?: boolean;
    role?: string;
    search?: string;
    status?: 'active' | 'inactive';
    excludeCompanyUsers?: boolean;
  };
  companies?: {
    page?: number;
    pageSize?: number;
    search?: string;
  };
  timesheets?: {
    shiftId?: string;
    userId?: string;
    status?: string;
  };
}

interface MutationConfig<TData, TVariables> extends Omit<UseMutationOptions<TData, Error, TVariables>, 'mutationFn'> {
  invalidateQueries?: string[][];
  optimisticUpdate?: {
    queryKey: string[];
    updateFn: (oldData: any, variables: TVariables) => any;
  };
  entityType?: string;
  mutationType?: 'create' | 'update' | 'delete';
  entityId?: string;
}

// ============================================================================
// CACHE CONFIGURATIONS
// ============================================================================

const CACHE_CONFIGS: Record<string, CacheConfig> = {
  // Real-time data (short cache)
  shifts: {
    staleTime: 2 * 60 * 1000, // 2 mins
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 1,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },
  
  // Semi-static data (medium cache)
  jobs: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },
  
  // Static data (long cache)
  companies: {
    staleTime: 7 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 1,
  },
  
  users: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 1,
  },
  
  // Dynamic data (very short cache)
  timesheets: {
    staleTime: 10 * 1000, // 10 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 2,
  },
  
  // Individual entities (medium cache)
  entity: {
    staleTime: 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 2,
  },
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const createQueryKey = (base: string, filters?: Record<string, any>): string[] => {
  if (!filters) return [base];
  
  const keys = [base];
  Object.entries(filters).forEach(([key, value]) => {
    if (value != null && value !== '' && value !== 'all') {
      keys.push(`${key}:${value}`);
    }
  });
  
  return keys;
};

const shouldRetry = (failureCount: number, error: Error): boolean => {
  // Don't retry on 4xx errors (client errors)
  if (typeof error.message === 'string' && error.message.includes('4')) {
    return false;
  }
  
  // Retry up to 3 times for 5xx errors
  return failureCount < 3;
};

// ============================================================================
// SMART INVALIDATION SYSTEM
// ============================================================================

const useSmartInvalidation = () => {
  const queryClient = useQueryClient();
  
  const invalidateRelated = useCallback(async (
    mutationType: 'create' | 'update' | 'delete',
    entityType: string,
    entityId?: string
  ) => {
    const invalidationMap: Record<string, string[]> = {
      shifts: ['shifts', 'jobs', 'timesheets', 'users'],
      jobs: ['jobs', 'shifts', 'companies'],
      users: ['users', 'shifts'],
      companies: ['companies', 'jobs'],
      timesheets: ['timesheets', 'shifts'],
    };
    
    const toInvalidate = invalidationMap[entityType] || [entityType];
    
    await Promise.all(
      toInvalidate.map(type => 
        queryClient.invalidateQueries({ 
          predicate: (query) => {
            const key = query.queryKey[0] as string;
            return key === type || (entityId ? query.queryKey.includes(entityId) : false);
          }
        })
      )
    );
  }, [queryClient]);
  
  return { invalidateRelated };
};

// ============================================================================
// QUERY HOOKS
// ============================================================================

export const useUnifiedShifts = (
  filters?: EntityFilters['shifts'],
  options?: Partial<UseQueryOptions<ShiftWithDetails[], Error>>
) => {
  const { user } = useUser();
  const config = CACHE_CONFIGS.shifts;
  
  return useQuery({
    queryKey: createQueryKey('shifts', filters),
    queryFn: async () => {
      try {
        console.log('🔍 [useUnifiedShifts] Fetching shifts with filters:', filters);
        const data = await apiService.getShifts(filters);
        console.log('✅ [useUnifiedShifts] Successfully fetched shifts:', data?.length || 0);
        return data;
      } catch (error) {
        logHookError('useUnifiedShifts', error, [filters, user?.id]);
        const appError = classifyError(error as Error, { hook: 'useUnifiedShifts', filters, userId: user?.id });
        logError(appError);
        throw error;
      }
    },
    enabled: !!user,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedShift = (
  id: string,
  options?: Partial<UseQueryOptions<ShiftWithDetails, Error>>
) => {
  const config = CACHE_CONFIGS.entity;
  
  return useQuery({
    queryKey: ['shift', id],
    queryFn: async () => {
      try {
        console.log('🔍 [useUnifiedShift] Fetching shift:', id);
        const data = await apiService.getShift(id);
        // @ts-ignore
        const shift = data?.shift || data;
        console.log('✅ [useUnifiedShift] Successfully fetched shift:', shift?.id);
        return shift;
      } catch (error) {
        logHookError('useUnifiedShift', error, [id]);
        const appError = classifyError(error as Error, { hook: 'useUnifiedShift', shiftId: id });
        logError(appError);
        throw error;
      }
    },
    enabled: !!id,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedJobs = (
  filters?: EntityFilters['jobs'],
  options?: Partial<UseQueryOptions<{ jobs: Job[]; pagination: { total: number; pages: number; currentPage: number } }, Error>>
) => {
  const { user } = useUser();
  const config = CACHE_CONFIGS.jobs;

  return useQuery({
    queryKey: createQueryKey('jobs', filters),
    queryFn: async () => {
      try {
        console.log('🔍 [useUnifiedJobs] Fetching jobs with filters:', filters);
        const { jobs, pagination } = await apiService.getJobs(filters);
        console.log('✅ [useUnifiedJobs] Successfully fetched jobs:', jobs?.length || 0);
        return {
          jobs,
          pagination, // Keep pagination as nested object for component compatibility
        };
      } catch (error) {
        logHookError('useUnifiedJobs', error, [filters, user?.id]);
        const appError = classifyError(error as Error, { hook: 'useUnifiedJobs', filters, userId: user?.id });
        logError(appError);
        throw error;
      }
    },
    enabled: !!user,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedJob = (
  id: string,
  options?: Partial<UseQueryOptions<Job, Error>>
) => {
  const config = CACHE_CONFIGS.entity;
  
  return useQuery({
    queryKey: ['job', id],
    queryFn: () => apiService.getJob(id),
    enabled: !!id,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedUsers = (
  filters?: EntityFilters['users'],
  options?: Partial<UseQueryOptions<{ users: User[]; total: number; pages: number; currentPage: number }, Error>>
) => {
  const config = CACHE_CONFIGS.users;
  
  return useQuery({
    queryKey: createQueryKey('users', filters),
    queryFn: async () => {
      const { users, pagination } = await apiService.getUsers(filters);
      return {
        users,
        total: pagination.total,
        pages: pagination.pages,
        currentPage: pagination.currentPage,
      };
    },
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedUser = (
  id: string,
  options?: Partial<UseQueryOptions<UserWithAssignments, Error>>
) => {
  const config = CACHE_CONFIGS.entity;
  
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => apiService.getUserById(id),
    enabled: !!id,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedCompanies = (
  filters?: EntityFilters['companies'],
  options?: Partial<UseInfiniteQueryOptions<{ companies: Company[]; pagination: any }, Error>>
) => {
  const config = CACHE_CONFIGS.companies;

  return useInfiniteQuery({
    queryKey: createQueryKey('companies', filters),
    queryFn: ({ pageParam = 1 }) =>
      apiService.getCompanies({ ...filters, page: pageParam as number }),
    getNextPageParam: (lastPage) => {
      if (lastPage.pagination.page < lastPage.pagination.totalPages) {
        return lastPage.pagination.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    // Flatten infinite data so components can access companiesData.companies directly
    select: (data: any) => {
      const pages = Array.isArray(data?.pages) ? data.pages : [];
      const companies = pages.flatMap((p: any) => p?.companies ?? []);
      const last = pages && Array.isArray(pages) && pages.length > 0 ? pages[pages.length - 1] : {};
      const pagination = last.pagination ?? {
        page: 1,
        totalPages: 1,
        total: companies.length,
      };
      return { companies, pagination };
    },
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedCompany = (
  id: string,
  options?: Partial<UseQueryOptions<Company, Error>>
) => {
  const config = CACHE_CONFIGS.entity;
  
  return useQuery({
    queryKey: ['company', id],
    queryFn: () => apiService.getCompany(id),
    enabled: !!id,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedTimesheets = (
  filters?: EntityFilters['timesheets'],
  options?: Partial<UseQueryOptions<TimesheetDetails[], Error>>
) => {
  const config = CACHE_CONFIGS.timesheets;
  
  return useQuery({
    queryKey: createQueryKey('timesheets', filters),
    queryFn: () => apiService.getTimesheets(filters),
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

export const useUnifiedTimesheet = (
  id: string,
  options?: Partial<UseQueryOptions<TimesheetDetails, Error>>
) => {
  const config = CACHE_CONFIGS.entity;
  
  return useQuery({
    queryKey: ['timesheet', id],
    queryFn: () => apiService.getTimesheet(id),
    enabled: !!id,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

// ============================================================================
// INFINITE QUERY HOOKS
// ============================================================================

export const useInfiniteShifts = (
  filters?: EntityFilters['shifts'],
  options?: Partial<UseInfiniteQueryOptions<{ shifts: ShiftWithDetails[]; hasMore: boolean; currentPage: number; }, Error>>
) => {
  const { user } = useUser();
  const config = CACHE_CONFIGS.shifts;
  
  return useInfiniteQuery({
    queryKey: createQueryKey('shifts-infinite', filters),
    queryFn: async ({ pageParam = 1 }) => {
      const page = pageParam as number;
      const limit = filters?.limit || 10;
      const params = { ...filters, page, limit };
      const shifts = await apiService.getShifts(params);
      return {
        shifts,
        hasMore: shifts.length === limit,
        currentPage: page,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage.hasMore ? lastPage.currentPage + 1 : undefined,
    enabled: !!user,
    ...config,
    retry: shouldRetry,
    ...options,
  });
};

// ============================================================================
// MUTATION HOOKS
// ============================================================================

export const useUnifiedMutation = <TData = unknown, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  config?: MutationConfig<TData, TVariables>
) => {
  const queryClient = useQueryClient();
  const { invalidateRelated } = useSmartInvalidation();
  const { 
    invalidateQueries, 
    optimisticUpdate, 
    entityType, 
    mutationType, 
    entityId,
    onSuccess,
    onError,
    onMutate,
    ...restOptions 
  } = config || {};
  
  return useMutation({
    mutationFn,
    onMutate: async (variables): Promise<{ previousData: unknown } | undefined> => {
      // Optimistic update
      if (optimisticUpdate) {
        await queryClient.cancelQueries({ queryKey: optimisticUpdate.queryKey });
        const previousData = queryClient.getQueryData(optimisticUpdate.queryKey);
        queryClient.setQueryData(
          optimisticUpdate.queryKey,
          (old: any) => optimisticUpdate.updateFn(old, variables)
        );
        return { previousData };
      }
      return undefined;
    },
    onSuccess: async (data, variables, context) => {
      // Smart invalidation
      if (entityType && mutationType) {
        await invalidateRelated(mutationType, entityType, entityId);
      }
      
      // Manual invalidation
      if (invalidateQueries) {
        await Promise.all(
          invalidateQueries.map(queryKey => 
            queryClient.invalidateQueries({ queryKey })
          )
        );
      }
      
      if (onSuccess) {
        onSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      // Revert optimistic update
      if (optimisticUpdate && (context as any)?.previousData) {
        queryClient.setQueryData(optimisticUpdate.queryKey, (context as any).previousData);
      }
      
      if (onError) {
        onError(error, variables, context);
      }
    },
    retry: 1,
    ...restOptions,
  });
};

// ============================================================================
// SPECIFIC MUTATION HOOKS
// ============================================================================

export const useAssignWorker = () => {
  return useUnifiedMutation(
    ({ shiftId, userId, roleCode, ignoreConflicts }: {
      shiftId: string;
      userId: string;
      roleCode: string;
      ignoreConflicts?: boolean;
    }) => apiService.assignWorker(shiftId, userId, roleCode, ignoreConflicts),
    {
      entityType: 'shifts',
      mutationType: 'update',
      optimisticUpdate: {
        queryKey: ['shift'],
        updateFn: (oldData: any, variables) => {
          // Optimistically add the assignment
          if (oldData && oldData.assignedPersonnel) {
            return {
              ...oldData,
              assignedPersonnel: [
                ...oldData.assignedPersonnel,
                {
                  id: `temp-${Date.now()}`,
                  userId: variables.userId,
                  roleCode: variables.roleCode,
                  status: 'Assigned',
                  user: { id: variables.userId, name: 'Loading...' },
                  timeEntries: [],
                }
              ]
            };
          }
          return oldData;
        }
      },
      invalidateQueries: [['shifts']]
    }
  );
};

export const useUnassignWorker = () => {
  return useUnifiedMutation(
    ({ shiftId, assignmentId }: {
      shiftId: string;
      assignmentId: string;
    }) => apiService.unassignWorker(shiftId, assignmentId),
    {
      entityType: 'shifts',
      mutationType: 'update',
      optimisticUpdate: {
        queryKey: ['shift'],
        updateFn: (oldData: any, variables) => {
          if (oldData && oldData.assignedPersonnel) {
            return {
              ...oldData,
              assignedPersonnel: oldData.assignedPersonnel.filter(
                (assignment: any) => assignment.id !== variables.assignmentId
              )
            };
          }
          return oldData;
        }
      }
    }
  );
};

export const useClockIn = () => {
  return useUnifiedMutation(
    ({ shiftId, assignmentId }: {
      shiftId: string;
      assignmentId: string;
    }) => apiService.clockIn(shiftId, assignmentId),
    {
      entityType: 'shifts',
      mutationType: 'update',
    }
  );
};

export const useClockOut = () => {
  return useUnifiedMutation(
    ({ shiftId, assignmentId }: {
      shiftId: string;
      assignmentId: string;
    }) => apiService.clockOut(shiftId, assignmentId),
    {
      entityType: 'shifts',
      mutationType: 'update',
    }
  );
};

// ============================================================================
// BACKWARD COMPATIBILITY LAYER
// ============================================================================

// Legacy hook names for backward compatibility
export const useShifts = useUnifiedShifts;
export const useShift = useUnifiedShift;
export const useJobs = useUnifiedJobs;
export const useJob = useUnifiedJob;
export const useUsers = useUnifiedUsers;
export const useCompanies = useUnifiedCompanies;
export const useCompany = useUnifiedCompany;
export const useTimesheets = useUnifiedTimesheets;
export const useTimesheet = useUnifiedTimesheet;
export const useApiMutation = useUnifiedMutation;

// ============================================================================
// UTILITY HOOKS
// ============================================================================

export const useQueryInvalidation = () => {
  const queryClient = useQueryClient();
  
  const invalidateAll = useCallback(() => {
    queryClient.invalidateQueries();
  }, [queryClient]);
  
  const invalidateByType = useCallback((entityType: string) => {
    queryClient.invalidateQueries({ 
      predicate: (query) => {
        const key = query.queryKey[0] as string;
        return key === entityType;
      }
    });
  }, [queryClient]);
  
  const invalidateById = useCallback((entityType: string, id: string) => {
    queryClient.invalidateQueries({ queryKey: [entityType, id] });
  }, [queryClient]);
  
  return {
    invalidateAll,
    invalidateByType,
    invalidateById,
  };
};

export const useCacheStats = () => {
  const queryClient = useQueryClient();
  
  return useMemo(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
    };
  }, [queryClient]);
};
