import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import { prisma } from '@/lib/prisma';
import { promises as fs } from 'fs';
import path from 'path';

async function getTimesheetData(timesheetId: string) {
  const timesheet = await prisma.timesheet.findUnique({
    where: { id: timesheetId },
    include: {
      shift: {
        include: {
          job: { include: { company: true } },
          assignedPersonnel: {
            include: {
              user: true,
              timeEntries: { orderBy: { createdAt: 'asc' } },
            },
          },
        },
      },
    },
  });
  if (!timesheet) {
    throw new Error('Timesheet not found');
  }
  return timesheet;
}

export class TimesheetPDFGenerator {
  private timesheetId: string;

  constructor(timesheetId: string) {
    this.timesheetId = timesheetId;
  }

  public async getPDFBuffer(preferSigned: boolean): Promise<Buffer> {
    const timesheet = await getTimesheetData(this.timesheetId);
    if (preferSigned && timesheet.signed_pdf_url) {
      const filePath = path.join(process.cwd(), 'public', timesheet.signed_pdf_url);
      return fs.readFile(filePath);
    }
    if (!preferSigned && timesheet.unsigned_pdf_url) {
        const filePath = path.join(process.cwd(), 'public', timesheet.unsigned_pdf_url);
        return fs.readFile(filePath);
    }
    return this.generatePDF();
  }

  private async generatePDF(): Promise<Buffer> {
    const timesheet = await getTimesheetData(this.timesheetId);
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const { width, height } = page.getSize();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    let y = height - 50;

    page.drawText(`Timesheet for ${timesheet.shift.job.name}`, { x: 50, y, font: boldFont, size: 24 });
    y -= 30;
    page.drawText(`Company: ${timesheet.shift.job.company.name}`, { x: 50, y, font, size: 12 });
    y -= 20;
    page.drawText(`Date: ${new Date(timesheet.shift.date).toLocaleDateString()}`, { x: 50, y, font, size: 12 });
    y -= 40;

    page.drawText('Personnel:', { x: 50, y, font: boldFont, size: 16 });
    y -= 20;

    for (const personnel of timesheet.shift.assignedPersonnel) {
      if(!personnel.user) continue;
      page.drawText(`${personnel.user.name} (${personnel.user.email})`, { x: 60, y, font, size: 12 });
      y -= 15;
      for (const entry of personnel.timeEntries) {
        const clockIn = new Date(entry.clockIn).toLocaleTimeString();
        const clockOut = entry.clockOut ? new Date(entry.clockOut).toLocaleTimeString() : 'N/A';
        page.drawText(`  - Clock In: ${clockIn}, Clock Out: ${clockOut}`, { x: 70, y, font, size: 10 });
        y -= 15;
      }
    }

    const pdfBytes = await pdfDoc.save();
    return Buffer.from(pdfBytes);
  }
}

export async function generateUnsignedTimesheetPdf(timesheetId: string): Promise<string> {
    const generator = new TimesheetPDFGenerator(timesheetId);
    const pdfBuffer = await generator['generatePDF']();
    const filename = `unsigned-timesheet-${timesheetId}.pdf`;
    const directory = path.join(process.cwd(), 'public', 'uploads');
    await fs.mkdir(directory, { recursive: true });
    const filePath = path.join(directory, filename);
    await fs.writeFile(filePath, pdfBuffer);
    const url = `/uploads/${filename}`;

    await prisma.timesheet.update({
        where: { id: timesheetId },
        data: { unsigned_pdf_url: url },
    });

    return url;
}
