/**
 * Date utilities - Re-exports from time-utils for backward compatibility
 * This file ensures all existing imports continue to work
 */

export {
  formatDate as formatDateForDisplay,
  formatTime as formatTimeForDisplay,
  formatDateTime as formatDateTimeForDisplay,
  formatDateForInput,
  formatTimeForInput,
  createDateTimeFromInputs,
  apiDateUtils,
  dateUtils,
  safeParseDate,
  toUTC,
  fromUTC,
  DEFAULT_TIMEZONE,
  DATE_FORMATS
} from './time-utils';

// Additional exports for specific use cases
export { apiDateUtils as formatDateForAPI } from './time-utils';