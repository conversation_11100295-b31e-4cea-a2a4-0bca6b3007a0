'use client';

import { useState, useEffect, useCallback } from 'react';
import { PDFStats } from '@/types/pdf-stats';

const API_ENDPOINT = '/api/admin/pdf-management';

export const usePDFManagement = () => {
  const [stats, setStats] = useState<PDFStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch(API_ENDPOINT, {
        credentials: 'same-origin'
      });
      if (response.ok) {
        const data = await response.json();
        setStats(data);
        setLastRefresh(new Date());
      } else {
        throw new Error('Failed to fetch PDF stats');
      }
    } catch (error) {
      console.error(error);
      // Optionally, set an error state here
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
    const interval = setInterval(fetchStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [fetchStats]);

  const performAction = async (action: string, params: any = {}) => {
    setActionLoading(action);
    try {
      const response = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, ...params })
      });
      
      if (response.ok) {
        await fetchStats(); // Refresh stats after action
      } else {
        console.error(`Failed to perform action ${action}`);
      }
    } catch (error) {
      console.error(`Error performing action ${action}:`, error);
    } finally {
      setActionLoading(null);
    }
  };

  const exportMetrics = async (format: 'json' | 'csv') => {
    try {
      const response = await fetch(`${API_ENDPOINT}?action=export-metrics&format=${format}`, {
        credentials: 'same-origin'
      });
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `pdf-metrics.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Failed to export metrics');
      }
    } catch (error) {
      console.error('Error exporting metrics:', error);
    }
  };

  return {
    stats,
    loading,
    actionLoading,
    lastRefresh,
    fetchStats,
    performAction,
    exportMetrics,
  };
};
