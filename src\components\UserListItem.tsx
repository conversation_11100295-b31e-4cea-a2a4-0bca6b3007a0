import React from 'react';
import { User } from '../lib/types';
import { Avatar } from './Avatar';
import { MapPin } from 'lucide-react';
import { getUserStatusBadges } from './worker-selector-utils';

interface UserListItemProps {
  user: User;
  onSelect: (user: User) => void;
  isSelected: boolean;
  isHighlighted: boolean;
}

export const UserListItem = React.memo<UserListItemProps>(({ user, onSelect, isSelected, isHighlighted }) => {
  return (
    <li
      onClick={() => onSelect(user)}
      className={`relative cursor-pointer select-none py-2 px-3 hover:bg-gray-700 ${
        isSelected ? 'bg-gray-700' : ''
      } ${isHighlighted ? 'bg-indigo-600' : ''} ${!user.isActive ? 'opacity-60' : ''}`}
    >
      <div className="flex items-start gap-2">
        <Avatar 
          src={user.avatarUrl} 
          name={user.name} 
          userId={user.id}
          size="sm"
          enableSmartCaching={true}
          className="w-6 h-6 flex-shrink-0 mt-0.5" 
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-1 mb-1">
            <span className={`truncate ${user.isActive ? 'text-white' : 'text-gray-400'}`}>
              {user.name}
            </span>
          </div>
          
          {user.email && (
            <p className="text-xs text-gray-400 truncate mb-1">{user.email}</p>
          )}
          
          {user.location && (
            <p className="text-xs text-gray-400 flex items-center gap-1 mb-1">
              <MapPin className="w-3 h-3" />
              {user.location}
            </p>
          )}

          {user.certifications && user.certifications.length > 0 && (
            <p className="text-xs text-purple-400 mb-1">
              Certs: {user.certifications.slice(0, 2).join(', ')}
              {user.certifications.length > 2 && ` +${user.certifications.length - 2} more`}
            </p>
          )}
          
          <div className="flex flex-wrap gap-1">
            {getUserStatusBadges(user)}
          </div>
        </div>
      </div>
    </li>
  );
});
