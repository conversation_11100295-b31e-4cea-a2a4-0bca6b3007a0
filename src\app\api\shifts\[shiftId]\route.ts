import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { ShiftWithDetails } from '@/lib/types';
import { Prisma } from '@prisma/client';

export const dynamic = 'force-dynamic';

const shiftWithDetailsInclude = {
  job: {
    include: {
      company: true,
    },
  },
  assignedPersonnel: {
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          companyId: true,
          crew_chief_eligible: true,
          fork_operator_eligible: true,
          OSHA_10_Certifications: true,
          certifications: true,
          performance: true,
          location: true,
          avatarUrl: true, // Include avatarUrl for avatar transformation
        },
      },
      timeEntries: true,
    },
  },
  timesheets: true,
};

function transformShiftToShiftWithDetails(shift: any): ShiftWithDetails {
  // Transform avatarUrl to avatarUrl for all assigned personnel users
  const transformedShift = {
    ...shift,
    assignedPersonnel: shift.assignedPersonnel?.map((assignment: any) => ({
      ...assignment,
      user: assignment.user ? {
        ...assignment.user,
        avatarUrl: assignment.user.avatarUrl 
          ? `/api/users/${assignment.user.id}/avatar/image`
          : null,
      } : null,
    })) || [],
  };
  
  return transformedShift as ShiftWithDetails;
}

async function getShiftById(
  user: any, // Replace with actual user type if available
  id: string,
  tx?: Prisma.TransactionClient
): Promise<ShiftWithDetails | null> {
  const db = tx || prisma;
const shift = await db.shift.findUnique({
  where: { id },
  include: shiftWithDetailsInclude,
});

if (!shift) return null;

const canAccess =
  user.role === "Admin" ||

  (user.role === "CompanyUser" &&
    user.companyId &&
    shift.job.companyId === user.companyId) ||
  ((user.role === "StageHand" || user.role === "CrewChief") &&
    shift.assignedPersonnel.some(p => p.userId === user.id));

return canAccess ? transformShiftToShiftWithDetails(shift) : null;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shiftId: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { shiftId } = await params;
    const shift = await getShiftById(user, shiftId);

    if (!shift) {
      return NextResponse.json(
        { error: 'Shift not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      shift,
    });
  } catch (error) {
    console.error('Error getting shift:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ shiftId: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { shiftId } = await params;
    
    // First, verify the shift exists and the user has permission to view it.
    const existingShift = await getShiftById(user, shiftId);
    if (!existingShift) {
      return NextResponse.json({ error: 'Shift not found or you do not have permission to view it.' }, { status: 404 });
    }

    // Now, check if the user has permission to EDIT it.
    const canEdit = (user.role === 'Admin') || (user.role === "StageHand") || ((user.role === "CompanyUser") && (existingShift.job.companyId === user.companyId));

    if (!canEdit) {
        return NextResponse.json({ error: 'You do not have permission to edit this shift.' }, { status: 403 });
    }

    const body = await request.json();
    console.log('Received shift update data:', body);
    
    const { ...shiftData } = body;

    const updatedShift = await prisma.$transaction(async (tx) => {
      // We already checked for existence, but let's get it again inside the transaction for safety
      const shiftInTx = await tx.shift.findUnique({ where: { id: shiftId } });
      if (!shiftInTx) {
        throw new Error("Shift not found");
      }
      
      // If the user is a CompanyUser, they cannot decrease worker counts.
      if (user.role === 'CompanyUser') {
        const requiredFields: (keyof typeof shiftInTx)[] = [
          'requiredStagehands',
          'requiredForkOperators',
          'requiredReachForkOperators',
          'requiredRiggers',

        ];

        for (const field of requiredFields) {
          if (shiftData[field] !== undefined) {
            const newValue = parseInt(shiftData[field], 10) || 0;
            const oldValue = parseInt(String(shiftInTx[field] || 0), 10) || 0;
            if (newValue < oldValue) {
              throw new Error(`Company users cannot decrease the number of required workers for ${field}.`);
            }
          }
        }
      }

      // Date/time data is now passed as proper ISO timestamps from frontend
      // Just validate and ensure they're proper Date objects if provided
      
      if (shiftData.startTime) {
        // Validate that it's a proper timestamp
        const startDate = new Date(shiftData.startTime);
        if (isNaN(startDate.getTime())) {
          throw new Error('Invalid start time format');
        }
        shiftData.startTime = startDate.toISOString();
      }

      if (shiftData.endTime) {
        // Validate that it's a proper timestamp  
        const endDate = new Date(shiftData.endTime);
        if (isNaN(endDate.getTime())) {
          throw new Error('Invalid end time format');
        }
        shiftData.endTime = endDate.toISOString();
      }
      
      if (shiftData.date) {
        // Validate that it's a proper timestamp
        const date = new Date(shiftData.date);
        if (isNaN(date.getTime())) {
          throw new Error('Invalid date format');
        }
        shiftData.date = date.toISOString();
      }

      // Ensure numeric fields are properly converted
      // Crew chief is always fixed at 1
      if (shiftData.requiredCrewChiefs !== undefined) {
        shiftData.requiredCrewChiefs = 1; // Always force to 1
      }
      if (shiftData.requiredStagehands !== undefined) {
        shiftData.requiredStagehands = parseInt(shiftData.requiredStagehands) || 0;
      }
      if (shiftData.requiredForkOperators !== undefined) {
        shiftData.requiredForkOperators = parseInt(shiftData.requiredForkOperators) || 0;
      }
      if (shiftData.requiredReachForkOperators !== undefined) {
        shiftData.requiredReachForkOperators = parseInt(shiftData.requiredReachForkOperators) || 0;
      }
      if (shiftData.requiredRiggers !== undefined) {
        shiftData.requiredRiggers = parseInt(shiftData.requiredRiggers) || 0;
      }
      if (shiftData.requiredGeneralLaborers !== undefined) {
        shiftData.requiredGeneralLaborers = parseInt(shiftData.requiredGeneralLaborers) || 0;
      }

      console.log('Updating shift with data:', shiftData);

      const shift = await tx.shift.update({
        where: { id: shiftId },
        data: shiftData,
      });
      
      console.log('Updated shift:', shift);
      
      return getShiftById(user, shiftId, tx as any);
    });

    return NextResponse.json({
      success: true,
      shift: updatedShift,
    });
  } catch (error) {
    console.error('Error updating shift:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ shiftId: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { shiftId } = await params;

    // First, verify the shift exists and the user has permission to view it.
    const shift = await getShiftById(user, shiftId);
    if (!shift) {
      return NextResponse.json({ error: 'Shift not found or you do not have permission to view it.' }, { status: 404 });
    }

    // Now, check if the user has permission to DELETE it.
    const canDelete = user.role === 'Admin' ;

    if (!canDelete) {
        return NextResponse.json({ error: 'You do not have permission to delete this shift.' }, { status: 403 });
    }

    await prisma.shift.delete({ where: { id: shiftId } });

    return NextResponse.json({
      success: true,
      message: 'Shift deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting shift:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}
