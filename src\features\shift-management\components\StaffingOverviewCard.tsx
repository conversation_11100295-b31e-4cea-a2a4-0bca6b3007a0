"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, UserPlus, Crown, AlertTriangle } from "lucide-react";
import { Shift, Assignment } from "@/lib/types";
import { Progress } from "@/components/ui/progress";
import { 
  getAssignedWorkerCount, 
  getTotalRequiredWorkers, 
  getWorkerCountData 
} from "@/lib/worker-count-utils";

interface WorkerRequirement {
  roleCode: string;
  requiredCount: number;
}

interface StaffingOverviewCardProps {
  shift: Shift;
  assignments: Assignment[];
  dynamicRequirements?: WorkerRequirement[];
}

export function StaffingOverviewCard({ shift, assignments }: StaffingOverviewCardProps) {
  const assignedWorkers = assignments.filter((p) => p.user && p.userId);
  const workerData = {
    ...shift,
    assignedPersonnel: assignments.map(a => ({ ...a, userId: a.userId || undefined })),
  };

  const { assigned: assignedCount, total: requested, progress } = getWorkerCountData(workerData);
  const isOverfilled = assignedCount > requested;

  const getStaffingStatus = () => {
    if (isOverfilled) {
      return <Badge className="bg-orange-500 hover:bg-orange-600">
        <AlertTriangle className="h-3 w-3 mr-1" />
        Overstaffed
      </Badge>;
    } else if (assignedCount === requested && requested > 0) {
      return <Badge color="green">Fully Staffed</Badge>;
    } else if (assignedCount > 0) {
      return <Badge color="yellow">Partially Staffed</Badge>;
    } else {
      return <Badge color="red">Unstaffed</Badge>;
    }
  };

  const getCrewChief = () => {
    const crewChief = assignedWorkers.find((person: any) => person.roleCode === 'CC');
    return crewChief ? crewChief.user?.name : 'Unassigned';
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center gap-2 pb-2">
        <Users className="h-5 w-5 text-muted-foreground" />
        <CardTitle className="text-lg">Staffing Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Status</span>
            {getStaffingStatus()}
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Staffing Level</span>
              <div className="flex items-center gap-1">
                {isOverfilled && (
                  <AlertTriangle className="h-3 w-3 text-orange-500" />
                )}
                <span className={`font-medium ${isOverfilled ? "text-orange-500" : ""}`}>
                  {`${assignedCount} / ${requested}`}
                </span>
              </div>
            </div>
            <Progress 
              value={Math.min(progress, 100)} 
              className={`h-2 ${isOverfilled ? "[&>div]:bg-orange-500" : ""}`}
            />
            {isOverfilled && (
              <div className="text-xs text-orange-500 font-medium">
                Overstaffed by {assignedCount - requested} worker{assignedCount - requested !== 1 ? 's' : ''}
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <UserCheck className="h-4 w-4 text-green-500" />
              <span className="text-muted-foreground">Assigned:</span>
              <span className="font-medium">{assignedCount}</span>
            </div>
            <div className="flex items-center gap-2">
              <UserPlus className="h-4 w-4 text-blue-500" />
              <span className="text-muted-foreground">Requested:</span>
              <span className="font-medium">{requested}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Crown className="h-4 w-4 text-yellow-500" />
            <span className="text-muted-foreground">Crew Chief:</span>
            <span className="font-medium">{getCrewChief()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
