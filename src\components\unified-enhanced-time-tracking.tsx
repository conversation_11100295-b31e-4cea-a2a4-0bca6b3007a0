"use client";


import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  Clock,
  Play,
  Pause,
  Square,
  Coffee,
  AlertTriangle,
  Users,
  CheckCircle,
  UserPlus,
  Settings,
  Plus,
  Edit,
  User as UserIcon,
  FileText,
  Eye,
  RefreshCw,
  Unlock
} from "lucide-react";
import { useMutation } from '@tanstack/react-query';
import { Avatar } from '@/components/Avatar';
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { format } from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';
import { ROLE_DEFINITIONS, TimesheetStatus } from '@/lib/constants';
import { logComponentError } from '@/lib/error-handler';
import {
  WORKER_STATUS,
  getWorkerStatus,
  formatTime,
  calculateTotalHours,
  WorkerStatus
} from '@/lib/time-tracking-utils';
import EnhancedWorkerSelector from '@/components/EnhancedWorkerSelector';
import WorkerRequirementsManager from '@/components/worker-requirements-manager';
import QuickRequirementsEditor from '@/components/quick-requirements-editor';
import { TimesheetApprovalButton } from '@/components/timesheet-approval-button';
import { UnlockTimesheetDialog } from '@/components/unlock-timesheet-dialog';
import { TimeTrackingHeaderInfo, TimeTrackingHeaderActions, TimeTrackingHeaderCompleted } from '@/components/time-tracking-header';
import { TimeTrackingProvider, useTimeTracking } from '@/components/time-tracking-context';
import { RoleCode, Assignment as WorkerAssignment, User, TimeEntry as TimeEntryType, UserRole } from '@/lib/types';

// --- REFACTORED: Child Components for better modularity ---

interface TimeCellProps {
  entry?: TimeEntryType;
  entryNumber: number;
  field: 'clockIn' | 'clockOut';
  isLockedForEdits: boolean;
  onTimeUpdate: (entryId: string, field: 'clockIn' | 'clockOut', value: string) => void;
  onEditStart?: (entryNumber: number, field: 'clockIn' | 'clockOut') => void;
  // Bulk-edit helpers
  forceEditing?: boolean;
  isBulkActive?: boolean;
  sharedValue?: string;
  onSharedValueChange?: (value: string) => void;
  onRequestBulkUpdate?: (value: string) => void;
  isRowExcluded?: boolean; // e.g., no-shows
}

const TimeCell: React.FC<TimeCellProps> = ({
  entry,
  entryNumber,
  field,
  isLockedForEdits,
  onTimeUpdate,
  onEditStart,
  forceEditing = false,
  isBulkActive = false,
  sharedValue,
  onSharedValueChange,
  onRequestBulkUpdate,
  isRowExcluded = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState('');

  const value = entry?.[field];
  const canEditBase = entry && (field === 'clockOut' ? entry.clockIn : true) && !isLockedForEdits;
  const canEdit = canEditBase && !isRowExcluded;

  // Auto-enter editing when bulk mode is active
  React.useEffect(() => {
    if (forceEditing && canEdit) {
      setEditValue(isBulkActive && sharedValue != null ? sharedValue : formatTime(value, true));
      setIsEditing(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [forceEditing, canEdit]);

  const handleStartEditing = () => {
    if (canEdit) {
      setEditValue(formatTime(value, true));
      setIsEditing(true);
      onEditStart?.(entryNumber, field);
    }
  };

  const commitUpdate = () => {
    if (!entry) return;

    if (isBulkActive && onRequestBulkUpdate) {
      onRequestBulkUpdate(editValue);
      setIsEditing(false);
      return;
    }

    onTimeUpdate(entry.id, field, editValue);
    setIsEditing(false);
  };

  const handleChange = (val: string) => {
    setEditValue(val);
    if (isBulkActive && onSharedValueChange) {
      onSharedValueChange(val);
    }
  };

  if (isEditing) {
    return (
      <TableCell className="text-center">
        <input
          type="time"
          value={editValue}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={commitUpdate}
          onKeyDown={(e) => e.key === 'Enter' && commitUpdate()}
          className="w-24 bg-background text-center"
          autoFocus
        />
      </TableCell>
    );
  }

  return (
    <TableCell
      className={`text-center ${canEdit ? 'cursor-pointer hover:bg-gray-700/30' : ''}`}
      onClick={handleStartEditing}
    >
      <div className={`text-xs ${field === 'clockOut' ? 'text-muted-foreground' : ''} ${value ? 'font-medium' : 'text-gray-400'}`}>
        {formatTime(value?.toString())}
      </div>
    </TableCell>
  );
};

interface WorkerActionButtonsProps {
  assignment: WorkerAssignment;
  disabled: boolean;
  isShiftCompleted: boolean;
  onClockIn: (assignmentId: string) => void;
  onClockOut: (assignmentId: string) => void;
  onEndShift: (assignmentId: string) => void;
  onNoShow: (assignmentId: string) => void;
}

const WorkerActionButtons: React.FC<WorkerActionButtonsProps> = ({
  assignment,
  disabled,
  isShiftCompleted,
  onClockIn,
  onClockOut,
  onEndShift,
  onNoShow,
}) => {
  const status = getWorkerStatus(assignment);
  const canAddMoreEntries = assignment.timeEntries.length < 3;

  if (isShiftCompleted) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <CheckCircle className="h-4 w-4" />
        Shift Completed
      </div>
    );
  }

  if (status === WORKER_STATUS.NOT_ASSIGNED) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <UserPlus className="h-4 w-4" />
        Assign worker first
      </div>
    );
  }
  
  if (status === WORKER_STATUS.SHIFT_ENDED || status === WORKER_STATUS.NO_SHOW) {
    return <span className="text-sm text-gray-500">No actions available</span>;
  }

  return (
    <div className="flex gap-1">
      {status === WORKER_STATUS.NOT_STARTED && (
        <>
          <Button size="sm" onClick={() => onClockIn(assignment.id)} disabled={disabled} className="bg-green-600 hover:bg-green-700">
            <Play className="h-3 w-3 mr-1" />
            Start Shift
          </Button>
          <Button size="sm" variant="destructive" onClick={() => onNoShow(assignment.id)} disabled={disabled}>
            <AlertTriangle className="h-3 w-3 mr-1" />
            No Show
          </Button>
        </>
      )}
      {status === WORKER_STATUS.CLOCKED_IN && (
        <>
          <Button size="sm" variant="outline" onClick={() => onClockOut(assignment.id)} disabled={disabled}>
            <Coffee className="h-3 w-3 mr-1" />
            Start Break
          </Button>
          <Button size="sm" variant="destructive" onClick={() => onEndShift(assignment.id)} disabled={disabled}>
            <Square className="h-3 w-3 mr-1" />
            End Shift
          </Button>
        </>
      )}
      {status === WORKER_STATUS.CLOCKED_OUT && (
        <>
          <Button size="sm" onClick={() => onClockIn(assignment.id)} disabled={disabled || !canAddMoreEntries} className="bg-green-600 hover:bg-green-700">
            <Play className="h-3 w-3 mr-1" />
            End Break
          </Button>
          <Button size="sm" variant="destructive" onClick={() => onEndShift(assignment.id)} disabled={disabled}>
            <Square className="h-3 w-3 mr-1" />
            End Shift
          </Button>
        </>
      )}
    </div>
  );
};

interface WorkerAssignmentRowProps {
  assignment: WorkerAssignment;
  allAssignedUserIds: string[];
}

const WorkerAssignmentRow: React.FC<WorkerAssignmentRowProps> = ({
  assignment,
  allAssignedUserIds,
}) => {
  const {
    isTimeTrackingDisabled,
    isAnyAssignmentUpdating,
    isUpdatingAssignment,
    enableTimeTracking,
    timeEntryColumns,
    availableUsers,
    onAssignWorker,
    onTimeUpdate,
    actionHandlers,
  } = useTimeTracking();
  const isUpdating = isUpdatingAssignment?.[assignment.id] || false;
  const status = getWorkerStatus(assignment);
  const totalHours = calculateTotalHours(assignment.timeEntries);
  const isPlaceholder = assignment.isPlaceholder || !assignment.userId;

  const filteredUsers = useMemo(() => {
    return availableUsers.filter(u => 
      // Include the user if they are currently assigned to this specific slot
      u.id === assignment.userId ||
      // Or, include them if they are not assigned to ANY slot at all
      !allAssignedUserIds.includes(u.id)
    );
  }, [availableUsers, assignment.userId, allAssignedUserIds]);

  // Handle start of single-cell edit: activate bulk UI for this column/field
  const handleEditStart = (entryNumber: number, field: 'clockIn' | 'clockOut') => {
    setBulkState({ showHeaderCheckbox: true, checked: false, entryNumber, field, value: '' });
  };

  // Commit bulk update across all relevant rows for a column
  const handleBulkCommit = (entryNumber: number, field: 'clockIn' | 'clockOut', value: string) => {
    try {
      validAssignments.forEach(assignment => {
        if (assignment.status === WORKER_STATUS.NO_SHOW) return;
        const entry = assignment.timeEntries.find(e => e.entryNumber === entryNumber);
        if (!entry) return;
        onTimeUpdate?.(entry.id, field, value);
      });
      toast({ title: 'Updated column', description: `Applied ${field === 'clockIn' ? 'In' : 'Out'} ${entryNumber} to all non no-show workers.` });
    } finally {
      setBulkState(s => s ? { ...s, showHeaderCheckbox: false, checked: false, value: '' } : null);
    }
  };

  const getStatusBadge = (status: WorkerStatus) => {
    switch (status) {
      case 'up_for_grabs': return <Badge className="bg-yellow-500 text-white">Up For Grabs</Badge>;
      case 'not_assigned': return <Badge variant="outline" className="bg-gray-100">Not Assigned</Badge>;
      case 'not_started': return <Badge variant="outline">Not Started</Badge>;
      case 'clocked_in': return <Badge className="bg-green-500">Clocked In</Badge>;
      case 'on_break': return <Badge className="bg-yellow-500">On Break</Badge>;
      case 'clocked_out': return <Badge className="bg-blue-500">Clocked Out</Badge>;
      case 'shift_ended': return <Badge className="bg-gray-500">Shift Ended</Badge>;
      case 'no_show': return <Badge variant="destructive">No Show</Badge>;
      default: return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <TableRow>
      <TableCell className="w-20 sticky left-0 bg-background z-10 border-r align-top pt-2">
        <div className="flex flex-col items-center text-center">
          {assignment.user ? (
            <>
              <Avatar src={assignment.user.avatarUrl} name={assignment.user.name} userId={assignment.user.id} className="w-16 h-16" size="md" enableSmartCaching={true} />
              <p className="text-[10px] leading-tight mt-1">{assignment.user.name}</p>
            </>
          ) : (
            <>
              <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-400">
                <UserIcon className="h-8 w-8" />
              </div>
              <p className="text-[10px] leading-tight mt-1 text-gray-500">Unassigned</p>
            </>
          )}
        </div>
      </TableCell>
      <TableCell>
        <div className="min-w-[200px]">
          <EnhancedWorkerSelector
            users={filteredUsers}
            selectedUserId={assignment.status === 'UpForGrabs' ? 'up-for-grabs' : (assignment.userId || null)}
            onChange={(userId) => onAssignWorker(assignment.id, userId, assignment.roleCode as RoleCode)}
            disabled={isTimeTrackingDisabled || isAnyAssignmentUpdating}
            isUpdating={isUpdating}
          />
        </div>
      </TableCell>
      <TableCell>
        {(() => {
          try {
            const roleCode = assignment.roleCode as RoleCode;
            
            // Handle placeholder assignments
            if (assignment.isPlaceholder || !assignment.userId) {
              const roleDefinition = ROLE_DEFINITIONS[assignment.roleCode as RoleCode];
              return (
                <Badge variant="outline" className={roleDefinition?.badgeClasses || "bg-gray-100 text-gray-600 border-gray-300"}>
                  {roleDefinition?.name || 'Not Assigned'}
                </Badge>
              );
            }
            
            // Handle missing role code
            if (!roleCode) {
              console.warn(`⚠️ [UnifiedEnhancedTimeTracking] Assignment has no role code:`, {
                assignmentId: assignment.id,
                isPlaceholder: assignment.isPlaceholder,
                userId: assignment.userId,
                componentStack: 'UnifiedEnhancedTimeTracking'
              });
              
              return (
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                  No Role
                </Badge>
              );
            }
            
            const roleDefinition = ROLE_DEFINITIONS[roleCode];
            
            if (!roleDefinition) {
              console.warn(`⚠️ [UnifiedEnhancedTimeTracking] Unknown role code: ${roleCode}`, {
                assignmentId: assignment.id,
                availableRoles: Object.keys(ROLE_DEFINITIONS),
                componentStack: 'UnifiedEnhancedTimeTracking'
              });
              
              logComponentError('UnifiedEnhancedTimeTracking', new Error(`Unknown role code: ${roleCode}`), {
                assignmentId: assignment.id,
                roleCode,
                availableRoles: Object.keys(ROLE_DEFINITIONS)
              });
              
              return (
                <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300">
                  {roleCode} (Unknown)
                </Badge>
              );
            }
            
            return (
              <Badge variant="outline" className={roleDefinition.badgeClasses}>
                {roleDefinition.name}
              </Badge>
            );
          } catch (error) {
            console.error('🔥 [UnifiedEnhancedTimeTracking] Error rendering role badge:', error);
            logComponentError('UnifiedEnhancedTimeTracking', error, { assignmentRoleCode: assignment.roleCode });
            
            return (
              <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                Error
              </Badge>
            );
          }
        })()}
      </TableCell>
      {enableTimeTracking && (
        <>
          {timeEntryColumns.map(col => col.show ? (
            <React.Fragment key={`time-col-${assignment.id}-${col.number}`}>
              {isPlaceholder ? (
                <>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                </>
              ) : (
                <>
                  <TimeCell
                    entry={assignment.timeEntries.find(e => e.entryNumber === col.number)}
                    entryNumber={col.number}
                    field="clockIn"
                    isLockedForEdits={isTimeTrackingDisabled}
                    onTimeUpdate={onTimeUpdate}
                    // Bulk: activate editing immediately when header checkbox is visible (cell was clicked)
                    forceEditing={!!(bulkState && bulkState.showHeaderCheckbox && bulkState.entryNumber === col.number && bulkState.field === 'clockIn' && bulkState.checked)}
                    isBulkActive={!!(bulkState && bulkState.entryNumber === col.number && bulkState.field === 'clockIn' && bulkState.checked)}
                    sharedValue={bulkState?.value}
                    onSharedValueChange={(val) => setBulkState(prev => prev && prev.entryNumber === col.number && prev.field === 'clockIn' ? { ...prev, value: val } : prev)}
                    onRequestBulkUpdate={(val) => handleBulkCommit(col.number, 'clockIn', val)}
                    isRowExcluded={assignment.status === WORKER_STATUS.NO_SHOW}
                    onEditStart={handleEditStart}
                  />
                  <TimeCell
                    entry={assignment.timeEntries.find(e => e.entryNumber === col.number)}
                    entryNumber={col.number}
                    field="clockOut"
                    isLockedForEdits={isTimeTrackingDisabled}
                    onTimeUpdate={onTimeUpdate}
                    // Bulk: activate editing immediately when header checkbox is visible (cell was clicked)
                    forceEditing={!!(bulkState && bulkState.showHeaderCheckbox && bulkState.entryNumber === col.number && bulkState.field === 'clockOut' && bulkState.checked)}
                    isBulkActive={!!(bulkState && bulkState.entryNumber === col.number && bulkState.field === 'clockOut' && bulkState.checked)}
                    sharedValue={bulkState?.value}
                    onSharedValueChange={(val) => setBulkState(prev => prev && prev.entryNumber === col.number && prev.field === 'clockOut' ? { ...prev, value: val } : prev)}
                    onRequestBulkUpdate={(val) => handleBulkCommit(col.number, 'clockOut', val)}
                    isRowExcluded={assignment.status === WORKER_STATUS.NO_SHOW}
                    onEditStart={handleEditStart}
                  />
                </>
              )}
            </React.Fragment>
          ) : null)}
          <TableCell>
            {isPlaceholder ? <Badge variant="outline" className="bg-gray-100">Not Assigned</Badge> : getStatusBadge(status)}
          </TableCell>
          <TableCell className="text-right font-medium">
            {isPlaceholder ? <span className="text-gray-400">-</span> : (totalHours > 0 ? `${totalHours.toFixed(2)} hrs` : '-')}
          </TableCell>
          <TableCell>
            <WorkerActionButtons
              assignment={assignment}
              disabled={isTimeTrackingDisabled}
              isShiftCompleted={isTimeTrackingDisabled}
              onClockIn={actionHandlers.handleClockInClick}
              onClockOut={actionHandlers.handleClockOutClick}
              onEndShift={actionHandlers.handleEndShiftClick}
              onNoShow={actionHandlers.handleNoShowClick}
            />
          </TableCell>
        </>
      )}
    </TableRow>
  );
};


// --- Main Component Props ---
interface UnifiedEnhancedTimeTrackingProps {
  shiftId: string;
  assignments: WorkerAssignment[];
  availableUsers: User[];
  onAssignmentUpdate: (assignmentId: string, userId: string | null, roleCode?: string) => void;
  onRefresh: () => void;
  requirements?: { roleCode: RoleCode; requiredCount: number }[];
  onRequirementsChange?: (requirements: { roleCode: RoleCode; requiredCount: number }[]) => void;
  disabled?: boolean;
  shiftStatus?: string;
  timesheets?: { id: string; status: string }[];
  enableTimeTracking?: boolean;
  onClockIn?: (assignmentId: string) => void;
  onClockOut?: (assignmentId: string) => void;
  onEndWorkerShift?: (assignmentId: string) => void;
  onMarkNoShow?: (assignmentId: string) => void;
  onMasterStartBreak?: () => void;
  onMasterEndShift?: () => void;
  onFinalizeTimesheet?: () => void;
  onTimeUpdate?: (entryId: string, field: 'clockIn' | 'clockOut', value: string) => void; // New prop for time updates
  isUpdatingAssignment?: Record<string, boolean>;
}

export default function UnifiedEnhancedTimeTracking({
  shiftId,
  assignments,
  availableUsers,
  onAssignmentUpdate,
  onRefresh,
  requirements,
  onRequirementsChange,
  disabled = false,
  shiftStatus,
  timesheets = [],
  enableTimeTracking = true,
  onClockIn,
  onClockOut,
  onEndWorkerShift,
  onMarkNoShow,
  onMasterStartBreak,
  onMasterEndShift,
  onFinalizeTimesheet,
  onTimeUpdate,
  isUpdatingAssignment = {},
}: UnifiedEnhancedTimeTrackingProps) {
  const { toast } = useToast();
  const { user } = useUser();
  const [showQuickEditor, setShowQuickEditor] = useState(false);
  const [localJustFinalized, setLocalJustFinalized] = useState(false);

  // Bulk column editing state
  const [bulkState, setBulkState] = useState<{
    // show header checkbox when a cell was clicked
    showHeaderCheckbox: boolean;
    // whether checkbox is checked (apply to all rows)
    checked: boolean;
    entryNumber: number;
    field: 'clockIn' | 'clockOut';
    value: string;
  } | null>(null);

  // Data validation - ensure we have valid assignments
  const validAssignments = useMemo(() => {
    try {
      if (!assignments || !Array.isArray(assignments)) {
        console.warn('⚠️ [UnifiedEnhancedTimeTracking] Invalid assignments data:', assignments);
        return [];
      }
      
      const filtered = assignments.filter((assignment, index) => {
        if (!assignment || typeof assignment !== 'object') {
          console.warn(`⚠️ [UnifiedEnhancedTimeTracking] Invalid assignment at index ${index}:`, assignment);
          return false;
        }
        
        if (!assignment.id) {
          console.warn(`⚠️ [UnifiedEnhancedTimeTracking] Assignment at index ${index} has no ID:`, assignment);
          return false;
        }
        
        return true;
      });
      
      if (filtered.length !== assignments.length) {
        console.log(`🔧 [UnifiedEnhancedTimeTracking] Filtered ${assignments.length - filtered.length} invalid assignments`);
      }
      
      return filtered;
    } catch (error) {
      console.error('🔥 [UnifiedEnhancedTimeTracking] Error validating assignments:', error);
      logComponentError('UnifiedEnhancedTimeTracking', error, { assignmentsCount: assignments?.length });
      return [];
    }
  }, [assignments]);

  const upForGrabsMutation = useMutation({
    mutationFn: (assignmentId: string) => {
      return fetch(`/api/assignments/${assignmentId}/up-for-grabs`, { method: 'POST' }).then(res => {
        if (!res.ok) throw new Error('Failed to set shift as up for grabs');
        return res.json();
      });
    },
    onSuccess: () => {
      toast({ title: 'Shift is Up For Grabs', description: 'Notifications have been sent to available workers.' });
      onRefresh();
    },
    onError: (error: any) => {
      toast({ title: 'Update Failed', description: error.message || 'Could not set shift as up for grabs.', variant: 'destructive' });
    }
  });

  const regeneratePdfMutation = useMutation({
    mutationFn: (timesheetId: string) => {
      return fetch(`/api/timesheets/${timesheetId}/generate-pdf`, { method: 'POST' }).then(res => {
        if (!res.ok) throw new Error('Could not regenerate timesheet PDF');
        return res;
      });
    },
    onSuccess: () => {
      toast({ title: 'Timesheet Regenerated' });
    },
    onError: () => {
      toast({ title: 'Error', description: 'Could not regenerate timesheet PDF', variant: 'destructive' });
    }
  });

  const isAnyAssignmentUpdating = Object.values(isUpdatingAssignment).some(Boolean);
  const isShiftCompleted = shiftStatus === 'Completed';
  // Allow manual edits after shift completion; only lock after timesheet finalization/pending approvals
  const isTimesheetLocked = timesheets && timesheets[0] && (
    timesheets[0].status === 'PENDING_COMPANY_APPROVAL' ||
    timesheets[0].status === 'PENDING_MANAGER_APPROVAL' ||
    timesheets[0].status === 'COMPLETED'
  );
  const isTimeTrackingDisabled = disabled || false; // no longer disable edits due to shift completion

  const allAssignedUserIds = useMemo(() => 
    [...new Set(validAssignments.filter(a => a.userId && a.status !== 'NoShow').map(a => a.userId as string))]
  , [validAssignments]);

  const handleAssignmentStructureChange = () => onRefresh();

  const handleAssignWorker = (assignmentId: string, userId: string | null, roleCode?: RoleCode) => {
    if (userId === 'up-for-grabs') {
      upForGrabsMutation.mutate(assignmentId);
      return;
    }
    onAssignmentUpdate(assignmentId, userId, roleCode);
  };

  // --- REFACTORED: Generic action handler creator ---
  const createSafeActionHandler = (actionFn: ((...args: any[]) => void) | undefined, errorMsg: string) => {
    return (...args: any[]) => {
      if (actionFn) {
        actionFn(...args);
      } else {
        toast({ title: 'Error', description: errorMsg, variant: 'destructive' });
      }
    };
  };

  const handleClockInClick = createSafeActionHandler(onClockIn, 'Clock in function not available.');
  const handleClockOutClick = createSafeActionHandler(onClockOut, 'Clock out function not available.');
  const handleEndShiftClick = createSafeActionHandler(onEndWorkerShift, 'End worker shift function not available.');
  const handleNoShowClick = createSafeActionHandler(onMarkNoShow, 'Mark no show function not available.');
  const handleMasterStartBreakClick = createSafeActionHandler(onMasterStartBreak, 'Master start break function not available.');
  const handleMasterEndShiftClick = createSafeActionHandler(onMasterEndShift, 'Master end shift function not available.');
  const handleFinalizeTimesheet = (...args: any[]) => {
    // Show lock graphic immediately when finalizing
    setLocalJustFinalized(true);
    if (onFinalizeTimesheet) onFinalizeTimesheet();
    else toast({ title: 'Error', description: 'Finalize timesheet function not available.', variant: 'destructive' });
  };
  const handleTimeUpdate = createSafeActionHandler(onTimeUpdate, 'Time update function not available.');

  const handleRegeneratePdf = async (timesheetId: string) => {
    await regeneratePdfMutation.mutateAsync(timesheetId);
  };

  // --- REFACTORED: Simplified finalization logic ---
  const activeWorkers = useMemo(() => {
    return validAssignments.filter(a => {
      if (!a.userId || a.isPlaceholder) return false;
      const status = getWorkerStatus(a);
      return status !== WORKER_STATUS.SHIFT_ENDED && status !== WORKER_STATUS.NO_SHOW;
    });
  }, [validAssignments]);

  // Finalization is allowed regardless of active workers per new requirement
  const canFinalizeTimesheet = true;

  const handleFinalizeTimesheetClick = () => {
    if (canFinalizeTimesheet) {
      handleFinalizeTimesheet();
    } else {
      const reason = !validAssignments.some(a => a.userId && !a.isPlaceholder)
        ? "No workers have been assigned to this shift."
        : `${activeWorkers.length} assigned workers are still active. You can still finalize, but timesheet will lock.`;
      toast({ title: "Heads up", description: reason });
      handleFinalizeTimesheet();
    }
  };

  const roleStats = useMemo(() => {
    try {
      // Build per-role required from explicit requirements prop (if provided)
      const stats: Record<string, { assigned: number; required: number }> = {};
      (Object.keys(ROLE_DEFINITIONS) as RoleCode[]).forEach(code => {
        stats[code] = { assigned: 0, required: 0 };
      });

      if (Array.isArray(requirements) && requirements.length > 0) {
        requirements.forEach(({ roleCode, requiredCount }) => {
          const code = roleCode as RoleCode;
          if (!stats[code]) stats[code] = { assigned: 0, required: 0 };
          stats[code].required += Number(requiredCount) || 0;
        });
      }

      // Compute assigned per role from assignments
      validAssignments.forEach((assignment, index) => {
        try {
          const code = assignment.roleCode as RoleCode;
          if (!code) return;
          if (!stats[code]) stats[code] = { assigned: 0, required: 0 };
          if (assignment.userId) stats[code].assigned++;
        } catch (error) {
          console.error('🔥 [UnifiedEnhancedTimeTracking] Error processing assignment in roleStats:', error);
          logComponentError('UnifiedEnhancedTimeTracking', error, { 
            assignmentIndex: index,
            assignmentId: assignment.id,
            roleCode: assignment.roleCode 
          });
        }
      });

      return stats;
    } catch (error) {
      console.error('🔥 [UnifiedEnhancedTimeTracking] Error calculating role stats:', error);
      logComponentError('UnifiedEnhancedTimeTracking', error, { assignmentsCount: validAssignments.length });
      const fallbackStats: Record<string, { assigned: number; required: number }> = {};
      (Object.keys(ROLE_DEFINITIONS) as RoleCode[]).forEach(code => {
        fallbackStats[code] = { assigned: 0, required: 0 };
      });
      return fallbackStats;
    }
  }, [validAssignments, requirements]);

  const totalAssigned = Object.values(roleStats).reduce((sum, stat) => sum + stat.assigned, 0);
  const totalRequired = Object.values(roleStats).reduce((sum, stat) => sum + stat.required, 0);

  const timeEntryColumns = useMemo(() => {
    const showSecond = validAssignments.some(a => a.timeEntries.some(e => e.entryNumber === 1 && e.clockOut));
    const showThird = validAssignments.some(a => a.timeEntries.some(e => e.entryNumber === 2 && e.clockOut));
    return [
      { number: 1, show: true },
      { number: 2, show: showSecond },
      { number: 3, show: showThird },
    ];
  }, [validAssignments]);

  const clockedInWorkers = validAssignments.filter(a => getWorkerStatus(a) === 'clocked_in');
  const workersForMasterEndShift = validAssignments.filter(a => {
      const status = getWorkerStatus(a);
      return status === 'clocked_in' || status === 'clocked_out';
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <TimeTrackingHeaderInfo
            enableTimeTracking={enableTimeTracking}
            totalAssigned={totalAssigned}
            totalRequired={totalRequired}
            roleStats={roleStats}
          />
          <div className="flex gap-2">
            {enableTimeTracking && !isShiftCompleted && (
              <TimeTrackingHeaderActions
                shiftId={shiftId}
                roleStats={roleStats}
                isTimeTrackingDisabled={isTimeTrackingDisabled}
                isShiftCompleted={isShiftCompleted}
                canFinalizeTimesheet={canFinalizeTimesheet}
                clockedInWorkersCount={clockedInWorkers.length}
                workersForMasterEndShiftCount={workersForMasterEndShift.length}
                assignedPersonnel={validAssignments}
                onRequirementsChange={onRequirementsChange || (() => {})}
                onAssignmentStructureChange={handleAssignmentStructureChange}
                onMasterStartBreak={handleMasterStartBreakClick}
                onMasterEndShift={handleMasterEndShiftClick}
                onFinalizeTimesheet={handleFinalizeTimesheetClick}
              />
            )}
            {enableTimeTracking && isShiftCompleted && (
              <TimeTrackingHeaderCompleted
                timesheets={timesheets}
                userRole={user?.role as UserRole}
                onRefresh={onRefresh}
                onRegeneratePdf={handleRegeneratePdf}
              />
            )}
          </div>
        </div>
      </CardHeader>

      {showQuickEditor && !isShiftCompleted && (
        <div className="border-b bg-gray-50 p-4">
          <QuickRequirementsEditor
            shiftId={shiftId}
            requirements={Object.entries(roleStats).map(([roleCode, stats]) => ({ roleCode: roleCode as RoleCode, requiredCount: stats.required }))}
            onRequirementsUpdate={(newRequirements) => {
              onRequirementsChange?.(newRequirements);
              handleAssignmentStructureChange();
            }}
            onAssignmentStructureChange={handleAssignmentStructureChange}
            disabled={isTimeTrackingDisabled}
          />
        </div>
      )}

      <CardContent>
        <div className="relative">
          {/* Locked overlay when timesheet is finalized/pending approvals, or just finalized locally */}
          {enableTimeTracking && (isTimesheetLocked || localJustFinalized) && (
            <div className="absolute inset-0 z-20 flex items-center justify-center bg-black/40 backdrop-blur-sm">
              <div className="text-center">
                <div className="mb-3 inline-flex items-center justify-center rounded-full bg-white/90 p-4 shadow">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-800" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>
                </div>
                <div className="text-white text-sm mb-3">Timesheet is locked</div>
                {(user?.role === 'Admin' || user?.role === 'Manager') && timesheets && timesheets[0] && (
                  <UnlockTimesheetDialog
                    timesheetId={timesheets[0].id}
                    onUnlock={() => { setLocalJustFinalized(false); onRefresh(); }}
                    trigger={(
                      <Button size="sm" variant="secondary" className="shadow">
                        <Unlock className="h-4 w-4 mr-2" /> Unlock for edits
                      </Button>
                    )}
                  />
                )}
              </div>
            </div>
          )}

          <div className="time-tracking-scroll-container overflow-x-auto">
            <Table className="time-tracking-table min-w-full w-max">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-20 sticky left-0 bg-background z-10 border-r">Avatar</TableHead>
                  <TableHead>Worker Assignment</TableHead>
                  <TableHead>Role</TableHead>
                  {enableTimeTracking && timeEntryColumns.map(col => col.show && (
                    <React.Fragment key={`header-${col.number}`}>
                      <TableHead className="text-center">
                        <div className="flex items-center justify-center gap-2">
                          <span>In {col.number}</span>
                          {/* Select-all checkbox appears once a cell in this column is clicked */}
                          {bulkState?.showHeaderCheckbox && bulkState.entryNumber === col.number && bulkState.field === 'clockIn' ? (
                            <Checkbox
                              checked={bulkState.checked}
                              onCheckedChange={(checked) => setBulkState(prev => (prev ? { ...prev, checked: Boolean(checked) } : prev))}
                              aria-label="Apply to entire column"
                            />
                          ) : null}
                        </div>
                      </TableHead>
                      <TableHead className="text-center">
                        <div className="flex items-center justify-center gap-2">
                          <span>Out {col.number}</span>
                          {bulkState?.showHeaderCheckbox && bulkState.entryNumber === col.number && bulkState.field === 'clockOut' ? (
                            <Checkbox
                              checked={bulkState.checked}
                              onCheckedChange={(checked) => setBulkState(prev => (prev ? { ...prev, checked: Boolean(checked) } : prev))}
                              aria-label="Apply to entire column"
                            />
                          ) : null}
                        </div>
                      </TableHead>
                    </React.Fragment>
                  ))}
                  {enableTimeTracking && <TableHead>Status</TableHead>}
                  {enableTimeTracking && <TableHead className="text-right">Total Hours</TableHead>}
                  {enableTimeTracking && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TimeTrackingProvider value={{
                isTimeTrackingDisabled: isTimesheetLocked || localJustFinalized, // disable actions but keep manual field edits managed in TimeCell
                isAnyAssignmentUpdating,
                enableTimeTracking,
                timeEntryColumns,
                availableUsers,
                onAssignWorker: handleAssignWorker,
                onTimeUpdate: handleTimeUpdate,
                actionHandlers: { handleClockInClick, handleClockOutClick, handleEndShiftClick, handleNoShowClick },
                isUpdatingAssignment,
              }}>
                <TableBody>
                  {validAssignments.map((assignment) => {
                    return (
                      <WorkerAssignmentRow
                        key={assignment.id}
                        assignment={assignment}
                        allAssignedUserIds={allAssignedUserIds}
                      />
                    );
                  })}
                  {validAssignments.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={enableTimeTracking ? 10 : 3} className="text-center py-8 text-gray-500">
                        No worker requirements set for this shift. Use the Worker Requirements Manager to add positions.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </TimeTrackingProvider>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
