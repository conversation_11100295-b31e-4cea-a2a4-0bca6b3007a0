"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useUser } from "@/hooks/use-user"
import { useUserById } from "@/hooks/use-api"
import { useQueryClient } from "@tanstack/react-query"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { UserRole } from "@prisma/client"
import { AvatarUploader } from "@/components/avatar-uploader"
import { getInitials } from "@/lib/utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  User,
  DollarSign,
  MapPin,
  Phone,
  Award,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Shield,
  FileText,
  Home
} from "lucide-react"
import { DashboardPage } from "@/components/DashboardPage"

type PayrollData = {
  payrollType: 'HOURLY' | 'SALARIED'
  payrollBaseRateCents: number
  ssn: string
  addressLine1: string
  addressLine2: string
  city: string
  state: string
  postalCode: string
}

type EmployeeData = {
  role: UserRole
  phone: string
  location: string
  certifications: string[]
  crew_chief_eligible: boolean
  fork_operator_eligible: boolean
  OSHA_10_Certifications: boolean
  performance: number
  isActive: boolean
}

const US_STATES = [
  'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
  'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
  'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
  'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
  'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
]

const COMMON_CERTIFICATIONS = [
  'OSHA 10-Hour',
  'OSHA 30-Hour',
  'First Aid',
  'CPR',
  'Forklift Operator',
  'Crane Operator',
  'Rigging Certification',
  'Electrical Safety',
  'Confined Space',
  'Fall Protection'
]

export function EnhancedEmployeeEdit() {
  const { user: adminUser } = useUser()
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const userId = params.id as string
  const { data: userData, isLoading, error } = useUserById(userId)

  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [activeTab, setActiveTab] = useState("basic")
  
  const [payroll, setPayroll] = useState<PayrollData>({
    payrollType: 'HOURLY',
    payrollBaseRateCents: 0,
    ssn: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: 'CA', // Default to California
    postalCode: '',
  })

  const [employee, setEmployee] = useState<EmployeeData>({
    role: UserRole.StageHand,
    phone: '',
    location: '',
    certifications: [],
    crew_chief_eligible: false,
    fork_operator_eligible: false,
    OSHA_10_Certifications: false,
    performance: 0,
    isActive: true,
  })

  const [customCertification, setCustomCertification] = useState("")

  useEffect(() => {
    if (userData) {
      setName(userData.name)
      setEmail(userData.email)
      setPayroll({
        payrollType: (userData.payrollType as any) || 'HOURLY',
        payrollBaseRateCents: userData.payrollBaseRateCents || 0,
        ssn: '',
        addressLine1: userData.addressLine1 || '',
        addressLine2: userData.addressLine2 || '',
        city: userData.city || '',
        state: userData.state || 'CA',
        postalCode: userData.postalCode || '',
      })
      setEmployee({
        role: userData.role || UserRole.StageHand,
        phone: userData.phone || '',
        location: userData.location || '',
        certifications: userData.certifications || [],
        crew_chief_eligible: userData.crew_chief_eligible || false,
        fork_operator_eligible: userData.fork_operator_eligible || false,
        OSHA_10_Certifications: userData.OSHA_10_Certifications || false,
        performance: userData.performance || 0,
        isActive: userData.isActive !== false,
      })
    }
  }, [userData])

  if (adminUser?.role !== UserRole.Admin) {
    router.push('/dashboard')
    return null
  }

  const handleAddCertification = () => {
    if (customCertification && !employee.certifications.includes(customCertification)) {
      setEmployee(prev => ({
        ...prev,
        certifications: [...prev.certifications, customCertification]
      }))
      setCustomCertification("")
    }
  }

  const handleRemoveCertification = (cert: string) => {
    setEmployee(prev => ({
      ...prev,
      certifications: prev.certifications.filter(c => c !== cert)
    }))
  }

  const handleToggleCertification = (cert: string, checked: boolean) => {
    if (checked) {
      setEmployee(prev => ({
        ...prev,
        certifications: [...prev.certifications, cert]
      }))
    } else {
      setEmployee(prev => ({
        ...prev,
        certifications: prev.certifications.filter(c => c !== cert)
      }))
    }
  }

  const formatSSN = (value: string) => {
    const digits = value.replace(/\D/g, '')
    if (digits.length <= 3) return digits
    if (digits.length <= 5) return `${digits.slice(0, 3)}-${digits.slice(3)}`
    return `${digits.slice(0, 3)}-${digits.slice(3, 5)}-${digits.slice(5, 9)}`
  }

  const handleSSNChange = (value: string) => {
    const formatted = formatSSN(value)
    setPayroll(prev => ({ ...prev, ssn: formatted }))
  }

  const validateForm = () => {
    const errors: string[] = []
    
    if (!name.trim()) errors.push("Name is required")
    if (!email.trim()) errors.push("Email is required")
    if (payroll.payrollType === 'HOURLY' && payroll.payrollBaseRateCents <= 0) {
      errors.push("Hourly rate must be greater than $0")
    }
    if (!payroll.addressLine1.trim()) errors.push("Address is required for payroll")
    if (!payroll.city.trim()) errors.push("City is required for payroll")
    if (!payroll.state.trim()) errors.push("State is required for payroll")
    if (!payroll.postalCode.trim()) errors.push("Postal code is required for payroll")
    if (payroll.ssn && !/^\d{3}-\d{2}-\d{4}$/.test(payroll.ssn)) {
      errors.push("SSN must be in format XXX-XX-XXXX")
    }

    return errors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm()
    if (errors.length > 0) {
      toast({
        title: "Validation Error",
        description: errors.join(", "),
        variant: "destructive",
      })
      return
    }

    let uploadedAvatarUrl = userData?.avatarUrl

    if (avatarFile) {
      const formData = new FormData()
      formData.append('avatar', avatarFile)

      try {
        const uploadResponse = await fetch(`/api/users/${userId}/upload-avatar`, {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const { avatarUrl } = await uploadResponse.json()
          uploadedAvatarUrl = avatarUrl
        } else {
          throw new Error('Failed to upload avatar')
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to upload avatar. Please try again.",
          variant: "destructive",
        })
        return
      }
    }

    const payload = {
      name,
      email,
      role: employee.role,
      phone: employee.phone,
      location: employee.location,
      certifications: employee.certifications,
      crew_chief_eligible: employee.crew_chief_eligible,
      fork_operator_eligible: employee.fork_operator_eligible,
      OSHA_10_Certifications: employee.OSHA_10_Certifications,
      performance: employee.performance,
      isActive: employee.isActive,
      avatarUrl: uploadedAvatarUrl,
      payrollType: payroll.payrollType,
      payrollBaseRateCents: payroll.payrollBaseRateCents,
      addressLine1: payroll.addressLine1,
      addressLine2: payroll.addressLine2,
      city: payroll.city,
      state: payroll.state,
      postalCode: payroll.postalCode,
      ssn: payroll.ssn || undefined,
    }

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        await queryClient.invalidateQueries({ queryKey: ['users', userId] })
        await queryClient.invalidateQueries({ queryKey: ['users'] })
        toast({
          title: "Employee Updated Successfully",
          description: `${name}'s profile has been updated with California compliance requirements.`,
        })
        router.push(`/admin/stagehands/${userId}`)
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update employee')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update employee. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <DashboardPage title="Loading Employee...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="ml-2">Loading employee details...</p>
        </div>
      </DashboardPage>
    )
  }

  if (error) {
    return (
      <DashboardPage title="Error">
        <div className="flex justify-center items-center h-64">
          <p className="text-destructive">Error loading employee: {error.toString()}</p>
        </div>
      </DashboardPage>
    )
  }

  const complianceScore = {
    payrollSetup: (payroll.payrollBaseRateCents > 0 ? 25 : 0) + 
                  (payroll.addressLine1 ? 25 : 0) + 
                  (payroll.ssn ? 25 : 0) + 
                  (payroll.city && payroll.state ? 25 : 0),
    certifications: employee.OSHA_10_Certifications ? 50 : 0,
    profile: (name ? 25 : 0) + (email ? 25 : 0) + (employee.phone ? 25 : 0) + (employee.location ? 25 : 0)
  }

  const totalCompliance = Math.round((complianceScore.payrollSetup + complianceScore.certifications + complianceScore.profile) / 3)

  return (
    <DashboardPage
      title={`Edit Employee: ${name || 'Loading...'}`}
      description="Update employee information with California labor law compliance"
      buttonText="Cancel"
      buttonAction={() => router.push(`/admin/stagehands/${userId}`)}
    >
      <div className="space-y-6">
        {/* Compliance Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              California Compliance Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold mb-1">{totalCompliance}%</div>
                <p className="text-sm text-muted-foreground">Overall Compliance</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold mb-1">{complianceScore.payrollSetup}%</div>
                <p className="text-sm text-muted-foreground">Payroll Setup</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold mb-1">{complianceScore.certifications}%</div>
                <p className="text-sm text-muted-foreground">Certifications</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold mb-1">{complianceScore.profile}%</div>
                <p className="text-sm text-muted-foreground">Profile Complete</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">
                <User className="h-4 w-4 mr-2" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="payroll">
                <DollarSign className="h-4 w-4 mr-2" />
                Payroll
              </TabsTrigger>
              <TabsTrigger value="certifications">
                <Award className="h-4 w-4 mr-2" />
                Certifications
              </TabsTrigger>
              <TabsTrigger value="employment">
                <Clock className="h-4 w-4 mr-2" />
                Employment
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>
                    Employee personal details and contact information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center gap-4">
                    <AvatarUploader
                      src={userData?.avatarUrl}
                      fallback={getInitials(name || email || 'U')}
                      userId={userId}
                      onUpload={async (file) => setAvatarFile(file)}
                    />
                    <div>
                      <p className="text-sm font-medium">Profile Photo</p>
                      <p className="text-sm text-muted-foreground">
                        Click to upload a new photo for this employee
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="name">Full Name *</Label>
                      <Input 
                        id="name" 
                        value={name} 
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Enter full name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input 
                        id="email" 
                        type="email"
                        value={email} 
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input 
                        id="phone" 
                        value={employee.phone} 
                        onChange={(e) => setEmployee(prev => ({ ...prev, phone: e.target.value }))}
                        placeholder="(*************"
                      />
                    </div>
                    <div>
                      <Label htmlFor="location">Primary Location</Label>
                      <Input 
                        id="location" 
                        value={employee.location} 
                        onChange={(e) => setEmployee(prev => ({ ...prev, location: e.target.value }))}
                        placeholder="City, State"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="payroll">
              <Card>
                <CardHeader>
                  <CardTitle>Payroll Information</CardTitle>
                  <CardDescription>
                    California-compliant payroll setup with address and tax information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label>Payroll Type *</Label>
                      <Select 
                        value={payroll.payrollType} 
                        onValueChange={(value: 'HOURLY' | 'SALARIED') => 
                          setPayroll(prev => ({ ...prev, payrollType: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="HOURLY">Hourly Employee</SelectItem>
                          <SelectItem value="SALARIED">Salaried Employee</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>
                        {payroll.payrollType === 'HOURLY' ? 'Hourly Rate *' : 'Annual Salary *'}
                      </Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input 
                          type="number" 
                          step="0.01" 
                          className="pl-10"
                          value={(payroll.payrollBaseRateCents / 100).toString()} 
                          onChange={(e) => setPayroll(prev => ({ 
                            ...prev, 
                            payrollBaseRateCents: Math.round(parseFloat(e.target.value || '0') * 100) 
                          }))}
                          placeholder={payroll.payrollType === 'HOURLY' ? '25.00' : '52000'}
                        />
                      </div>
                      {payroll.payrollType === 'HOURLY' && (
                        <p className="text-sm text-muted-foreground mt-1">
                          California minimum wage: $16.00/hr (2024)
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label>Social Security Number</Label>
                    <Input 
                      value={payroll.ssn}
                      onChange={(e) => handleSSNChange(e.target.value)}
                      placeholder="XXX-XX-XXXX"
                      maxLength={11}
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Required for tax reporting. Will be encrypted and stored securely.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium flex items-center gap-2">
                      <Home className="h-5 w-5" />
                      Mailing Address *
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <Label>Address Line 1 *</Label>
                        <Input 
                          value={payroll.addressLine1}
                          onChange={(e) => setPayroll(prev => ({ ...prev, addressLine1: e.target.value }))}
                          placeholder="123 Main Street"
                        />
                      </div>
                      <div>
                        <Label>Address Line 2</Label>
                        <Input 
                          value={payroll.addressLine2}
                          onChange={(e) => setPayroll(prev => ({ ...prev, addressLine2: e.target.value }))}
                          placeholder="Apt 4B, Suite 200, etc."
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label>City *</Label>
                          <Input 
                            value={payroll.city}
                            onChange={(e) => setPayroll(prev => ({ ...prev, city: e.target.value }))}
                            placeholder="Los Angeles"
                          />
                        </div>
                        <div>
                          <Label>State *</Label>
                          <Select 
                            value={payroll.state} 
                            onValueChange={(value) => setPayroll(prev => ({ ...prev, state: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {US_STATES.map(state => (
                                <SelectItem key={state} value={state}>{state}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label>Postal Code *</Label>
                          <Input 
                            value={payroll.postalCode}
                            onChange={(e) => setPayroll(prev => ({ ...prev, postalCode: e.target.value }))}
                            placeholder="90210"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="certifications">
              <Card>
                <CardHeader>
                  <CardTitle>Certifications & Safety</CardTitle>
                  <CardDescription>
                    Track safety certifications and specialized skills
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="osha"
                        checked={employee.OSHA_10_Certifications}
                        onCheckedChange={(checked) => 
                          setEmployee(prev => ({ ...prev, OSHA_10_Certifications: !!checked }))
                        }
                      />
                      <Label htmlFor="osha" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        OSHA 10-Hour Certification
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="crew-chief"
                        checked={employee.crew_chief_eligible}
                        onCheckedChange={(checked) => 
                          setEmployee(prev => ({ ...prev, crew_chief_eligible: !!checked }))
                        }
                      />
                      <Label htmlFor="crew-chief">Crew Chief Eligible</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="fork-operator"
                        checked={employee.fork_operator_eligible}
                        onCheckedChange={(checked) => 
                          setEmployee(prev => ({ ...prev, fork_operator_eligible: !!checked }))
                        }
                      />
                      <Label htmlFor="fork-operator">Fork Operator Certified</Label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Additional Certifications</h3>
                    
                    <div className="space-y-2">
                      <Label>Common Certifications</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {COMMON_CERTIFICATIONS.map((cert) => (
                          <div key={cert} className="flex items-center space-x-2">
                            <Checkbox 
                              id={cert}
                              checked={employee.certifications.includes(cert)}
                              onCheckedChange={(checked) => handleToggleCertification(cert, !!checked)}
                            />
                            <Label htmlFor={cert} className="text-sm">{cert}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Add Custom Certification</Label>
                      <div className="flex gap-2">
                        <Input 
                          value={customCertification}
                          onChange={(e) => setCustomCertification(e.target.value)}
                          placeholder="Enter certification name"
                        />
                        <Button type="button" onClick={handleAddCertification}>Add</Button>
                      </div>
                    </div>

                    {employee.certifications.length > 0 && (
                      <div className="space-y-2">
                        <Label>Current Certifications</Label>
                        <div className="flex flex-wrap gap-2">
                          {employee.certifications.map((cert) => (
                            <Badge key={cert} variant="secondary" className="cursor-pointer" 
                                   onClick={() => handleRemoveCertification(cert)}>
                              {cert} ×
                            </Badge>
                          ))}
                        </div>
                        <p className="text-sm text-muted-foreground">Click to remove</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="employment">
              <Card>
                <CardHeader>
                  <CardTitle>Employment Details</CardTitle>
                  <CardDescription>
                    Role, performance, and employment status
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label>Role</Label>
                      <Select 
                        value={employee.role} 
                        onValueChange={(value: UserRole) => 
                          setEmployee(prev => ({ ...prev, role: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={UserRole.StageHand}>StageHand</SelectItem>
                          <SelectItem value={UserRole.Staff}>Staff</SelectItem>
                          <SelectItem value={UserRole.CrewChief}>Crew Chief</SelectItem>
                          <SelectItem value={UserRole.Manager}>Manager</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Performance Score (0-100)</Label>
                      <Input 
                        type="number" 
                        min="0" 
                        max="100"
                        value={employee.performance} 
                        onChange={(e) => setEmployee(prev => ({ 
                          ...prev, 
                          performance: parseInt(e.target.value) || 0 
                        }))}
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="active"
                      checked={employee.isActive}
                      onCheckedChange={(checked) => 
                        setEmployee(prev => ({ ...prev, isActive: !!checked }))
                      }
                    />
                    <Label htmlFor="active">Active Employee</Label>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h3 className="font-medium text-blue-900 mb-2">California Labor Law Compliance</h3>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Overtime calculated at 1.5x after 8 hours/day and 40 hours/week</li>
                      <li>• Double time calculated at 2x after 12 hours/day</li>
                      <li>• Seventh consecutive workday: first 8 hours overtime, over 8 hours double time</li>
                      <li>• Meal breaks: 30 minutes unpaid for 5+ hour shifts</li>
                      <li>• Rest breaks: 10 minutes paid for every 4 hours worked</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-4 pt-6">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => router.push(`/admin/stagehands/${userId}`)}
            >
              Cancel
            </Button>
            <Button type="submit">
              <CheckCircle2 className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </DashboardPage>
  )
}
