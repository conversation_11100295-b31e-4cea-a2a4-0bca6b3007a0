"use client"

import React, { useEffect, useMemo, useState } from 'react'
import { withAuth } from '@/lib/withAuth'
import { UserRole } from '@prisma/client'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Calendar,
  DollarSign,
  Download,
  FileText,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Users,
  TrendingUp,
  Shield,
  Calculator,
  Plus,
  Refresh<PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'lucide-react'
import { DashboardPage } from '@/components/DashboardPage'
import { useToast } from '@/hooks/use-toast'
import { format, subDays, addDays, startOfWeek, endOfWeek } from 'date-fns'

type PayrollPeriod = {
  id: string
  startDate: string
  endDate: string
  status: 'OPEN' | 'CLOSED' | 'PROCESSED'
  entries?: PayrollEntry[]
}

type PayrollEntry = {
  id: string
  userId: string
  regularHours: number
  overtimeHours: number
  doubletimeHours: number
  totalHours: number
  hourlyRateCents: number
  grossPayCents: number
  user: {
    id: string
    name: string
    email: string
    payrollType: string
  }
  details?: any
}

function EnhancedPayrollManagement() {
  const router = useRouter()
  const { toast } = useToast()
  const [periods, setPeriods] = useState<PayrollPeriod[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [computing, setComputing] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("overview")

  // Form state for creating new periods
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // Quick period generators
  const generateWeeklyPeriod = () => {
    const today = new Date()
    const start = startOfWeek(today, { weekStartsOn: 0 }) // Sunday
    const end = endOfWeek(today, { weekStartsOn: 0 })
    setStartDate(format(start, 'yyyy-MM-dd'))
    setEndDate(format(end, 'yyyy-MM-dd'))
  }

  const generateBiweeklyPeriod = () => {
    const today = new Date()
    const start = startOfWeek(today, { weekStartsOn: 0 })
    const end = addDays(start, 13) // Two weeks
    setStartDate(format(start, 'yyyy-MM-dd'))
    setEndDate(format(end, 'yyyy-MM-dd'))
  }

  const load = async () => {
    try {
      setLoading(true)
      const resp = await fetch('/api/payroll')
      if (resp.ok) {
        const data = await resp.json()
        setPeriods(data.periods || [])
      } else {
        throw new Error('Failed to load payroll periods')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load payroll periods",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => { 
    load() 
  }, [])

  const createPeriod = async () => {
    if (!startDate || !endDate) {
      toast({
        title: "Validation Error",
        description: "Please select both start and end dates",
        variant: "destructive"
      })
      return
    }

    try {
      setCreating(true)
      const resp = await fetch('/api/payroll', { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' }, 
        body: JSON.stringify({ 
          startDate: new Date(startDate).toISOString(), 
          endDate: new Date(endDate).toISOString() 
        }) 
      })
      
      if (resp.ok) { 
        setStartDate('')
        setEndDate('')
        await load()
        toast({
          title: "Period Created",
          description: "New payroll period has been created successfully"
        })
      } else {
        const error = await resp.json()
        throw new Error(error.error || 'Failed to create period')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create payroll period",
        variant: "destructive"
      })
    } finally {
      setCreating(false)
    }
  }

  const compute = async (id: string) => {
    try {
      setComputing(id)
      const resp = await fetch(`/api/payroll/${id}/compute`, { method: 'POST' })
      if (resp.ok) {
        await load()
        toast({
          title: "Payroll Computed",
          description: "Payroll calculations completed using California labor laws"
        })
      } else {
        const error = await resp.json()
        throw new Error(error.error || 'Failed to compute payroll')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to compute payroll",
        variant: "destructive"
      })
    } finally {
      setComputing(null)
    }
  }

  const exportPeriod = (id: string, format: 'csv' | 'xlsx') => {
    window.open(`/api/payroll/${id}/export?format=${format}`, '_blank')
  }

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const currentPeriod = periods.find(p => p.status === 'OPEN') || periods[0]
    if (!currentPeriod?.entries) return null

    const totalEmployees = currentPeriod.entries.length
    const totalHours = currentPeriod.entries.reduce((sum, e) => sum + e.totalHours, 0)
    const totalRegularHours = currentPeriod.entries.reduce((sum, e) => sum + e.regularHours, 0)
    const totalOvertimeHours = currentPeriod.entries.reduce((sum, e) => sum + e.overtimeHours, 0)
    const totalDoubletimeHours = currentPeriod.entries.reduce((sum, e) => sum + e.doubletimeHours, 0)
    const totalGrossPay = currentPeriod.entries.reduce((sum, e) => sum + e.grossPayCents, 0)

    return {
      period: currentPeriod,
      totalEmployees,
      totalHours: totalHours.toFixed(1),
      totalRegularHours: totalRegularHours.toFixed(1),
      totalOvertimeHours: totalOvertimeHours.toFixed(1),
      totalDoubletimeHours: totalDoubletimeHours.toFixed(1),
      totalGrossPay: totalGrossPay / 100,
      avgHoursPerEmployee: totalEmployees > 0 ? (totalHours / totalEmployees).toFixed(1) : '0',
      overtimePercentage: totalHours > 0 ? ((totalOvertimeHours + totalDoubletimeHours) / totalHours * 100).toFixed(1) : '0'
    }
  }, [periods])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'OPEN':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Open</Badge>
      case 'CLOSED':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Closed</Badge>
      case 'PROCESSED':
        return <Badge variant="outline" className="text-green-600 border-green-600">Processed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(cents / 100)
  }

  if (loading) {
    return (
      <DashboardPage title="Loading Payroll...">
        <div className="flex justify-center items-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <p className="ml-2">Loading payroll data...</p>
        </div>
      </DashboardPage>
    )
  }

  return (
    <DashboardPage
      title="Payroll Management"
      description="California-compliant payroll processing and management"
      buttonText="Employee Management"
      buttonAction={() => router.push('/admin/stagehands')}
    >
      <div className="space-y-6">
        {/* Summary Cards */}
        {summaryStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summaryStats.totalEmployees}</div>
                <p className="text-xs text-muted-foreground">In current period</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summaryStats.totalHours}</div>
                <p className="text-xs text-muted-foreground">
                  {summaryStats.avgHoursPerEmployee} avg per employee
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overtime Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summaryStats.overtimePercentage}%</div>
                <p className="text-xs text-muted-foreground">
                  OT: {summaryStats.totalOvertimeHours}h, DT: {summaryStats.totalDoubletimeHours}h
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Gross Pay</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(summaryStats.totalGrossPay * 100)}</div>
                <p className="text-xs text-muted-foreground">Current period total</p>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Period Overview</TabsTrigger>
            <TabsTrigger value="create">Create Period</TabsTrigger>
            <TabsTrigger value="compliance">CA Compliance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <Card>
              <CardHeader>
                <CardTitle>Payroll Periods</CardTitle>
                <CardDescription>Manage and process payroll periods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {periods.length === 0 ? (
                    <div className="text-center py-8">
                      <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground mb-4">No payroll periods created yet</p>
                      <Button onClick={() => setActiveTab("create")}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Period
                      </Button>
                    </div>
                  ) : (
                    periods.map(period => (
                      <Card key={period.id} className="border-l-4 border-l-blue-500">
                        <CardContent className="pt-4">
                          <div className="flex items-center justify-between">
                            <div className="space-y-2">
                              <div className="flex items-center gap-3">
                                <div className="font-medium">
                                  {format(new Date(period.startDate), 'MMM dd, yyyy')} - {format(new Date(period.endDate), 'MMM dd, yyyy')}
                                </div>
                                {getStatusBadge(period.status)}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {period.entries ? (
                                  <>
                                    {period.entries.length} employees • {" "}
                                    {period.entries.reduce((sum, e) => sum + e.totalHours, 0).toFixed(1)} total hours • {" "}
                                    {formatCurrency(period.entries.reduce((sum, e) => sum + e.grossPayCents, 0))} gross pay
                                  </>
                                ) : (
                                  'No entries computed yet'
                                )}
                              </div>
                            </div>
                            <div className="flex gap-2">
                              {period.status !== 'PROCESSED' && (
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => compute(period.id)}
                                  disabled={computing === period.id}
                                >
                                  {computing === period.id ? (
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                  ) : (
                                    <Calculator className="h-4 w-4 mr-2" />
                                  )}
                                  {computing === period.id ? 'Computing...' : 'Compute'}
                                </Button>
                              )}
                              {period.entries && period.entries.length > 0 && (
                                <>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => exportPeriod(period.id, 'csv')}
                                  >
                                    <Download className="h-4 w-4 mr-2" />
                                    CSV
                                  </Button>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => exportPeriod(period.id, 'xlsx')}
                                  >
                                    <Download className="h-4 w-4 mr-2" />
                                    Excel
                                  </Button>
                                </>
                              )}
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => router.push(`/admin/payroll/${period.id}`)}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                            </div>
                          </div>
                          
                          {/* Employee breakdown for computed periods */}
                          {period.entries && period.entries.length > 0 && (
                            <div className="mt-4">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Employee</TableHead>
                                    <TableHead>Regular</TableHead>
                                    <TableHead>Overtime</TableHead>
                                    <TableHead>Double Time</TableHead>
                                    <TableHead>Total Hours</TableHead>
                                    <TableHead className="text-right">Gross Pay</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {period.entries.slice(0, 5).map(entry => (
                                    <TableRow key={entry.id}>
                                      <TableCell>
                                        <div>
                                          <p className="font-medium">{entry.user.name}</p>
                                          <p className="text-sm text-muted-foreground">{entry.user.email}</p>
                                        </div>
                                      </TableCell>
                                      <TableCell>{entry.regularHours.toFixed(1)}h</TableCell>
                                      <TableCell>{entry.overtimeHours.toFixed(1)}h</TableCell>
                                      <TableCell>{entry.doubletimeHours.toFixed(1)}h</TableCell>
                                      <TableCell className="font-medium">{entry.totalHours.toFixed(1)}h</TableCell>
                                      <TableCell className="text-right font-medium">
                                        {formatCurrency(entry.grossPayCents)}
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                  {period.entries.length > 5 && (
                                    <TableRow>
                                      <TableCell colSpan={6} className="text-center text-muted-foreground">
                                        ... and {period.entries.length - 5} more employees
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </TableBody>
                              </Table>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="create">
            <Card>
              <CardHeader>
                <CardTitle>Create Payroll Period</CardTitle>
                <CardDescription>
                  Set up a new payroll period for time tracking and processing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label>Start Date</Label>
                    <Input 
                      type="date" 
                      value={startDate} 
                      onChange={(e) => setStartDate(e.target.value)} 
                    />
                  </div>
                  <div>
                    <Label>End Date</Label>
                    <Input 
                      type="date" 
                      value={endDate} 
                      onChange={(e) => setEndDate(e.target.value)} 
                    />
                  </div>
                  <div className="flex items-end">
                    <Button 
                      onClick={createPeriod} 
                      disabled={creating}
                      className="w-full"
                    >
                      {creating ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      {creating ? 'Creating...' : 'Create Period'}
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>Quick Period Setup</Label>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={generateWeeklyPeriod}>
                      <Calendar className="h-4 w-4 mr-2" />
                      This Week
                    </Button>
                    <Button variant="outline" onClick={generateBiweeklyPeriod}>
                      <Calendar className="h-4 w-4 mr-2" />
                      Bi-weekly
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Standard California payroll periods. Most employers use bi-weekly periods.
                  </p>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="font-medium text-blue-900 mb-2">California Requirements</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Pay periods cannot exceed one month</li>
                    <li>• Bi-weekly periods (14 days) are most common</li>
                    <li>• Overtime must be calculated weekly and daily</li>
                    <li>• All hours over 8/day or 40/week are overtime (1.5x)</li>
                    <li>• Hours over 12/day are double time (2x)</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="compliance">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  California Labor Law Compliance
                </CardTitle>
                <CardDescription>
                  Ensure full compliance with California employment laws
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Overtime Calculations</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Daily Overtime (8+ hours)</p>
                          <p className="text-sm text-muted-foreground">1.5x rate for hours 8-12</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Daily Double Time (12+ hours)</p>
                          <p className="text-sm text-muted-foreground">2x rate for hours over 12</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Weekly Overtime (40+ hours)</p>
                          <p className="text-sm text-muted-foreground">1.5x rate for hours over 40/week</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Seventh Day Rule</p>
                          <p className="text-sm text-muted-foreground">
                            First 8 hours on 7th consecutive day are OT, over 8 are DT
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Break Requirements</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Meal Breaks</p>
                          <p className="text-sm text-muted-foreground">
                            30 min unpaid for 5+ hour shifts
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Second Meal Break</p>
                          <p className="text-sm text-muted-foreground">
                            Additional 30 min for 12+ hour shifts
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Rest Periods</p>
                          <p className="text-sm text-muted-foreground">
                            10 min paid for every 4 hours worked
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">System Features</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Calculator className="h-5 w-5 text-blue-600" />
                        <h4 className="font-medium">Automatic Calculations</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        All overtime and double-time calculations are performed automatically 
                        according to California Labor Code 510.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-5 w-5 text-green-600" />
                        <h4 className="font-medium">Compliance Tracking</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Track break compliance, wage thresholds, and ensure all 
                        California requirements are met.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-5 w-5 text-purple-600" />
                        <h4 className="font-medium">Audit Trail</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Complete audit trail for all payroll calculations and 
                        time entries for compliance purposes.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Download className="h-5 w-5 text-orange-600" />
                        <h4 className="font-medium">Export & Reports</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Export payroll data in multiple formats for accounting 
                        systems and state reporting requirements.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-900">Important Notice</h4>
                      <p className="text-sm text-yellow-800 mt-1">
                        This system implements California Labor Code requirements as of 2024. 
                        Always consult with employment law professionals for specific situations 
                        and ensure compliance with local ordinances.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardPage>
  )
}

export default withAuth(EnhancedPayrollManagement, UserRole.Admin)
