#!/usr/bin/env node
/**
 * Startup script for Next.js standalone build
 * - Auto-detects server.js location
 * - Optionally runs migrations before starting the app
 * - Handles graceful shutdown in Cloud Run / containers
 */

import { spawn } from 'child_process';
import { readdir, access } from 'fs/promises';
import path from 'path';

console.log('🚀 Starting HoliTime application with migration check...');

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`📋 Running: ${command} ${args.join(' ')}`);
    const child = spawn(command, args, { stdio: 'inherit', ...options });

    child.on('close', (code) => (code === 0 ? resolve() : reject(new Error(`Command failed with exit code ${code}`))));
    child.on('error', (error) => reject(error));
  });
}

async function findServerJs() {
  const candidates = [
    path.join('.', 'server.js'),
    path.join('.next', 'standalone', 'server.js'),
    path.join('.next', 'standalone', process.env.npm_package_name || '', 'server.js'),
  ];

  for (const candidate of candidates) {
    try {
      await access(candidate);
      return candidate;
    } catch {}
  }

  return null;
}

async function main() {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
    console.log(`🚪 Port: ${process.env.PORT || '3000'}`);
    console.log(`📁 Working directory: ${process.cwd()}`);

    // Optional: list first 10 files in current directory for debugging
    try {
      const files = await readdir('.');
      console.log('📂 Files in current directory:', files.slice(0, 10).join(', '));
    } catch {}

    // Find server.js automatically
    const serverJsPath = await findServerJs();
    if (!serverJsPath) {
      console.error('❌ server.js not found in any known location');
      process.exit(1);
    }
    console.log(`✅ Found server.js at: ${serverJsPath}`);


    // Start the Next.js standalone server
    console.log('🎉 Starting Next.js standalone server...');
    
    // Set environment variables for the server
    process.env.PORT = process.env.PORT || '8080';
    process.env.HOSTNAME = '0.0.0.0';

    // The standalone server.js is now spawned as a child process
    // for better isolation and signal handling.
    const serverProcess = spawn('node', [serverJsPath], {
      stdio: 'inherit',
      env: {
        ...process.env,
        PORT: process.env.PORT || '8080',
        HOSTNAME: '0.0.0.0',
      },
    });

    // Graceful shutdown
    const signals = ['SIGINT', 'SIGTERM'];
    signals.forEach((signal) => {
      process.on(signal, () => {
        console.log(`Received ${signal}, shutting down...`);
        serverProcess.kill(signal);
      });
    });

  } catch (error) {
    console.error('❌ Startup failed:', error.message);
    process.exit(1);
  }
}

main();
