'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { RefreshCw, Zap, Trash2 } from 'lucide-react';
import { PDFStats } from '@/types/pdf-stats';
import { ACTION_NAMES } from '@/lib/pdf-management-helpers';

interface CacheTabProps {
  cache: PDFStats['cache'];
  actionLoading: string | null;
  performAction: (action: string) => void;
}

const MetricRow: React.FC<{ label: string; value: string | number; variant?: "default" | "secondary" | "destructive" | "outline" | null | undefined }> = ({ label, value, variant = "outline" }) => (
  <div className="flex justify-between">
    <span>{label}:</span>
    <Badge variant={variant}>{value}</Badge>
  </div>
);

const ActionButton: React.FC<{ actionName: string; currentAction: string | null; onClick: () => void; variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | null | undefined; children: React.ReactNode; icon: React.ReactNode }> = ({ actionName, currentAction, onClick, variant, children, icon }) => (
  <Button
    onClick={onClick}
    disabled={currentAction === actionName}
    variant={variant}
    className="w-full"
  >
    {currentAction === actionName ? (
      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
    ) : (
      icon
    )}
    {children}
  </Button>
);

export const CacheTab: React.FC<CacheTabProps> = ({ cache, actionLoading, performAction }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>Cache Statistics</CardTitle>
          <CardDescription>PDF cache performance and usage</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <MetricRow label="Total Cached PDFs" value={cache.totalEntries} />
          <MetricRow label="Cache Size" value={`${(cache.totalSize / 1024 / 1024).toFixed(1)} MB`} />
          <MetricRow 
            label="Hit Rate" 
            value={`${cache.hitRate.toFixed(1)}%`}
            variant={cache.hitRate > 50 ? 'secondary' : 'destructive'}
          />
          <Progress value={cache.hitRate} className="mt-2" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Cache Management</CardTitle>
          <CardDescription>Manage PDF cache system</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <ActionButton
            actionName={ACTION_NAMES.CLEANUP_CACHE}
            currentAction={actionLoading}
            onClick={() => performAction(ACTION_NAMES.CLEANUP_CACHE)}
            variant="outline"
            icon={<Zap className="h-4 w-4 mr-2" />}
          >
            Cleanup Expired Entries
          </ActionButton>
          <ActionButton
            actionName={ACTION_NAMES.CLEAR_CACHE}
            currentAction={actionLoading}
            onClick={() => performAction(ACTION_NAMES.CLEAR_CACHE)}
            variant="destructive"
            icon={<Trash2 className="h-4 w-4 mr-2" />}
          >
            Clear All Cache
          </ActionButton>
        </CardContent>
      </Card>
    </div>
  );
};
