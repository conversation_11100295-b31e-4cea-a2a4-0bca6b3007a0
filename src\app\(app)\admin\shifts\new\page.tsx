"use client";

import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import { AuthenticatedUser, UserRole } from '@/lib/types';
import NewShiftClientPage from "./NewShiftClientPage"

async function fetchAuthenticatedUser(): Promise<AuthenticatedUser | null> {
  const res = await fetch("/api/auth/me");
  if (!res.ok) return null;
  const data = await res.json();
  return data.user;
}

export default function NewShiftPage() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAuthenticatedUser().then(user => {
      if (!user || user.role !== UserRole.Admin) {
        redirect('/unauthorized');
        return;
      }
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  return <NewShiftClientPage />;
}
