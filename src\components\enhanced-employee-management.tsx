"use client"

import React, { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { useUser } from "@/hooks/use-user"
import { useUsers } from "@/hooks/use-api"
import { useOptimizedMutation } from "@/hooks/use-optimized-queries"
import { UserRole } from "@prisma/client"
import {
  ArrowLeft,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  UserCheck,
  DollarSign,
  Clock,
  Award,
  MapPin,
  Phone,
  FileText,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  Filter,
  Download
} from "lucide-react"
import { toast } from "sonner"
import { DashboardPage } from "@/components/DashboardPage"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar } from "@/components/Avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { createSmartCacheKey } from "@/lib/query-config"

type EmployeeWithPayroll = {
  id: string
  name: string
  email: string
  role: UserRole
  isActive: boolean
  phone?: string
  location?: string
  payrollType?: 'HOURLY' | 'SALARIED'
  payrollBaseRateCents?: number
  certifications?: string[]
  crew_chief_eligible?: boolean
  fork_operator_eligible?: boolean
  OSHA_10_Certifications?: boolean
  performance?: number
  companyId?: string
  addressLine1?: string
  city?: string
  state?: string
  ssnLast4?: string
  createdAt?: string
  avatarUrl?: string
}

export function EnhancedStagehandManagement({ initialData }: { initialData?: any }) {
  const { user } = useUser()
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || "")
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>(searchParams.get('role') as UserRole | 'all' || 'all')
  const [payrollFilter, setPayrollFilter] = useState<'all' | 'HOURLY' | 'SALARIED'>(searchParams.get('payrollType') as 'all' | 'HOURLY' | 'SALARIED' || 'all')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>(searchParams.get('status') as 'all' | 'active' | 'inactive' || 'all')
  const [page, setPage] = useState(Number(searchParams.get('page')) || 1)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    const params = new URLSearchParams(searchParams)
    if (searchTerm) {
      params.set('search', searchTerm)
    } else {
      params.delete('search')
    }
    router.replace(`${pathname}?${params.toString()}`)
  }, [searchTerm, pathname, router, searchParams])

  const filters = useMemo(() => ({
    page,
    role: roleFilter === 'all' ? undefined : roleFilter,
    payrollType: payrollFilter === 'all' ? undefined : payrollFilter,
    isActive: statusFilter === 'all' ? undefined : statusFilter === 'active',
    search: searchTerm,
  }), [page, roleFilter, payrollFilter, statusFilter, searchTerm])

  const { data, isLoading: loading, error } = useUsers(filters)
  const queryKey = useMemo(() => createSmartCacheKey('users', filters), [filters])

  const deleteEmployeeMutation = useOptimizedMutation(
    (employeeId: string) => fetch(`/api/users/${employeeId}`, { method: 'DELETE' }),
    {
      optimisticUpdater: {
        queryKey: queryKey,
        updateFn: (oldData: any, employeeId) => {
          if (!oldData) return oldData
          const newUsers = oldData.users.filter((u: any) => u.id !== employeeId)
          return { ...oldData, users: newUsers }
        },
      },
      onSuccess: () => {
        toast.success("Stagehand Deleted", {
          description: `Stagehand has been successfully deleted.`
        })
      },
      onError: (error) => {
        toast.error("Delete Failed", {
          description: error.message || "An unexpected error occurred."
        })
      },
    }
  )

  // Redirect if not admin - use useEffect to avoid conditional rendering after hooks
  useEffect(() => {
    if (user && user.role !== UserRole.Admin) {
      router.push('/dashboard')
    }
  }, [user, router])

  // Don't render if not admin
  if (user?.role !== UserRole.Admin) {
    return null
  }

  const employees: EmployeeWithPayroll[] = data?.users || []
  const pagination = data?.pagination

  const filteredStagehands = employees

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    const active = employees.filter(e => e.isActive).length
    const hourly = employees.filter(e => e.payrollType === 'HOURLY').length
    const salaried = employees.filter(e => e.payrollType === 'SALARIED').length
    const avgRate = employees
      .filter(e => e.payrollBaseRateCents && e.payrollBaseRateCents > 0)
      .reduce((sum, e) => sum + (e.payrollBaseRateCents || 0), 0) / 
      Math.max(1, employees.filter(e => e.payrollBaseRateCents && e.payrollBaseRateCents > 0).length)

    return {
      total: employees.length,
      active,
      inactive: employees.length - active,
      hourly,
      salaried,
      avgHourlyRate: avgRate / 100, // Convert cents to dollars
      certifiedOSHA: employees.filter(e => e.OSHA_10_Certifications).length,
      crewChiefEligible: employees.filter(e => e.crew_chief_eligible).length,
    }
  }, [employees])

  const handleDeleteStagehand = (employeeId: string, employeeName: string) => {
    if (confirm(`Are you sure you want to delete ${employeeName}? This action cannot be undone.`)) {
      deleteEmployeeMutation.mutate(employeeId)
    }
  }

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'Admin': return 'destructive'
      case 'CrewChief': return 'default'
      case 'StageHand': return 'secondary'
      case 'CompanyUser': return 'outline'
      default: return 'secondary'
    }
  }

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(cents / 100)
  }

  const exportStagehands = () => {
    // Create CSV content
    const headers = ['Name', 'Email', 'Role', 'Status', 'Payroll Type', 'Hourly Rate', 'Phone', 'Location', 'Certifications']
    const csvContent = [
      headers.join(','),
      ...filteredStagehands.map(emp => [
        emp.name,
        emp.email,
        emp.role,
        emp.isActive ? 'Active' : 'Inactive',
        emp.payrollType || 'Not Set',
        emp.payrollBaseRateCents ? formatCurrency(emp.payrollBaseRateCents) : 'Not Set',
        emp.phone || '',
        emp.location || '',
        emp.certifications?.join(';') || ''
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `stagehands_${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <DashboardPage title="Loading Stagehands...">
        <div className="flex justify-center items-center h-64">
          <Users className="h-12 w-12 animate-spin" />
        </div>
      </DashboardPage>
    )
  }

  if (error) {
    return (
      <DashboardPage title="Error">
        <div className="flex justify-center items-center h-64">
          <p className="text-destructive">Error loading Stagehands: {error.toString()}</p>
        </div>
      </DashboardPage>
    )
  }

  return (
    <DashboardPage
      title="Stagehand Management"
      description="Manage workforce with integrated payroll and compliance tracking"
      buttonText="Add Stagehand"
      buttonAction={() => router.push('/admin/stagehands/new')}
    >
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Stagehands</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {summaryStats.active} active, {summaryStats.inactive} inactive
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Hourly Rate</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summaryStats.avgHourlyRate * 100)}
              </div>
              <p className="text-xs text-muted-foreground">
                {summaryStats.hourly} hourly, {summaryStats.salaried} salaried
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">OSHA Certified</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.certifiedOSHA}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((summaryStats.certifiedOSHA / summaryStats.total) * 100)}% of workforce
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Crew Chief Eligible</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.crewChiefEligible}</div>
              <p className="text-xs text-muted-foreground">
                Leadership potential
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Stagehand Overview</TabsTrigger>
            <TabsTrigger value="payroll">Payroll Details</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <Card>
              <CardHeader>
                <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                  <CardTitle>All Stagehands</CardTitle>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                    <Input
                      placeholder="Search stagehands..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full sm:w-64"
                    />
                    <Select
                      value={roleFilter}
                      onValueChange={(value) => {
                        setRoleFilter(value as UserRole | 'all')
                        setPage(1)
                      }}
                    >
                      <SelectTrigger className="w-full sm:w-[140px]">
                        <SelectValue placeholder="Role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Roles</SelectItem>
                        {Object.values(UserRole).map(role => (
                          <SelectItem key={role} value={role}>{role}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select
                      value={statusFilter}
                      onValueChange={(value) => {
                        setStatusFilter(value as 'all' | 'active' | 'inactive')
                        setPage(1)
                      }}
                    >
                      <SelectTrigger className="w-full sm:w-[140px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button onClick={exportStagehands} variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Stagehand</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Certifications</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStagehands.map((employee) => (
                      <TableRow
                        key={employee.id}
                        onClick={() => router.push(`/admin/stagehands/${employee.id}`)}
                        className="cursor-pointer hover:bg-muted/50"
                      >
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar
                              src={employee.avatarUrl}
                              name={employee.name}
                              userId={employee.id}
                              size="xl"
                              enableSmartCaching={true}
                              className="h-12 w-12"
                            />
                            <div>
                              <p className="font-medium">{employee.name}</p>
                              <p className="text-sm text-muted-foreground">
                                ID: {employee.id.slice(0, 8)}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="text-sm">{employee.email}</p>
                            {employee.phone && (
                              <div className="flex items-center gap-1">
                                <Phone className="h-3 w-3 text-muted-foreground" />
                                <p className="text-xs text-muted-foreground">{employee.phone}</p>
                              </div>
                            )}
                            {employee.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3 text-muted-foreground" />
                                <p className="text-xs text-muted-foreground">{employee.location}</p>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRoleBadgeVariant(employee.role)}>
                            {employee.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={employee.isActive ? "secondary" : "outline"}>
                            {employee.isActive ? (
                              <>
                                <UserCheck className="h-3 w-3 mr-1" />
                                Active
                              </>
                            ) : (
                              'Inactive'
                            )}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {employee.OSHA_10_Certifications && (
                              <Badge variant="outline" className="text-xs">
                                <Award className="h-3 w-3 mr-1" />
                                OSHA-10
                              </Badge>
                            )}
                            {employee.crew_chief_eligible && (
                              <Badge variant="outline" className="text-xs">
                                <Users className="h-3 w-3 mr-1" />
                                Crew Chief
                              </Badge>
                            )}
                            {employee.fork_operator_eligible && (
                              <Badge variant="outline" className="text-xs">
                                Fork Op
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation()
                                router.push(`/admin/stagehands/${employee.id}`)
                              }}>
                                <FileText className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation()
                                router.push(`/admin/stagehands/${employee.id}/edit`)
                              }}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Stagehand
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleDeleteStagehand(employee.id, employee.name)
                                }}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Stagehand
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {pagination && (
                  <div className="flex justify-center items-center gap-4 mt-4">
                    <Button onClick={() => setPage(p => Math.max(p - 1, 1))} disabled={page === 1} variant="outline">
                      Previous
                    </Button>
                    <p className="text-sm text-muted-foreground">
                      Page {pagination.page} of {pagination.totalPages}
                    </p>
                    <Button 
                      onClick={() => setPage(p => Math.min(p + 1, pagination.totalPages))} 
                      disabled={!pagination.hasNextPage} 
                      variant="outline"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payroll">
            <Card>
              <CardHeader>
                <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                  <CardTitle>Payroll Information</CardTitle>
                  <div className="flex space-x-2">
                    <Select
                      value={payrollFilter}
                      onValueChange={(value) => {
                        setPayrollFilter(value as 'all' | 'HOURLY' | 'SALARIED')
                        setPage(1)
                      }}
                    >
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Payroll Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="HOURLY">Hourly</SelectItem>
                        <SelectItem value="SALARIED">Salaried</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button onClick={() => router.push('/admin/payroll')} variant="outline">
                      <Clock className="h-4 w-4 mr-2" />
                      Manage Payroll
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Stagehand</TableHead>
                      <TableHead>Payroll Type</TableHead>
                      <TableHead>Base Rate</TableHead>
                      <TableHead>Address Status</TableHead>
                      <TableHead>SSN Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStagehands
                      .filter(emp => payrollFilter === 'all' || emp.payrollType === payrollFilter)
                      .map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar
                              src={employee.avatarUrl}
                              name={employee.name}
                              userId={employee.id}
                              size="sm"
                              className="h-8 w-8"
                            />
                            <div>
                              <p className="font-medium">{employee.name}</p>
                              <p className="text-sm text-muted-foreground">{employee.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={employee.payrollType === 'HOURLY' ? 'default' : 'secondary'}>
                            {employee.payrollType || 'Not Set'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {employee.payrollBaseRateCents ? (
                            <div>
                              <p className="font-medium">
                                {formatCurrency(employee.payrollBaseRateCents)}
                                {employee.payrollType === 'HOURLY' ? '/hr' : '/yr'}
                              </p>
                            </div>
                          ) : (
                            <Badge variant="outline" className="text-red-600">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Not Set
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {employee.addressLine1 && employee.city && employee.state ? (
                            <Badge variant="outline" className="text-green-600">
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              Complete
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-yellow-600">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Incomplete
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {employee.ssnLast4 ? (
                            <Badge variant="outline" className="text-green-600">
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              ****{employee.ssnLast4}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-red-600">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Missing
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/admin/users/${employee.id}/edit`)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Payroll
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="compliance">
            <Card>
              <CardHeader>
                <CardTitle>California Labor Law Compliance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Payroll Setup Status</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm">Complete Setup:</span>
                            <span className="font-medium">
                              {employees.filter(e => 
                                e.payrollBaseRateCents && 
                                e.addressLine1 && 
                                e.city && 
                                e.state && 
                                e.ssnLast4
                              ).length}/{employees.length}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Missing Address:</span>
                            <span className="font-medium text-yellow-600">
                              {employees.filter(e => 
                                !e.addressLine1 || !e.city || !e.state
                              ).length}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Missing SSN:</span>
                            <span className="font-medium text-red-600">
                              {employees.filter(e => !e.ssnLast4).length}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Certification Status</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm">OSHA-10 Certified:</span>
                            <span className="font-medium text-green-600">
                              {summaryStats.certifiedOSHA}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Crew Chief Eligible:</span>
                            <span className="font-medium">
                              {summaryStats.crewChiefEligible}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Fork Operator:</span>
                            <span className="font-medium">
                              {employees.filter(e => e.fork_operator_eligible).length}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Compliance Actions</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => router.push('/admin/stagehands/reports')}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            Generate Report
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => router.push('/admin/payroll')}
                          >
                            <Clock className="h-4 w-4 mr-2" />
                            Process Payroll
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={exportStagehands}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Export All Data
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Compliance Checklist</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">California Overtime Calculations</p>
                          <p className="text-sm text-muted-foreground">
                            1.5x rate after 8 hours/day and 40 hours/week, 2x rate after 12 hours/day
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Seventh Day Rule</p>
                          <p className="text-sm text-muted-foreground">
                            First 8 hours on 7th consecutive workday are overtime, over 8 are double time
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Meal Break Tracking</p>
                          <p className="text-sm text-muted-foreground">
                            30-minute unpaid break for shifts over 5 hours, additional break for 12+ hours
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Rest Period Compliance</p>
                          <p className="text-sm text-muted-foreground">
                            10-minute paid rest period for every 4 hours worked
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardPage>
  )
}
