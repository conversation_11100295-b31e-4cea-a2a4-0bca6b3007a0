import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('Backfilling WorkerRequirement from existing Shift numeric fields (non-destructive)...')

  // Ensure WorkerType exists for required codes
  const codes = [
    { code: '<PERSON>', name: 'Crew Chief' },
    { code: '<PERSON>', name: '<PERSON> Hand' },
    { code: 'FO', name: 'Fork Operator' },
    { code: 'RF<PERSON>', name: 'Reach Fork Operator' },
    { code: 'R<PERSON>', name: '<PERSON>ig<PERSON>' },
  ]

  for (const c of codes) {
    await prisma.workerType.upsert({
      where: { code: c.code },
      update: { name: c.name, isActive: true },
      create: { code: c.code, name: c.name, isActive: true },
    })
  }

  // Iterate shifts in pages to avoid large transactions
  const pageSize = 200
  let cursor: string | undefined
  let processed = 0

  while (true) {
    const shifts = await prisma.shift.findMany({
      take: pageSize,
      ...(cursor ? { skip: 1, cursor: { id: cursor } } : {}),
      orderBy: { id: 'asc' },
      select: {
        id: true,
        requiredCrewChiefs: true,
        requiredStagehands: true,
        requiredForkOperators: true,
        requiredReachForkOperators: true,
        requiredRiggers: true,
      },
    })

    if (shifts.length === 0) break

    for (const s of shifts) {
      const reqs: Array<{ workerTypeCode: string; requiredCount: number }> = [
        { workerTypeCode: 'CC', requiredCount: s.requiredCrewChiefs ?? 0 },
        { workerTypeCode: 'SH', requiredCount: s.requiredStagehands ?? 0 },
        { workerTypeCode: 'FO', requiredCount: s.requiredForkOperators ?? 0 },
        { workerTypeCode: 'RFO', requiredCount: s.requiredReachForkOperators ?? 0 },
        { workerTypeCode: 'RG', requiredCount: s.requiredRiggers ?? 0 },
      ]

      for (const r of reqs) {
        if (r.requiredCount > 0) {
          await prisma.workerRequirement.upsert({
            where: { shiftId_workerTypeCode: { shiftId: s.id, workerTypeCode: r.workerTypeCode } },
            update: { requiredCount: r.requiredCount },
            create: { shiftId: s.id, workerTypeCode: r.workerTypeCode, requiredCount: r.requiredCount },
          })
        }
      }
      processed += 1
      if (processed % 200 === 0) console.log(`Processed ${processed} shifts...`)
    }

    cursor = shifts[shifts.length - 1].id
  }

  console.log(`Done. Processed ${processed} shifts.`)
}

main().catch((e) => {
  console.error(e)
  process.exit(1)
}).finally(async () => {
  await prisma.$disconnect()
})