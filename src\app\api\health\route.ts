import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Basic health check information
    const healthData: { [key: string]: any } = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      platform: process.env.K_SERVICE ? 'cloud-run' : 'other',
      region: process.env.GOOGLE_CLOUD_REGION || process.env.GCLOUD_REGION || 'unknown',
      deployment: process.env.GITHUB_SHA || process.env.CLOUD_BUILD_ID || 'unknown',
      service: process.env.K_SERVICE || 'unknown',
      revision: process.env.K_REVISION || 'unknown',
      global_check: typeof global !== 'undefined' ? 'defined' : 'undefined',
      database_url_configured: !!process.env.DATABASE_URL,
      nextauth_configured: !!process.env.NEXTAUTH_SECRET,
      google_oauth_configured: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
      gcs_configured: !!process.env.GCS_BUCKET_NAME,
      database_status: 'not_tested',
      database_error: null,
    };

    // Test database connection if available
    if (process.env.DATABASE_URL) {
      try {
        // Import prisma dynamically to avoid build-time issues
        const { prisma } = await import('@/lib/prisma');
        await prisma.$queryRaw`SELECT 1`;
        healthData.database_status = 'connected';
      } catch (dbError) {
        healthData.database_status = 'error';
        healthData.database_error = dbError instanceof Error ? dbError.message : 'Unknown database error';
      }
    }

    return NextResponse.json(healthData);
  } catch (error) {
    return NextResponse.json({ 
      status: 'error', 
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      environment: process.env.NODE_ENV || 'unknown',
      platform: process.env.K_SERVICE ? 'cloud-run' : 'other'
    }, { status: 500 });
  }
}
