import { PrismaClient } from '@prisma/client';
import { PDFDocument } from 'pdf-lib';
import { formatDateForDisplay, formatTimeForDisplay } from './date-utils';
import { Buffer } from 'node:buffer';
import path from 'node:path';
import fs from 'node:fs/promises';
import { getTimesheetDataForPdf, FullTimesheetData } from './pdf-data-utils';
import { prisma } from './prisma';

export interface OverlayPDFOptions {
  includeSignature?: boolean;
  signatureType?: 'company' | 'manager' | 'both';
  uploadToCloud?: boolean;
}

export class OverlayTemplatePDFGenerator {
  private timesheetId: string;
  private tx?: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;

  constructor(
    timesheetId: string,
    tx?: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>
  ) {
    this.timesheetId = timesheetId;
    this.tx = tx;
  }

  /**
   * Load the static PDF template bytes from pdf-templates directory
   */
  private async loadTemplateBytes(): Promise<Uint8Array> {
    const templatePath = path.join(process.cwd(), 'pdf-templates', 'timesheet-template-sheet.pdf');
    const bytes = await fs.readFile(templatePath);
    return new Uint8Array(bytes);
  }


  private formatDate(date: Date | string | null | undefined): string {
    if (!date) return 'N/A';
    return formatDateForDisplay(new Date(date));
  }

  private formatTime(time: Date | string | null | undefined): string {
    if (!time) return '';
    return formatTimeForDisplay(new Date(time));
  }

  // Normalize field names to allow fuzzy matching (e.g., "Start Time" -> "starttime")
  private normalize(name: string): string {
    return name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
  }

  // Try to populate AcroForm fields if present using exact field names first.
  // Returns true if any field was filled.
  private async tryFillForm(pdfDoc: PDFDocument, timesheet: FullTimesheetData): Promise<boolean> {
    try {
      const form = pdfDoc.getForm();
      const fields = form.getFields();
      if (!fields || fields.length === 0) return false;
  
      let anySet = false;
  
      const setField = (name: string, value: string) => {
        try {
          const field = form.getTextField(name);
          field.setText(value ?? '');
          anySet = true;
        } catch (e) {
          // Field might not exist, which is fine.
        }
      };
  
      // --- Header Information ---
      setField('job-number', timesheet.shift?.job?.name || '');
      setField('customer-value', timesheet.shift?.job?.company?.name || '');
      setField('shift-name', timesheet.shift?.description || '');
      setField('location', timesheet.shift?.job?.location || '');
      const dateTime = `${this.formatDate(timesheet.shift?.date)} ${this.formatTime(timesheet.shift?.startTime)}`.trim();
      setField('date-value', dateTime);
  
      // --- Stagehand Data ---
      const employees = timesheet.entries || [];
      const maxRows = 35; // TODO: Make this dynamic based on the PDF template
  
      // Group entries by user
      const entriesByUser = employees.reduce((acc, entry) => {
        if (!acc[entry.userId]) {
          acc[entry.userId] = {
            name: entry.userName,
            roleCode: entry.roleCode,
            timeEntries: [],
          };
        }
        acc[entry.userId].timeEntries.push(entry);
        return acc;
      }, {} as Record<string, { name: string; roleCode: string; timeEntries: any[] }>);
  
      const uniqueUsers = Object.values(entriesByUser);
  
      for (let i = 0; i < maxRows; i++) {
        const row = i + 1;
        const userGroup = uniqueUsers[i];
  
        if (userGroup) {
          setField(`EMPLOYEE NAMERow${row}`, userGroup.name || '');
          setField(`SHRow${row}`, userGroup.roleCode || '');
  
          const timeEntries = userGroup.timeEntries.sort((a, b) => a.entryNumber - b.entryNumber);
          
          for (let j = 0; j < 3; j++) {
            const entry = timeEntries[j];
            const entryNum = j + 1;
            setField(`IN${entryNum}Row${row}`, entry ? this.formatTime(entry.clockIn) : '');
            setField(`OUT${entryNum}Row${row}`, entry ? this.formatTime(entry.clockOut) : '');
          }
        } else {
          // Clear rows that are not in use
          setField(`EMPLOYEE NAMERow${row}`, '');
          setField(`SHRow${row}`, '');
          for (let j = 1; j <= 3; j++) {
            setField(`IN${j}Row${row}`, '');
            setField(`OUT${j}Row${row}`, '');
          }
        }
      }
  
      try {
        form.updateFieldAppearances();
      } catch (e) {
        console.warn("Could not update field appearances, but proceeding.", e);
      }
  
      return anySet;
    } catch (error) {
      // console.error("Error filling PDF form:", error);
      return false;
    }
  }

  /**
   * Generate PDF buffer using form fields when available, falling back to overlay drawing
   */
  async generatePDF(options: OverlayPDFOptions = {}): Promise<Buffer> {
    const timesheet = await getTimesheetDataForPdf(this.timesheetId, this.tx);

    // Load template
    const templateBytes = await this.loadTemplateBytes();
    const pdfDoc = await PDFDocument.load(templateBytes);

    // Try form-filling first
    const filled = await this.tryFillForm(pdfDoc, timesheet);

    // Signature handling
    if (options.includeSignature) {
      const form = pdfDoc.getForm();
      const page = pdfDoc.getPage(0);
      const pageHeight = page.getHeight();

      const addSignature = async (signature: string, fieldName: string, x: number, y: number) => {
        try {
          const base64 = signature.includes('base64,') ? signature.split('base64,')[1] : signature;
          const imgBytes = Buffer.from(base64, 'base64');
          let image;
          try {
            image = await pdfDoc.embedPng(imgBytes);
          } catch {
            image = await pdfDoc.embedJpg(imgBytes);
          }

          let placed = false;
          try {
            const button = form.getButton(fieldName);
            button.setImage(image);
            placed = true;
          } catch {
            // Field not found, draw manually
          }

          if (!placed) {
            page.drawImage(image, { x, y: pageHeight - y, width: 160, height: 35 });
          }
        } catch (err) {
          console.warn(`Failed to add ${fieldName} signature to PDF:`, err);
        }
      };

      if ((options.signatureType === 'company' || options.signatureType === 'both') && timesheet.company_signature) {
        await addSignature(timesheet.company_signature, 'company_sig_box', 200, 505);
      }

      if ((options.signatureType === 'manager' || options.signatureType === 'both') && timesheet.manager_signature) {
        await addSignature(timesheet.manager_signature, 'manager_sig_box', 500, 505);
      }

      try {
        form.updateFieldAppearances();
      } catch {}
    }

    if (!filled) {
      console.warn("Failed to fill PDF form, the resulting PDF may be empty or incomplete.");
    }

    const pdfBytes = await pdfDoc.save();
    return Buffer.from(pdfBytes);
  }
}
