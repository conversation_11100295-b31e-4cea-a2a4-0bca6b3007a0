'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, FileText, ChevronRight, Inbox, LucideIcon } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useTimesheets, useShifts } from '@/hooks/use-api';
import { useMemo } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { formatDateForDisplay } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { useHasMounted } from '@/hooks/use-has-mounted';
import { getTotalRequiredWorkers, getAssignedWorkerCount } from '@/lib/worker-count-utils';

// Define a unified type for action items
type ActionItem = {
  id: string;
  type: 'timesheet' | 'understaffed_shift';
  title: string;
  subtitle: string;
  date: Date;
  priority: 'high' | 'medium';
  actionUrl: string;
  Icon: LucideIcon;
};

const ActionItemRow = ({ item }: { item: ActionItem }) => {
  const router = useRouter();
  const { Icon } = item; // Destructure to use as a component
  const hasMounted = useHasMounted();

  const priorityClasses = {
    high: 'border-l-4 border-red-500',
    medium: 'border-l-4 border-orange-400',
  };

  return (
    <div
      className={cn(
        "flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors",
        priorityClasses[item.priority]
      )}
      onClick={() => router.push(item.actionUrl)}
    >
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <div className="flex-shrink-0">
          <Icon className={cn("h-5 w-5", item.priority === 'high' ? 'text-red-500' : 'text-orange-400')} />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{item.title}</p>
          <p className="text-xs text-muted-foreground truncate">{item.subtitle}</p>
        </div>
      </div>
      <div className="flex items-center space-x-3 flex-shrink-0 ml-4">
        <span className="text-xs text-muted-foreground hidden sm:block">
          {hasMounted ? formatDistanceToNow(item.date, { addSuffix: true }) : '...'}
        </span>
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
      </div>
    </div>
  );
};


export const ActionItemsInbox = () => {
  const router = useRouter();
  const { data: timesheets, isLoading: timesheetsLoading } = useTimesheets();
  const { data: shifts, isLoading: shiftsLoading } = useShifts();

  const actionItems = useMemo((): ActionItem[] => {
    const items: ActionItem[] = [];

    // 1. Pending Timesheets
    const pendingTimesheets = timesheets?.filter(t => 
      t.status === 'PENDING_COMPANY_APPROVAL' || t.status === 'PENDING_MANAGER_APPROVAL'
    ) || [];

    pendingTimesheets.forEach(t => {
      items.push({
        id: `ts-${t.id}`,
        type: 'timesheet',
        title: `Timesheet #${t.id.slice(-6)} needs approval`,
        subtitle: `For shift on ${formatDateForDisplay(t.shift.date)}`,
        date: new Date(t.submittedAt || t.createdAt),
        priority: 'medium',
        actionUrl: `/shifts/${t.shiftId}?tab=timesheet`,
        Icon: FileText,
      });
    });

    // 2. Understaffed Shifts
    const understaffedShifts = shifts?.filter(s => {
        const workerData = {
            ...s,
            assignedPersonnel: s.assignedPersonnel?.map((p: any) => ({ ...p, userId: p.userId || undefined })) || [],
        };
        const required = getTotalRequiredWorkers(workerData);
        const assigned = getAssignedWorkerCount(workerData);
        return required > assigned && new Date(s.startTime) > new Date();
    }) || [];

    understaffedShifts.forEach(s => {
        const workerData = {
            ...s,
            assignedPersonnel: s.assignedPersonnel?.map((p: any) => ({ ...p, userId: p.userId || undefined })) || [],
        };
        const required = getTotalRequiredWorkers(workerData);
        const assigned = getAssignedWorkerCount(workerData);
        items.push({
            id: `us-${s.id}`,
            type: 'understaffed_shift',
            title: `Shift needs ${required - assigned} more staff`,
            subtitle: `${s.job?.name || 'Unknown Job'} on ${formatDateForDisplay(s.date)}`,
            date: new Date(s.startTime),
            priority: 'high',
            actionUrl: `/shifts/${s.id}`,
            Icon: AlertTriangle,
        });
    });

    // Sort by priority then date
    return items.sort((a, b) => {
      if (a.priority === 'high' && b.priority !== 'high') return -1;
      if (a.priority !== 'high' && b.priority === 'high') return 1;
      return b.date.getTime() - a.date.getTime();
    });

  }, [timesheets, shifts]);

  if (timesheetsLoading || shiftsLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-64" />
                <Skeleton className="h-3 w-48" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Inbox className="h-5 w-5" />
          Action Inbox
        </CardTitle>
        <Badge variant="destructive">{actionItems.length}</Badge>
      </CardHeader>
      <CardContent>
        {actionItems.length > 0 ? (
          <div className="space-y-2">
            {actionItems.map((item) => (
              <ActionItemRow key={item.id} item={item} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Inbox className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="font-medium">All caught up!</p>
            <p className="text-sm">No pending action items.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
