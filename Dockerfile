# Multi-stage Dockerfile for Next.js with Prisma
FROM node:20-slim AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json* ./
RUN npm ci --legacy-peer-deps
RUN npm install --os=linux --cpu=x64 sharp --legacy-peer-deps

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Install OpenSSL for Prisma with timeout and retry
RUN apt-get update -y && \
    apt-get install -y --no-install-recommends openssl ca-certificates && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Generate Prisma client
RUN npx prisma generate

# Set Node.js options for build - CRITICAL: Set these BEFORE the build
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV="production"
ENV BUILD_TIME="true"
ENV NEXT_PHASE="phase-production-build"
ENV CLOUDBUILD="true"
# Explicitly unset DATABASE_URL to force build-time mode
ENV DATABASE_URL=""
ENV GCS_AVATAR_BUCKET="handsonavatarbucket"
ENV GCS_BUCKET_NAME="handsonavatarbucket"
# Build the application with explicit environment
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

# Install OpenSSL for Prisma
RUN apt-get update -y && apt-get install -y --no-install-recommends openssl \
    ca-certificates && apt-get clean && rm -rf /var/lib/apt/lists/*

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the standalone build and required files
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/prisma ./node_modules/prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma
COPY --from=builder /app/package.json ./package.json
# Copy startup script used to locate server.js and handle startup
COPY --from=builder --chown=nextjs:nodejs /app/scripts/startup-with-migration.js ./scripts/startup-with-migration.js

# Set the correct permission for prerender cache
RUN chown -R nextjs:nodejs .next

USER nextjs

# Cloud Run expects the app to listen on PORT environment variable
EXPOSE 8080

# Start via startup script (auto-detects server.js path)
CMD ["node", "serverjs"]
