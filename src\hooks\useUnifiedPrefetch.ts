import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useCallback } from 'react';
import { useUser } from "@/hooks/use-user";
import { apiService } from '@/lib/services/api';
import { UserRole } from '@prisma/client';

const useUnifiedPrefetch = () => {
  const queryClient = useQueryClient();
  const { user } = useUser();

  const prefetchCriticalData = useCallback(async () => {
    if (!user || typeof window === 'undefined') return;

    try {
      console.log('Prefetching critical data for user:', user.name);
      const prefetchPromises: Promise<any>[] = [];

      // Prefetch common critical data for all roles
      prefetchPromises.push(
        queryClient.prefetchQuery({
          queryKey: ['notifications'],
          queryFn: apiService.getNotifications,
          staleTime: 5 * 60 * 1000, // 5 minutes
        }),
        queryClient.prefetchQuery({
          queryKey: ['announcements'],
          queryFn: apiService.getAnnouncements,
          staleTime: 15 * 60 * 1000, // 15 minutes
        })
      );

      await Promise.allSettled(prefetchPromises);
    } catch (error) {
      console.error('Critical data prefetching failed:', error);
    }
  }, [user, queryClient]);

  const prefetchSecondaryData = useCallback(async () => {
    if (!user || typeof window === 'undefined') return;

    try {
      console.log('Prefetching secondary data for user:', user.name);
      const prefetchPromises: Promise<any>[] = [];

      // Define role-specific secondary prefetching
      switch (user.role) {
        case UserRole.Admin:
          prefetchPromises.push(
            queryClient.prefetchQuery({
              queryKey: ['companies', { fetchAll: true }],
              queryFn: () => apiService.getCompanies({ fetchAll: true }),
              staleTime: 30 * 60 * 1000, // 30 minutes
            })
          );
          break;
        case UserRole.CompanyUser:
          if (user.companyId) {
            prefetchPromises.push(
              queryClient.prefetchQuery({
                queryKey: ['jobs', { companyId: user.companyId }],
                queryFn: () => apiService.getJobs({ companyId: user.companyId }),
                staleTime: 10 * 60 * 1000, // 10 minutes
              })
            );
          }
          break;
        // Add other roles as needed
      }

      await Promise.allSettled(prefetchPromises);
    } catch (error) {
      console.error('Secondary data prefetching failed:', error);
    }
  }, [user, queryClient]);

  useEffect(() => {
    if (user && typeof window !== 'undefined') {
      // Prefetch critical data immediately
      prefetchCriticalData();

      // Prefetch secondary data after a short delay
      const timer = setTimeout(prefetchSecondaryData, 3000); // 3-second delay
      return () => clearTimeout(timer);
    }
  }, [user, prefetchCriticalData, prefetchSecondaryData]);

  return { prefetchCriticalData, prefetchSecondaryData };
};

export default useUnifiedPrefetch;