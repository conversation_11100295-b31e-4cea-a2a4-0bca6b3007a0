"use client"; // 👈 this wrapper runs in the client

import { useRouter } from "next/navigation";
import React from "react";
import { UserRole } from "@/lib/types";
import { useSession } from "next-auth/react";
import { getDashboardPath } from "@/lib/routing";

type PageAuthProps = {
  allowedRoles?: UserRole[];
  redirectTo?: string;
};

export function withPageAuthRequired<P extends object>(
  Component: React.ComponentType<P>,
  options: PageAuthProps = {}
) {
  return function PageAuthWrapper(props: P) {
    const { data: session, status } = useSession();
    const router = useRouter();

    React.useEffect(() => {
      if (status === 'loading') return;

      if (!session?.user) {
        router.push(options.redirectTo || "/login");
        return;
      }

      if (
        options.allowedRoles &&
        options.allowedRoles.length > 0 &&
        !options.allowedRoles.includes(session.user.role as UserRole)
      ) {
        const dest = getDashboardPath(session.user.role as UserRole, (session.user as any).companyId);
        router.push(dest || "/unauthorized");
        return;
      }
    }, [session, status, router]);

    if (status === 'loading') {
      return <div>Loading...</div>; // or a proper loading spinner
    }

    if (!session?.user) {
      return null; // Will redirect in useEffect
    }

    return <Component {...props} user={session.user} />;
  };
}