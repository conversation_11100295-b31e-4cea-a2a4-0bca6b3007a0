export async function processImageBuffer(buffer: Buffer, mimeType: string, maxWidth = 200, maxHeight = 200, quality = 85) {
  // Minimal shim: pass-through and build a data URL. Replace with sharp for real processing.
  const base64 = buffer.toString('base64');
  const dataUrl = `data:${mimeType};base64,${base64}`;
  return {
    dataUrl,
    size: buffer.length,
    mimeType,
    width: maxWidth,
    height: maxHeight,
  };
}

export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!validTypes.includes(file.type)) {
    return { valid: false, error: 'Please select a valid image file (JPEG, PNG, WebP, or GIF)' };
  }

  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 5MB' };
  }

  return { valid: true };
}
