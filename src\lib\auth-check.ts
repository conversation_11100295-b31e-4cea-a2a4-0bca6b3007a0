import { prisma } from './prisma';
import { AuthenticatedUser } from './middleware';
import { UserRole } from '@prisma/client';

export async function canCrewChiefManageShift(user: AuthenticatedUser, shiftId: string): Promise<boolean> {
  if (!user) return false;
  // Admins and Managers have full access to clock management
  if (user.role === UserRole.Admin || user.role === UserRole.Manager) return true;
  // Crew chiefs can only manage shifts where they are assigned as crew chief (CC role)
  if (user.role !== UserRole.CrewChief) return false;

  const shift = await prisma.shift.findUnique({
    where: { id: shiftId },
    select: {
      assignedPersonnel: {
        where: {
          userId: user.id,
          roleCode: 'CC',
        },
      },
    },
  });

  return !!shift && shift.assignedPersonnel.length > 0;
}
