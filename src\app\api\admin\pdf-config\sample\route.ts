import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

interface PDFElement {
  id: string;
  type: 'text' | 'table' | 'signature' | 'image';
  label: string;
  x: number;
  y: number;
  width: number;
  height: number;
  dataKey?: string;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold';
  required: boolean;
}

interface PDFConfiguration {
  id: string;
  name: string;
  pageSize: 'letter' | 'a4';
  pageOrientation: 'portrait' | 'landscape';
  elements: PDFElement[];
  createdAt: string;
  updatedAt: string;
}

// Sample data for demonstration
const sampleData = {
  headerTitle: 'HOLI TIMESHEET',
  jobNumber: 'JOB-2024-001',
  jobName: 'Red Rocks Amphitheater Show',
  customerLabel: 'Customer:',
  customerName: 'Live Nation Entertainment',
  dateLabel: 'Date:',
  date: '2024-01-15',
  employeeTable: [
    { name: '<PERSON>', role: 'Crew Chief', hours: '8.0', rate: '$25.00', total: '$200.00' },
    { name: 'Jane Doe', role: 'Stagehand', hours: '8.0', rate: '$20.00', total: '$160.00' },
    { name: 'Mike Johnson', role: 'Fork Operator', hours: '6.0', rate: '$22.00', total: '$132.00' },
  ],
  customerSignatureLabel: 'Customer Signature:',
  customerSignatureBox: '[Signature Area]'
};

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'Admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const configuration: PDFConfiguration = await request.json();
    
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const { width, height } = page.getSize();
    
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Render elements based on configuration
    configuration.elements.forEach((element) => {
      const data = sampleData[element.dataKey as keyof typeof sampleData];
      const fontToUse = element.fontWeight === 'bold' ? boldFont : font;
      const y = height - element.y;

      switch (element.type) {
        case 'text':
          if (typeof data === 'string') {
            page.drawText(data, {
              x: element.x,
              y: y,
              font: fontToUse,
              size: element.fontSize || 12,
              color: rgb(0, 0, 0),
            });
          }
          break;

        case 'table':
          if (element.dataKey === 'employeeTable' && Array.isArray(data)) {
            // Manual table drawing
            const startX = element.x;
            let currentY = y;
            const rowHeight = 20;
            const headers = ['Name', 'Role', 'Hours', 'Rate', 'Total'];
            const colWidths = [150, 100, 50, 50, 50];

            // Draw header
            let currentX = startX;
            headers.forEach((header, i) => {
              page.drawText(header, {
                x: currentX + 2,
                y: currentY - 15,
                font: boldFont,
                size: element.fontSize || 8,
              });
              currentX += colWidths[i];
            });
            currentY -= rowHeight;

            // Draw rows
            data.forEach((row: any) => {
              currentX = startX;
              const rowValues = [row.name, row.role, row.hours, row.rate, row.total];
              rowValues.forEach((text, i) => {
                 page.drawText(text, {
                    x: currentX + 2,
                    y: currentY - 15,
                    font: font,
                    size: element.fontSize || 8,
                 });
                 currentX += colWidths[i];
              });
              currentY -= rowHeight;
            });
          }
          break;

        case 'signature':
          page.drawRectangle({
            x: element.x,
            y: y - element.height,
            width: element.width,
            height: element.height,
            borderColor: rgb(0, 0, 0),
            borderWidth: 1,
          });
          page.drawText('Signature', {
            x: element.x + 5,
            y: y - element.height - 10,
            font,
            size: 8,
            color: rgb(0.5, 0.5, 0.5),
          });
          break;

        case 'image':
          page.drawRectangle({
            x: element.x,
            y: y - element.height,
            width: element.width,
            height: element.height,
            borderColor: rgb(0, 0, 0),
            borderWidth: 1,
          });
          page.drawText('[Image Placeholder]', {
            x: element.x + 5,
            y: y - element.height / 2,
            font,
            size: 8,
            color: rgb(0.5, 0.5, 0.5),
          });
          break;
      }
    });

    const pdfBytes = await pdfDoc.save();
    const pdfBuffer = Buffer.from(pdfBytes);

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="timesheet-sample-${Date.now()}.pdf"`,
      },
    });

  } catch (error) {
    console.error('Error generating sample PDF:', error);
    return NextResponse.json(
      { error: 'Failed to generate sample PDF' },
      { status: 500 }
    );
  }
}