import { PrismaClient } from '@prisma/client';
import { applyTimesheetScope, applyShiftScope, applyJobScope, applyTimeEntryScope } from '@/lib/permissions';

// Declare a global variable to hold the Prisma client instance.
// This prevents creating a new connection on every hot reload in development.
declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

// Create a new Prisma client instance, or use the existing one if it's already on the global object.
const prisma = global.prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['warn', 'error'] : ['error'], // Reduce logging for performance
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});


// In development, assign the new client to the global object.
if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma;
}

export { prisma };
