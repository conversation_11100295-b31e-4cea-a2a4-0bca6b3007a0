import { UserRole } from '@prisma/client';

/**
 * Central place to map a user role to its dashboard route.
 * Keeps routing consistent across login, dashboard hub, and guards/middleware.
 */
export function getDashboardPath(role: UserRole, companyId?: string | null): string {
  switch (role) {
    case 'Admin':
      return '/admin';
    case 'Manager':
      // Managers share the admin dashboard experience for now
      return '/admin';
    case 'CrewChief':
      return '/crew-chief';
    case 'StageHand':
      return '/employee';
    case 'CompanyUser':
      // We maintain a single company dashboard that derives companyId from session
      // Legacy paths like `/companies/:id/dashboard` are redirected in middleware
      return '/company';
    default:
      return '/dashboard';
  }
}

