import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function PUT(request: Request, { params }: { params: { assignmentId: string } }) {
  const assignmentId = params.assignmentId;
  const { userId, roleCode } = await request.json();

  try {
    const updatedAssignment = await prisma.assignedPersonnel.update({
      where: { id: assignmentId },
      data: {
        userId,
        roleCode,
      },
    });
    return NextResponse.json(updatedAssignment);
  } catch (error) {
    console.error('Error updating assignment:', error);
    return NextResponse.json({ error: 'Failed to update assignment' }, { status: 500 });
  }
}
