"use client";

import Link from "next/link";
import Image from "next/image";
import { useTheme } from "next-themes";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  Home,
  Briefcase,
  Users,
  Building,
  FileText,
  Settings,
  Clock,
  ChevronDown,
} from "lucide-react";
import { MobileNavMenu } from "./MobileNavMenu";
import { ThemeSwitcher } from "./theme-switcher";
import { MobileProfileNav } from "./mobile-profile-nav";
import { UserNav } from "./user-nav";
import EnhancedNotificationCenter from "./enhanced-notification-center";
import React from "react";
import { useUser } from "@/hooks/use-user";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Main nav for all roles
const navItems = [
  { href: "/dashboard", label: "Dashboard", icon: Home, roles: ["Admin", "Manager", "CrewChief", "StageHand", "CompanyUser"] },
  { href: "/jobs", label: "Jobs", icon: Briefcase, roles: ["Admin", "Manager", "CrewChief", "CompanyUser"] },
  { href: "/shifts", label: "Shifts", icon: Clock, roles: ["Admin", "Manager", "CrewChief", "StageHand", "CompanyUser"] },
  { href: "/stagehands", label: "Stagehands", icon: Users, roles: ["Admin", "Manager", "CrewChief"] },
];

// Admin-only nav (excluding Clients, handled separately as dropdown)
const adminNavItems = [
  { href: "/timesheets", label: "Timesheets", icon: FileText, roles: ["Admin", "Manager"] },
  { href: "/admin/payroll", label: "Payroll", icon: FileText, roles: ["Admin"] },
  { href: "/admin/settings", label: "Admin Settings", icon: Settings, roles: ["Admin"] },
];

export default function Header({ children }: { children?: React.ReactNode }) {
  const pathname = usePathname();
  const { theme } = useTheme();
  const { user, status } = useUser();


  // ✅ Build navigation based on role
  const getVisibleNavItems = () => {
    if (status === "loading" || !user?.role) return navItems.slice(0, 2);
    const userRole = user.role as string;
    let mainItems = navItems.filter((item) => item.roles.includes(userRole));
    if (userRole === "Admin") return [...mainItems, ...adminNavItems];
    return mainItems;
  };

  const visibleNavItems = getVisibleNavItems();
  const isAdmin = user?.role === "Admin";

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur-lg supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            {/* Logo */}
            <Link href="/dashboard" className="flex items-center space-x-2 mr-6">
  <Image
    src={
      theme === "dark"
        ? "https://storage.googleapis.com/public_uploads_holi/images/companylogos/dark.png"
        : "https://storage.googleapis.com/public_uploads_holi/images/companylogos/light.png"
    }
    alt="HandsOnLabor"
    width={260}   // ⬆️ Increased width
    height={80}   // ⬆️ Increased height
    className="object-contain max-h-16 sm:max-h-20" // responsive cap
    priority
  />
</Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center gap-2 text-sm font-medium">
              {visibleNavItems.map((item) => {
                // Skip companies/clients here (handled in dropdown below)
                if (isAdmin && (item.href === "/companies" || item.href === "/clients")) return null;

                const Icon = item.icon;
                const isActive =
                  pathname === item.href ||
                  (item.href !== "/dashboard" && pathname.startsWith(item.href));

                return (
                  <Link
                    key={item.label}
                    href={item.href}
                    className={cn(
                      "flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200",
                      isActive
                        ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                    )}
                  >
                    <Icon size={16} />
                    <span className="hidden sm:inline">{item.label}</span>
                  </Link>
                );
              })}

              {/* ✅ Clients Dropdown (Admin only) */}
              {isAdmin && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button
                      className={cn(
                        "flex items-center gap-1 px-3 py-2 rounded-lg transition-all duration-200",
                        pathname.startsWith("/companies") || pathname.startsWith("/clients")
                          ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
                          : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                      )}
                    >
                      <Building size={16} />
                      <span>Clients</span>
                      <ChevronDown size={14} className="ml-1 opacity-70" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-44">
                    <DropdownMenuItem asChild>
                      <Link href="/companies" className="flex items-center gap-2">
                        <Building size={14} />
                        Companies
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/clients" className="flex items-center gap-2">
                        <Users size={14} />
                        Client Users
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </nav>
          </div>

          {/* Right Section */}
          <div className="flex items-center space-x-2">
            {children}
            <div className="hidden md:flex items-center space-x-2">
              <EnhancedNotificationCenter />
              <ThemeSwitcher />
              <UserNav />
            </div>
            <div className="flex md:hidden items-center space-x-2">
              <MobileProfileNav />
              <ThemeSwitcher />
              <MobileNavMenu />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
