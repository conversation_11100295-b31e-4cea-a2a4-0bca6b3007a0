"use client";

import React, { useEffect, useMemo, useState } from "react";
import { withAuth } from "@/lib/withAuth";
import { useUser } from "@/hooks/use-user";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { UserRole } from "@prisma/client";

// Simple fetch helpers
async function getConfig() {
  const res = await fetch("/api/admin/up-for-grabs/config", { cache: "no-store" });
  if (!res.ok) throw new Error("Failed to load config");
  return res.json();
}

async function saveConfig(payload: any) {
  const res = await fetch("/api/admin/up-for-grabs/config", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  });
  if (!res.ok) throw new Error("Failed to save config");
  return res.json();
}

async function searchUsers(q: string) {
  const params = new URLSearchParams({ search: q, fetchAll: "true" });
  const res = await fetch(`/api/users?${params.toString()}`);
  if (!res.ok) throw new Error("Failed to search users");
  const data = await res.json();
  return data.users as { id: string; name: string; email: string; role: string }[];
}

function UpForGrabsSettingsPage() {
  const { user } = useUser();
  const { toast } = useToast();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const [allowedUsers, setAllowedUsers] = useState<{ id: string; name: string; email: string }[]>([]);
  const [notifyCrewChiefs, setNotifyCrewChiefs] = useState(false);
  const [notifyEmployees, setNotifyEmployees] = useState(true);
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<{ id: string; name: string; email: string; role: string }[]>([]);

  useEffect(() => {
    (async () => {
      try {
        const cfg = await getConfig();
        setAllowedUsers(cfg.allowedUsers || []);
        setNotifyCrewChiefs(!!cfg.notifyCrewChiefs);
        setNotifyEmployees(cfg.notifyEmployees !== false);
      } catch {}
      setLoading(false);
    })();
  }, []);

  useEffect(() => {
    const t = setTimeout(async () => {
      if (!query.trim()) { setResults([]); return; }
      try {
        const users = await searchUsers(query.trim());
        setResults(users);
      } catch {}
    }, 300);
    return () => clearTimeout(t);
  }, [query]);

  const onAdd = (user: { id: string; name: string; email: string }) => {
    setAllowedUsers(prev => prev.find(u => u.id === user.id) ? prev : [...prev, user]);
  };
  const onRemove = (id: string) => setAllowedUsers(prev => prev.filter(x => x.id !== id));

  const onSave = async () => {
    setSaving(true);
    try {
      const allowedUserIds = allowedUsers.map(u => u.id);
      await saveConfig({ allowedUserIds, notifyCrewChiefs, notifyEmployees });
      toast({
        title: "Success",
        description: "Settings saved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!user || user.role !== UserRole.Admin) {
    router.push("/dashboard");
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Up For Grabs Notification Settings</CardTitle>
          <CardDescription>Manage the global employee list to notify when a shift is offered up for grabs.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Search employees to add</Label>
            <Input placeholder="Search by name or email" value={query} onChange={e => setQuery(e.target.value)} />
            {results.length > 0 && (
              <div className="border rounded p-2 max-h-64 overflow-auto space-y-2">
                {results.map(u => (
                  <div key={u.id} className="flex items-center justify-between text-sm">
                    <span>{u.name} <span className="text-muted-foreground">({u.email})</span></span>
                    <Button size="sm" variant="outline" onClick={() => onAdd(u)}>Add</Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Separator />

          <div className="space-y-2">
            <Label>Selected Employees</Label>
            {allowedUsers.length === 0 ? (
              <p className="text-sm text-muted-foreground">No users selected.</p>
            ) : (
              <div className="flex flex-wrap gap-2">
                {allowedUsers.map(u => (
                  <Badge key={u.id} variant="secondary" className="flex items-center gap-2">
                    <span>{u.name}</span>
                    <button className="text-xs" onClick={() => onRemove(u.id)}>x</button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <Separator />

          <div className="space-y-2">
            <Label>Fallback audience</Label>
            <div className="text-sm text-muted-foreground">
              If the list is empty, the system will notify Employees and Crew Chiefs by default.
            </div>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm">
                <input type="checkbox" checked={notifyEmployees} onChange={e => setNotifyEmployees(e.target.checked)} />
                Notify Employees
              </label>
              <label className="flex items-center gap-2 text-sm">
                <input type="checkbox" checked={notifyCrewChiefs} onChange={e => setNotifyCrewChiefs(e.target.checked)} />
                Notify Crew Chiefs
              </label>
            </div>
          </div>

          <div className="pt-2">
            <Button onClick={onSave} disabled={saving}>{saving ? "Saving..." : "Save Settings"}</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default withAuth(UpForGrabsSettingsPage, "Admin");
