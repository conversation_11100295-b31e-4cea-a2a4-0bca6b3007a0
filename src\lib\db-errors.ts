'use server';

import { NextResponse } from 'next/server';
import { PrismaClientInitializationError, PrismaClientKnownRequestError, PrismaClientUnknownRequestError } from '@prisma/client/runtime/library';
import { ApiError } from './api-client';

// #region API Error Handling
export class DatabaseConnectionError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'DatabaseConnectionError';
  }
}

export class DatabaseTimeoutError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'DatabaseTimeoutError';
  }
}

export function handleDatabaseError(error: any): ApiError {
  console.error('Database error occurred:', error);

  if (error instanceof PrismaClientInitializationError) {
    return {
      name: 'PrismaClientInitializationError',
      message: 'Database connection failed. Please try again later.',
      code: 'DATABASE_CONNECTION_ERROR',
      statusCode: 503,
      details: { type: 'PrismaClientInitializationError', errorCode: error.errorCode, message: error.message }
    };
  }

  if (error instanceof PrismaClientKnownRequestError) {
    switch (error.code) {
        case 'P1001':
          return {
            name: 'DatabaseUnreachable',
            message: 'Database server is unreachable. Please check your connection.',
            code: 'DATABASE_UNREACHABLE',
            statusCode: 503,
            details: { prismaCode: error.code }
          };
        case 'P1002':
          return {
            name: 'DatabaseTimeout',
            message: 'Database connection timeout. Please try again.',
            code: 'DATABASE_TIMEOUT',
            statusCode: 504,
            details: { prismaCode: error.code }
          };
        case 'P1003':
          return {
            name: 'DatabaseNotFound',
            message: 'Database does not exist.',
            code: 'DATABASE_NOT_FOUND',
            statusCode: 503,
            details: { prismaCode: error.code }
          };
        case 'P1008':
          return {
            name: 'DatabaseOperationTimeout',
            message: 'Database operation timeout.',
            code: 'DATABASE_OPERATION_TIMEOUT',
            statusCode: 504,
            details: { prismaCode: error.code }
          };
        case 'P1009':
          return {
            name: 'DatabaseAlreadyExists',
            message: 'Database already exists.',
            code: 'DATABASE_ALREADY_EXISTS',
            statusCode: 409,
            details: { prismaCode: error.code }
          };
        case 'P1010':
          return {
            name: 'DatabaseAccessDenied',
            message: 'Access denied for database user.',
            code: 'DATABASE_ACCESS_DENIED',
            statusCode: 403,
            details: { prismaCode: error.code }
          };
        case 'P1011':
          return {
            name: 'DatabaseTlsError',
            message: 'Error opening TLS connection.',
            code: 'DATABASE_TLS_ERROR',
            statusCode: 503,
            details: { prismaCode: error.code }
          };
        case 'P1012':
          return {
            name: 'DatabaseSchemaError',
            message: 'Database schema validation error.',
            code: 'DATABASE_SCHEMA_ERROR',
            statusCode: 500,
            details: { prismaCode: error.code }
          };
        case 'P1013':
          return {
            name: 'DatabaseStringInvalid',
            message: 'Invalid database string provided.',
            code: 'DATABASE_STRING_INVALID',
            statusCode: 500,
            details: { prismaCode: error.code }
          };
        case 'P1014':
          return {
            name: 'DatabaseModelNotFound',
            message: 'Underlying model does not exist.',
            code: 'DATABASE_MODEL_NOT_FOUND',
            statusCode: 500,
            details: { prismaCode: error.code }
          };
        case 'P1015':
          return {
            name: 'DatabaseVersionUnsupported',
            message: 'Unsupported database version.',
            code: 'DATABASE_VERSION_UNSUPPORTED',
            statusCode: 503,
            details: { prismaCode: error.code }
          };
        case 'P1016':
          return {
            name: 'DatabaseRawQueryFailed',
            message: 'Raw query failed.',
            code: 'DATABASE_RAW_QUERY_FAILED',
            statusCode: 500,
            details: { prismaCode: error.code }
          };
        case 'P1017':
          return {
            name: 'DatabaseConnectionClosed',
            message: 'Server has closed the connection.',
            code: 'DATABASE_CONNECTION_CLOSED',
            statusCode: 503,
            details: { prismaCode: error.code }
          };
        default:
          return {
            name: 'DatabaseOperationError',
            message: 'Database operation failed.',
            code: 'DATABASE_OPERATION_ERROR',
            statusCode: 500,
            details: { prismaCode: error.code, message: error.message }
          };
      }
  }

  if (error instanceof PrismaClientUnknownRequestError) {
    return {
      name: 'PrismaClientUnknownRequestError',
      message: 'An unknown database error occurred.',
      code: 'DATABASE_UNKNOWN_ERROR',
      statusCode: 500,
      details: { type: 'PrismaClientUnknownRequestError', message: error.message }
    };
  }

  if (error instanceof DatabaseConnectionError) {
    return { name: 'DatabaseConnectionError', message: error.message, code: 'DATABASE_CONNECTION_ERROR', statusCode: 503, details: error.originalError };
  }

  if (error instanceof DatabaseTimeoutError) {
    return { name: 'DatabaseTimeoutError', message: error.message, code: 'DATABASE_TIMEOUT_ERROR', statusCode: 504, details: error.originalError };
  }

  if (error.code === 'ECONNREFUSED') {
    return { name: 'DatabaseConnectionRefused', message: 'Database connection refused. The database server may be down.', code: 'DATABASE_CONNECTION_REFUSED', statusCode: 503, details: { networkError: error.code } };
  }

  if (error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET') {
    return { name: 'DatabaseConnectionTimeout', message: 'Database connection timeout. Please try again.', code: 'DATABASE_CONNECTION_TIMEOUT', statusCode: 504, details: { networkError: error.code } };
  }

  if (error.code === 'ENOTFOUND') {
    return { name: 'DatabaseHostNotFound', message: 'Database host not found. Please check the connection configuration.', code: 'DATABASE_HOST_NOT_FOUND', statusCode: 503, details: { networkError: error.code } };
  }

  return {
    name: 'DatabaseGenericError',
    message: 'An unexpected error occurred while accessing the database.',
    code: 'DATABASE_GENERIC_ERROR',
    statusCode: 500,
    details: { message: error.message, name: error.name, stack: process.env.ENV === 'development' ? error.stack : undefined }
  };
}

export function createErrorResponse(error: ApiError): NextResponse {
  return NextResponse.json({
    success: false,
    error: { message: error.message, code: error.code, details: error.details },
    timestamp: new Date().toISOString(),
  }, { status: error.statusCode });
}

export async function withDatabaseErrorHandling<T>(operation: () => Promise<T>): Promise<T | NextResponse> {
  try {
    return await operation();
  } catch (error) {
    const apiError = handleDatabaseError(error);
    return createErrorResponse(apiError);
  }
}
