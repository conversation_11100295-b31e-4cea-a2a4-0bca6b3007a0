/**
 * Bulk Operations Components
 * 
 * Provides comprehensive bulk operation capabilities including:
 * - Multi-select functionality
 * - Bulk actions (delete, update, export)
 * - Progress tracking
 * - Undo/redo functionality
 * - Batch processing with error handling
 */

"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Check, 
  X, 
  Trash2, 
  Edit, 
  Download, 
  Upload, 
  MoreHorizontal, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  Undo2
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface BulkAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  variant?: 'default' | 'destructive' | 'secondary';
  requiresConfirmation?: boolean;
  confirmationTitle?: string;
  confirmationDescription?: string;
  fields?: BulkActionField[];
  execute: (selectedIds: string[], data?: Record<string, any>) => Promise<BulkOperationResult>;
}

export interface BulkActionField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'date' | 'number';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
  validation?: (value: any) => string | null;
}

export interface BulkOperationResult {
  success: boolean;
  successCount: number;
  errorCount: number;
  errors?: Array<{ id: string; error: string }>;
  message?: string;
  undoAction?: () => Promise<void>;
}

export interface BulkOperationProgress {
  total: number;
  completed: number;
  errors: number;
  currentItem?: string;
  isComplete: boolean;
}

interface BulkOperationsProps {
  items: Array<{ id: string; [key: string]: any }>;
  selectedIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  actions: BulkAction[];
  className?: string;
  maxSelectableItems?: number;
  showSelectAll?: boolean;
  showProgress?: boolean;
}

interface SelectableItemProps {
  item: { id: string; [key: string]: any };
  isSelected: boolean;
  onSelectionChange: (id: string, selected: boolean) => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

interface BulkActionDialogProps {
  action: BulkAction;
  selectedCount: number;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data?: Record<string, any>) => void;
  isExecuting?: boolean;
}

// ============================================================================
// BULK OPERATIONS CONTEXT
// ============================================================================

interface BulkOperationsContextType {
  selectedIds: string[];
  selectItem: (id: string) => void;
  deselectItem: (id: string) => void;
  selectAll: () => void;
  deselectAll: () => void;
  isSelected: (id: string) => boolean;
  selectedCount: number;
  totalCount: number;
}

const BulkOperationsContext = React.createContext<BulkOperationsContextType | null>(null);

export const useBulkOperations = () => {
  const context = React.useContext(BulkOperationsContext);
  if (!context) {
    throw new Error('useBulkOperations must be used within a BulkOperationsProvider');
  }
  return context;
};

// ============================================================================
// SELECTABLE ITEM COMPONENT
// ============================================================================

export const SelectableItem: React.FC<SelectableItemProps> = ({
  item,
  isSelected,
  onSelectionChange,
  children,
  className,
  disabled = false
}) => {
  return (
    <div className={cn(
      'relative group transition-all duration-200',
      isSelected && 'ring-2 ring-primary ring-offset-2',
      disabled && 'opacity-50 cursor-not-allowed',
      className
    )}>
      {/* Selection Checkbox */}
      <div className={cn(
        'absolute top-2 left-2 z-10 transition-opacity duration-200',
        isSelected || !disabled ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
      )}>
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelectionChange(item.id, !!checked)}
          disabled={disabled}
          className="bg-background border-2 shadow-sm"
        />
      </div>
      
      {/* Item Content */}
      <div className={cn(
        'transition-all duration-200',
        isSelected && 'transform scale-[0.98]'
      )}>
        {children}
      </div>
    </div>
  );
};

// ============================================================================
// BULK ACTION DIALOG COMPONENT
// ============================================================================

const BulkActionDialog: React.FC<BulkActionDialogProps> = ({
  action,
  selectedCount,
  isOpen,
  onClose,
  onConfirm,
  isExecuting = false
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    action.fields?.forEach(field => {
      const value = formData[field.name];
      
      if (field.required && (!value || value.toString().trim() === '')) {
        newErrors[field.name] = `${field.label} is required`;
      } else if (field.validation && value) {
        const validationError = field.validation(value);
        if (validationError) {
          newErrors[field.name] = validationError;
        }
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfirm = () => {
    if (!action.fields || validateForm()) {
      onConfirm(formData);
    }
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({ ...prev, [fieldName]: '' }));
    }
  };

  const renderField = (field: BulkActionField) => {
    const value = formData[field.name] || '';
    const error = errors[field.name];

    switch (field.type) {
      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name}>{field.label}</Label>
            <Textarea
              id={field.name}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              className={error ? 'border-destructive' : ''}
            />
            {error && <p className="text-sm text-destructive">{error}</p>}
          </div>
        );
      
      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name}>{field.label}</Label>
            <Select value={value} onValueChange={(val) => handleFieldChange(field.name, val)}>
              <SelectTrigger className={error ? 'border-destructive' : ''}>
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {error && <p className="text-sm text-destructive">{error}</p>}
          </div>
        );
      
      case 'number':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name}>{field.label}</Label>
            <Input
              id={field.name}
              type="number"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              className={error ? 'border-destructive' : ''}
            />
            {error && <p className="text-sm text-destructive">{error}</p>}
          </div>
        );
      
      case 'date':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name}>{field.label}</Label>
            <Input
              id={field.name}
              type="date"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              className={error ? 'border-destructive' : ''}
            />
            {error && <p className="text-sm text-destructive">{error}</p>}
          </div>
        );
      
      default:
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name}>{field.label}</Label>
            <Input
              id={field.name}
              type="text"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              className={error ? 'border-destructive' : ''}
            />
            {error && <p className="text-sm text-destructive">{error}</p>}
          </div>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {action.icon}
            {action.confirmationTitle || action.label}
          </DialogTitle>
          <DialogDescription>
            {action.confirmationDescription || 
             `This action will be applied to ${selectedCount} selected item${selectedCount !== 1 ? 's' : ''}.`}
          </DialogDescription>
        </DialogHeader>
        
        {action.fields && action.fields.length > 0 && (
          <div className="space-y-4 py-4">
            {action.fields.map(renderField)}
          </div>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isExecuting}>
            Cancel
          </Button>
          <Button
            variant={action.variant === 'destructive' ? 'destructive' : 'default'}
            onClick={handleConfirm}
            disabled={isExecuting}
          >
            {isExecuting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                {action.icon}
                {action.label}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// ============================================================================
// PROGRESS TRACKER COMPONENT
// ============================================================================

interface ProgressTrackerProps {
  progress: BulkOperationProgress;
  onCancel?: () => void;
  className?: string;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  progress,
  onCancel,
  className
}) => {
  const percentage = progress.total > 0 ? (progress.completed / progress.total) * 100 : 0;
  
  return (
    <Card className={cn('border-primary', className)}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin text-primary" />
              <span className="font-medium">Processing bulk operation...</span>
            </div>
            {onCancel && (
              <Button variant="ghost" size="sm" onClick={onCancel}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <Progress value={percentage} className="h-2" />
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              {progress.completed} of {progress.total} completed
              {progress.errors > 0 && ` (${progress.errors} errors)`}
            </span>
            <span>{Math.round(percentage)}%</span>
          </div>
          
          {progress.currentItem && (
            <p className="text-sm text-muted-foreground truncate">
              Processing: {progress.currentItem}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// MAIN BULK OPERATIONS COMPONENT
// ============================================================================

export const BulkOperations: React.FC<BulkOperationsProps> = ({
  items,
  selectedIds,
  onSelectionChange,
  actions,
  className,
  maxSelectableItems,
  showSelectAll = true,
  showProgress = true
}) => {
  const [activeAction, setActiveAction] = useState<BulkAction | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [progress, setProgress] = useState<BulkOperationProgress | null>(null);
  const [lastResult, setLastResult] = useState<BulkOperationResult | null>(null);
  const { toast } = useToast();

  const selectedCount = selectedIds.length;
  const totalCount = items.length;
  const isAllSelected = selectedCount === totalCount && totalCount > 0;
  const isPartiallySelected = selectedCount > 0 && selectedCount < totalCount;

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const idsToSelect = maxSelectableItems 
        ? items.slice(0, maxSelectableItems).map(item => item.id)
        : items.map(item => item.id);
      onSelectionChange(idsToSelect);
    } else {
      onSelectionChange([]);
    }
  };

  const handleActionClick = (action: BulkAction) => {
    if (selectedCount === 0) {
      toast({
        title: "No items selected",
        description: "Please select at least one item to perform this action.",
        variant: "destructive",
      });
      return;
    }

    if (action.requiresConfirmation || action.fields) {
      setActiveAction(action);
    } else {
      executeAction(action);
    }
  };

  const executeAction = async (action: BulkAction, data?: Record<string, any>) => {
    setIsExecuting(true);
    setActiveAction(null);
    
    if (showProgress) {
      setProgress({
        total: selectedCount,
        completed: 0,
        errors: 0,
        isComplete: false
      });
    }

    try {
      const result = await action.execute(selectedIds, data);
      setLastResult(result);
      
      if (result.success) {
        toast({
          title: "Bulk operation completed",
          description: result.message || `Successfully processed ${result.successCount} items.`,
        });
        
        // Clear selection after successful operation
        onSelectionChange([]);
      } else {
        toast({
          title: "Bulk operation failed",
          description: result.message || `Failed to process ${result.errorCount} items.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Bulk operation error",
        description: error instanceof Error ? error.message : "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
      setProgress(null);
    }
  };

  const handleUndo = async () => {
    if (lastResult?.undoAction) {
      try {
        await lastResult.undoAction();
        toast({
          title: "Action undone",
          description: "The last bulk operation has been undone.",
        });
        setLastResult(null);
      } catch (error) {
        toast({
          title: "Undo failed",
          description: "Failed to undo the last operation.",
          variant: "destructive",
        });
      }
    }
  };

  if (selectedCount === 0 && !showSelectAll) {
    return null;
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Progress Tracker */}
      {progress && showProgress && (
        <ProgressTracker progress={progress} />
      )}
      
      {/* Bulk Operations Bar */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Select All Checkbox */}
              {showSelectAll && (
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={isAllSelected}
                    ref={(el) => {
                      if (el) (el as HTMLInputElement).indeterminate = isPartiallySelected;
                    }}
                    onCheckedChange={handleSelectAll}
                    disabled={isExecuting}
                  />
                  <span className="text-sm font-medium">
                    {isAllSelected ? 'Deselect All' : 'Select All'}
                  </span>
                </div>
              )}
              
              {/* Selection Count */}
              {selectedCount > 0 && (
                <Badge variant="secondary" className="font-medium">
                  {selectedCount} of {totalCount} selected
                  {maxSelectableItems && selectedCount >= maxSelectableItems && (
                    <span className="ml-1 text-xs">(max)</span>
                  )}
                </Badge>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex items-center gap-2">
              {/* Undo Button */}
              {lastResult?.undoAction && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUndo}
                  disabled={isExecuting}
                >
                  <Undo2 className="h-4 w-4 mr-1" />
                  Undo
                </Button>
              )}
              
              {/* Action Buttons */}
              {actions.map(action => (
                <Button
                  key={action.id}
                  variant={action.variant || 'default'}
                  size="sm"
                  onClick={() => handleActionClick(action)}
                  disabled={isExecuting || selectedCount === 0}
                >
                  {action.icon}
                  {action.label}
                </Button>
              ))}
              
              {/* More Actions Dropdown */}
              {actions.length > 3 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" disabled={isExecuting || selectedCount === 0}>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {actions.slice(3).map(action => (
                      <DropdownMenuItem
                        key={action.id}
                        onClick={() => handleActionClick(action)}
                        className={action.variant === 'destructive' ? 'text-destructive' : ''}
                      >
                        {action.icon}
                        {action.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Action Dialog */}
      {activeAction && (
        <BulkActionDialog
          action={activeAction}
          selectedCount={selectedCount}
          isOpen={!!activeAction}
          onClose={() => setActiveAction(null)}
          onConfirm={(data) => executeAction(activeAction, data)}
          isExecuting={isExecuting}
        />
      )}
    </div>
  );
};

// ============================================================================
// BULK OPERATIONS PROVIDER
// ============================================================================

interface BulkOperationsProviderProps {
  children: React.ReactNode;
  items: Array<{ id: string; [key: string]: any }>;
  selectedIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
}

export const BulkOperationsProvider: React.FC<BulkOperationsProviderProps> = ({
  children,
  items,
  selectedIds,
  onSelectionChange
}) => {
  const selectItem = useCallback((id: string) => {
    if (!selectedIds.includes(id)) {
      onSelectionChange([...selectedIds, id]);
    }
  }, [selectedIds, onSelectionChange]);

  const deselectItem = useCallback((id: string) => {
    onSelectionChange(selectedIds.filter(selectedId => selectedId !== id));
  }, [selectedIds, onSelectionChange]);

  const selectAll = useCallback(() => {
    onSelectionChange(items.map(item => item.id));
  }, [items, onSelectionChange]);

  const deselectAll = useCallback(() => {
    onSelectionChange([]);
  }, [onSelectionChange]);

  const isSelected = useCallback((id: string) => {
    return selectedIds.includes(id);
  }, [selectedIds]);

  const contextValue: BulkOperationsContextType = {
    selectedIds,
    selectItem,
    deselectItem,
    selectAll,
    deselectAll,
    isSelected,
    selectedCount: selectedIds.length,
    totalCount: items.length,
  };

  return (
    <BulkOperationsContext.Provider value={contextValue}>
      {children}
    </BulkOperationsContext.Provider>
  );
};

export default BulkOperations;
