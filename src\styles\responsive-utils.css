/* src/styles/responsive-utils.css */

/* Utility classes for responsive design */

/* Hide elements on small screens (mobile-first approach) */
.hidden-mobile {
  display: none !important;
}

/* Show elements only on small screens */
@media (max-width: 767px) {
  .show-mobile {
    display: block !important;
  }
}

/* Flexbox utilities for responsive layouts */
.flex-col-mobile {
  @media (max-width: 767px) {
    flex-direction: column !important;
  }
}

.flex-wrap-mobile {
  @media (max-width: 767px) {
    flex-wrap: wrap !important;
  }
}

/* Spacing adjustments for mobile */
.p-mobile-4 {
  @media (max-width: 767px) {
    padding: 1rem !important;
  }
}

.mx-mobile-auto {
  @media (max-width: 767px) {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

/* Font size adjustments for mobile */
.text-sm-mobile {
  @media (max-width: 767px) {
    font-size: 0.875rem !important; /* 14px */
  }
}

/* Max width for content on mobile */
.max-w-full-mobile {
  @media (max-width: 767px) {
    max-width: 100% !important;
  }
}

/* General responsive adjustments for images */
img.responsive-img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Adjustments for safe area insets (iOS notch, etc.) */
body.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

body.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Example of a responsive container */
.responsive-container {
  width: 100%;
  padding: 1rem;
  box-sizing: border-box;
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 960px; /* Example max-width for larger screens */
    margin: 0 auto;
    padding: 1.5rem;
  }
}
