import { useQuery } from "@tanstack/react-query";
import { Job } from "@/lib/types";

async function getJobs(filters: {
  status?: string;
  companyId?: string;
  search?: string;
  sortBy?: string;
}): Promise<Job[]> {
  const { status, companyId, search, sortBy } = filters;
  const params = new URLSearchParams();
  if (status && status !== "all") params.append("status", status);
  if (companyId && companyId !== "all")
    params.append("companyId", companyId);
  if (search) params.append("search", search);
  if (sortBy) params.append("sortBy", sortBy);

  const res = await fetch(`/api/jobs?${params.toString()}`);
  if (!res.ok) {
    throw new Error("Failed to fetch jobs");
  }
  return res.json();
}

export function useJobs(filters: {
  status?: string;
  companyId?: string;
  search?: string;
  sortBy?: string;
}) {
  return useQuery<Job[], Error>({
    queryKey: ["jobs", filters],
    queryFn: () => getJobs(filters),
  });
}
