/* Custom animations for enhanced UI */

/* Recording indicator animation - mimics a real recording light */
@keyframes recording-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0);
  }
}

.recording-indicator {
  animation: recording-pulse 1.5s ease-in-out infinite;
}

/* Subtle card hover animations */
@keyframes card-hover {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-2px) scale(1.01);
  }
}

.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  animation: card-hover 0.3s ease-out forwards;
}

/* Shimmer effect for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dark .shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}

/* Status badge glow effects */
.status-glow-red {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.status-glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.status-glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.status-glow-amber {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

/* Backdrop blur enhancement */
.backdrop-blur-enhanced {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Enhanced focus states */
.focus-enhanced:focus {
  outline: none;
  ring: 2px;
  ring-color: rgb(59 130 246 / 0.5);
  ring-offset: 2px;
  ring-offset-color: rgb(255 255 255);
}

.dark .focus-enhanced:focus {
  ring-offset-color: rgb(17 24 39);
}

/* Micro-interactions for buttons */
.button-micro:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* Gradient text animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-text-animated {
  background: linear-gradient(-45deg, #6366f1, #8b5cf6, #ec4899, #f59e0b);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Stagger animation for list items */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  animation: stagger-in 0.6s ease forwards;
}

@keyframes stagger-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); /* Tailwind blue-500 */
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pulse-animation {
  animation: pulse 1.5s infinite;
}