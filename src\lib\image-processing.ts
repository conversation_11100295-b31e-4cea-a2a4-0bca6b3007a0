'use server';

import sharp from 'sharp';
import { ProcessedImage } from '@/lib/utils';

export function parseDataUrl(dataUrl: string): { buffer: Buffer; mimeType: string } {
  const matches = dataUrl.match(/^data:([^;]+);base64,(.+)$/);
  
  if (!matches) {
    throw new Error('Invalid data URL format');
  }

  const mimeType = matches[1];
  const base64Data = matches[2];
  
  const buffer = Buffer.from(base64Data, 'base64');
  
  return { buffer, mimeType };
}

export async function processImageBuffer(buffer: Buffer): Promise<ProcessedImage> {
  const processedBuffer = await sharp(buffer)
    .resize(256, 256, {
      fit: 'cover',
      position: 'entropy'
    })
    .webp({ quality: 80 })
    .toBuffer();

  const mimeType = 'image/webp';
  const size = processedBuffer.length;
  const dataUrl = `data:${mimeType};base64,${processedBuffer.toString('base64')}`;

  return { dataUrl, mimeType, size };
}
