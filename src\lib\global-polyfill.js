// Universal global polyfill for webpack ProvidePlugin and runtime environments
// This provides a consistent global object across all environments

(() => {
  'use strict';

  const getGlobalObject = () => {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof self !== 'undefined') return self;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    return {};
  };

  const globalObject = getGlobalObject();

  if (typeof globalObject.global === 'undefined') {
    try {
      Object.defineProperty(globalObject, 'global', {
        value: globalObject,
        writable: true,
        enumerable: false,
        configurable: true
      });
    } catch (e) {
      globalObject.global = globalObject;
    }
  }

  if (typeof globalObject.self === 'undefined') {
    try {
      Object.defineProperty(globalObject, 'self', {
        value: globalObject,
        writable: true,
        enumerable: false,
        configurable: true
      });
    } catch (e) {
      globalObject.self = globalObject;
    }
  }

  if (typeof globalObject.window === 'undefined' && typeof global !== 'undefined') {
    globalObject.window = global;
  }

  // Server-side specific polyfills
  if (typeof window === 'undefined' && typeof self !== 'undefined') {
    if (typeof globalObject.process === 'undefined') {
      globalObject.process = {
        env: {},
        platform: 'edge',
        version: 'edge-runtime',
        versions: { node: 'edge-runtime' }
      };
    }

    const chunkNames = ['webpackChunk_N_E', 'webpackJsonp', '__webpack_chunks__'];
    chunkNames.forEach(chunkName => {
      if (typeof globalObject[chunkName] === 'undefined') {
        globalObject[chunkName] = [];
      }
    });

    if (typeof globalObject.document === 'undefined') {
      globalObject.document = {
        createElement: () => ({}),
        getElementsByTagName: () => [],
        getElementById: () => null,
        addEventListener: () => {},
        removeEventListener: () => {},
      };
    }

    if (typeof globalObject.navigator === 'undefined') {
      globalObject.navigator = {
        userAgent: 'Mozilla/5.0 (Server Side Rendering)',
      };
    }

    if (typeof globalObject.location === 'undefined') {
      globalObject.location = {
        href: '',
        origin: '',
        protocol: 'https:',
        hostname: '',
        port: '',
        pathname: '/',
        search: '',
        hash: '',
      };
    }

    if (typeof globalObject.__webpack_require__ === 'undefined') {
      globalObject.__webpack_require__ = function(id) { 
        console.warn(`Server-side __webpack_require__ called with id: ${id}`);
        return {}; 
      };
    }

    if (typeof globalObject.__webpack_public_path__ === 'undefined') {
      globalObject.__webpack_public_path__ = '/_next/';
    }
  }
})();

export default (typeof globalThis !== 'undefined' ? globalThis : (typeof self !== 'undefined' ? self : (typeof window !== 'undefined' ? window : global)));
