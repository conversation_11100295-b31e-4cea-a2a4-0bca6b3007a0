import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  const { notifications } = await req.json();

  if (!notifications) {
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  try {
    await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        upForGrabsNotifications: notifications.push,
      },
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save settings:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
