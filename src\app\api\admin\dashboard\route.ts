import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { applyShiftScope } from '@/lib/permissions';
import { startOfToday, endOfToday, startOfTomorrow, endOfTomorrow, startOfWeek, endOfWeek } from 'date-fns';
import { getTotalRequiredWorkers, getAssignedWorkerCount } from '@/lib/worker-count-utils';

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const today = new Date();
    const todayStart = startOfToday();
    const todayEnd = endOfToday();
    const tomorrowStart = startOfTomorrow();
    const tomorrowEnd = endOfTomorrow();
    const weekStart = startOfWeek(today);
    const weekEnd = endOfWeek(today);

    // Parallelize data fetching
    const [
      jobMetrics,
      shiftCounts,
      recentActivityData,
      upcomingShiftsData,
      allShiftsForFulfillment,
      totalUsers,
    ] = await Promise.all([
      // 1. Job Metrics
      prisma.job.groupBy({
        by: ['status'],
        _count: { status: true },
      }),
      // 2. Shift Counts
      prisma.$transaction([
        prisma.shift.count({ where: applyShiftScope(user) }),
        prisma.shift.count({ where: applyShiftScope(user, { date: { gte: todayStart, lte: todayEnd } }) }),
        prisma.shift.count({ where: applyShiftScope(user, { date: { gte: tomorrowStart, lte: tomorrowEnd } }) }),
        prisma.shift.count({ where: applyShiftScope(user, { date: { gte: weekStart, lte: weekEnd } }) }),
        prisma.shift.count({ where: applyShiftScope(user, { status: { in: ['Active', 'InProgress'] } }) }),
      ]),
      // 3. Recent Activity Data
      prisma.$transaction([
        prisma.job.findMany({
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: { company: { select: { name: true } } },
        }),
        prisma.shift.findMany({
          where: applyShiftScope(user),
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: { job: { select: { name: true } } },
        }),
        prisma.timesheet.findMany({
          orderBy: { createdAt: 'desc' },
          take: 3,
        }),
      ]),
      // 4. Upcoming Shifts
      prisma.shift.findMany({
        where: applyShiftScope(user, { startTime: { gt: new Date() } }),
        orderBy: { startTime: 'asc' },
        take: 6,
        include: {
          job: {
            select: {
              name: true,
              company: { select: { company_logo_url: true, name: true } },
            },
          },
          assignedPersonnel: true,
        },
      }),
      // 5. Data for Fulfillment Calculation
      prisma.shift.findMany({
        where: applyShiftScope(user),
        include: {
          assignedPersonnel: true,
        },
      }),
      // 6. User count
      prisma.user.count({ where: { role: { not: 'CompanyUser' } } }),
    ]);

    // Process Job Metrics
    const totalJobs = jobMetrics.reduce((acc, group) => acc + group._count.status, 0);
    const activeJobs = jobMetrics.find(g => g.status === 'Active')?._count.status || 0;
    const completedJobs = jobMetrics.find(g => g.status === 'Completed')?._count.status || 0;

    // Process Shift Counts
    const [totalShifts, todayShiftsCount, tomorrowShiftsCount, thisWeekShiftsCount, activeShiftsCount] = shiftCounts;

    // Process Fulfillment
    const understaffedShifts = allShiftsForFulfillment.filter(shift => {
      const required = getTotalRequiredWorkers(shift);
      const assigned = getAssignedWorkerCount(shift);
      return required > 0 && assigned < required;
    });
    const fulfillmentRate = totalShifts > 0 ? ((totalShifts - understaffedShifts.length) / totalShifts) * 100 : 100;

    // Process Recent Activity
    const [recentJobs, recentShifts, recentTimesheets] = recentActivityData;
    const recentActivity = [
      ...recentJobs.map(j => ({ type: 'job', title: `New job: ${j.name}`, subtitle: j.company?.name || '', time: j.createdAt, icon: 'Briefcase', color: 'blue', id: `job-${j.id}` })),
      ...recentShifts.map(s => ({ type: 'shift', title: `Shift scheduled: ${s.job?.name || 'Unknown'}`, subtitle: s.startTime, time: s.createdAt, icon: 'Calendar', color: 'green', id: `shift-${s.id}` })),
      ...recentTimesheets.map(t => ({ type: 'timesheet', title: 'Timesheet submitted', subtitle: `Status: ${t.status}`, time: t.createdAt, icon: 'FileText', color: 'purple', id: `timesheet-${t.id}` })),
    ]
      .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
      .slice(0, 8);

    const data = {
      metrics: {
        jobs: { total: totalJobs, active: activeJobs, completed: completedJobs },
        shifts: {
          today: todayShiftsCount,
          tomorrow: tomorrowShiftsCount,
          thisWeek: thisWeekShiftsCount,
          active: activeShiftsCount,
        },
        workforce: { total: totalUsers, active: totalUsers }, // Placeholder for active workers
        fulfillment: { rate: fulfillmentRate, understaffed: understaffedShifts.length },
      },
      recentActivity,
      upcomingShifts: upcomingShiftsData,
    };

    return NextResponse.json(data);
  } catch (error) {
    console.error('[API_ADMIN_DASHBOARD_ERROR]', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
