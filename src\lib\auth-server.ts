import { prisma } from './prisma';
import { UserRole } from './types';
import bcrypt from 'bcryptjs';
import { getServerSession } from 'next-auth/next';
import type { Session } from 'next-auth';
import { authOptions } from './auth-config';
import { NextResponse } from 'next/server';
import { User as PrismaUser } from '@prisma/client';

interface UserContext {
  id: string;
  role: UserRole;
}

export async function canCrewChiefManageShift(user: UserContext, shiftId: string): Promise<boolean> {
  if (user.role === UserRole.Admin || user.role === UserRole.Manager) {
    return true;
  }
  if (user.role !== UserRole.CrewChief) {
    return false;
  }
  const assignment = await prisma.assignedPersonnel.findFirst({
    where: {
      shiftId: shiftId,
      userId: user.id,
      roleCode: 'CC',
    },
  });
  return !!assignment;
}

export async function isCrewChiefAssignedToShift(user: UserContext, shiftId: string): Promise<boolean> {
  if (user.role !== UserRole.CrewChief) {
    return false;
  }
  const assignment = await prisma.assignedPersonnel.findFirst({
    where: {
      shiftId: shiftId,
      userId: user.id,
    },
  });
  return !!assignment;
}

function isBuildTime(): boolean {
  return !!process.env.npm_lifecycle_event?.includes('build');
}

export async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

export async function getUserByEmail(email: string) {
  if (isBuildTime()) {
    return null;
  }
  return prisma.user.findUnique({ where: { email } });
}

export async function createUser(data: any) {
  if (isBuildTime()) {
    return null;
  }
  const { password, ...rest } = data;
  const passwordHash = await hashPassword(password);
  return prisma.user.create({
    data: { ...rest, passwordHash },
  });
}

export async function refreshUserData(userId: string) {
    const { getUserBasic } = await import('./user-queries');
    return getUserBasic(userId);
}

type ApiHandler = (req: Request, context: { params: any }) => Promise<NextResponse>;

type RoleCheck = (role: UserRole) => boolean;

export function withAuthApi(handler: ApiHandler, roleCheck?: RoleCheck) {
  return async (req: Request, context: { params: any }) => {
    const session: Session | null = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (roleCheck && !roleCheck(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return handler(req, context);
  };
}

export async function getAuthenticatedUser(): Promise<PrismaUser | null> {
  try {
    const session: Session | null = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return null;
    }

    const user = await prisma.user.findUnique({
        where: { id: session.user.id },
    });

    return user;
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}

export async function isAdmin(): Promise<boolean> {
  try {
    const session: Session | null = await getServerSession(authOptions);
    return session?.user?.role === UserRole.Admin;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}