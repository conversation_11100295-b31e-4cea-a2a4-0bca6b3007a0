import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { updateWorkerRequirements } from '@/lib/services/worker-requirements';
import { prisma } from '@/lib/prisma';

export async function PUT(
  request: NextRequest,
  { params }: { params: { shiftId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !['Admin', 'CrewChief'].includes(user.role)) {
      return NextResponse.json({ error: 'Admin or Crew Chief access required' }, { status: 403 });
    }

    const { shiftId } = params;
    const body = await request.json();
    const { workerRequirements } = body;

    console.log('PUT worker-requirements received:', { shiftId, body, workerRequirements });

    if (!workerRequirements || !Array.isArray(workerRequirements)) {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    await updateWorkerRequirements(shiftId, workerRequirements);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating worker requirements:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

// GET /api/shifts/[shiftId]/worker-requirements - Get current worker requirements
export async function GET(
  request: NextRequest,
  { params }: { params: { shiftId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { shiftId } = params;

    // Get normalized worker requirements for this shift
    const [shift, reqs] = await Promise.all([
      prisma.shift.findUnique({ where: { id: shiftId }, select: { id: true } }),
      prisma.workerRequirement.findMany({ where: { shiftId }, select: { workerTypeCode: true, requiredCount: true } })
    ]);

    if (!shift) {
      return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
    }

    const workerRequirements = reqs.map(r => ({ roleCode: r.workerTypeCode, requiredCount: r.requiredCount }));

    return NextResponse.json({
      success: true,
      data: {
        shiftId: shiftId,
        workerRequirements
      }
    });

  } catch (error) {
    console.error('Error fetching worker requirements:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}
