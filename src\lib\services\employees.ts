import { prisma } from '../prisma';
import { User as PrismaUser } from '@prisma/client';
import { UserRole } from '@/lib/types';
import { User as NextAuthUser } from 'next-auth';
import { hasAnyRole } from '../role-check';

export async function getAllStageHands(user: NextAuthUser): Promise<Partial<PrismaUser>[]> {
  if (!hasAnyRole(user, [UserRole.Admin])) {
    throw new Error('Not authorized to view all stagehands');
  }
  return prisma.user.findMany({
    where: {
      role: {
        in: [UserRole.CrewChief, UserRole.StageHand],
      },
      isActive: true,
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      companyId: true,
      payrollType: true,
      payrollBaseRateCents: true,
      phone: true,
      location: true,
      certifications: true,
      crew_chief_eligible: true,
      fork_operator_eligible: true,
      OSHA_10_Certifications: true,
      performance: true,
      addressLine1: true,
      addressLine2: true,
      city: true,
      state: true,
      postalCode: true,
      ssnLast4: true,
    },
    orderBy: {
      name: 'asc',
    },
  });
}

export async function getStageHandById(
  user: NextAuthUser,
  id: string
): Promise<Partial<PrismaUser> | null> {
  if (
    !hasAnyRole(user, [UserRole.Admin]) &&
    user.id !== id
  ) {
    throw new Error('Not authorized to view this stagehand');
  }
  return prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      companyId: true,
      certifications: true,
      performance: true,
      location: true,
    },
  });
}

export async function createStageHand(
  user: NextAuthUser,
  data: {
    name: string;
    email: string;
    passwordHash: string;
    role: UserRole;
    companyId?: string;
    certifications?: string[];
    performance?: number;
    location?: string;
  }
): Promise<PrismaUser> {
  if (!hasAnyRole(user, [UserRole.Admin])) {
    throw new Error('Not authorized to create a stagehand');
  }
  return prisma.user.create({
    data,
  });
}

export async function updateStageHand(
  user: NextAuthUser,
  id: string,
  data: Partial<PrismaUser>
): Promise<PrismaUser> {
  if (!hasAnyRole(user, [UserRole.Admin])) {
    throw new Error('Not authorized to update a stagehand');
  }
  return prisma.user.update({
    where: { id },
    data,
  });
}

export async function deleteStageHand(
  user: NextAuthUser,
  id: string
): Promise<PrismaUser> {
  if (!hasAnyRole(user, [UserRole.Admin])) {
    throw new Error('Not authorized to delete a stagehand');
  }
  return prisma.user.delete({
    where: { id },
  });
}

export async function getStageHandsByLocation(
  user: NextAuthUser,
  location: string
): Promise<Partial<PrismaUser>[]> {
  if (!hasAnyRole(user, [UserRole.Admin])) {
    throw new Error('Not authorized to view stagehands by location');
  }
  return prisma.user.findMany({
    where: {
      location,
      isActive: true,
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      companyId: true,
    },
    orderBy: {
      name: 'asc',
    },
  });
}

export async function getStageHandsWithCertifications(
  user: NextAuthUser,
  certifications: string[]
): Promise<Partial<PrismaUser>[]> {
  if (!hasAnyRole(user, [UserRole.Admin])) {
    throw new Error('Not authorized to view stagehands by certification');
  }
  return prisma.user.findMany({
    where: {
      certifications: {
        hasSome: certifications,
      },
      isActive: true,
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      companyId: true,
      certifications: true,
    },
    orderBy: {
      performance: 'desc',
    },
  });
}
