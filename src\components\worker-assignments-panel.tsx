"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import EnhancedWorkerSelector from "@/components/EnhancedWorkerSelector";
import { RoleCode, ShiftWithDetails, Assignment, User } from "@/lib/types";
import { ROLE_DEFINITIONS, ROLE_ORDER } from "@/lib/constants";
import { WORKER_TYPES } from "@/constants/worker-types";

interface Props {
  open: boolean;
  onClose: () => void;
  shift: ShiftWithDetails;
  users: User[];
  onAssign: (assignmentIdOrPlaceholder: string, userId: string | null, roleCode: RoleCode) => Promise<void>;
  isLoading?: boolean;
}

export default function WorkerAssignmentsPanel({ open, onClose, shift, users, onAssign, isLoading }: Props) {
  // Build requirements from normalized workerRequirements first; fall back to legacy numeric fields
  const requirements = (() => {
    const reqs = Array.isArray((shift as any).workerRequirements) ? (shift as any).workerRequirements : [];
    if (reqs.length > 0) {
      const byCode = reqs.reduce((acc: Record<RoleCode, number>, r: any) => {
        const code = (r.roleCode || r.workerTypeCode) as RoleCode | undefined;
        if (!code) return acc;
        acc[code] = (acc[code] || 0) + (Number(r.requiredCount) || 0);
        return acc;
      }, {} as Record<RoleCode, number>);
      return (Object.keys(byCode) as RoleCode[]).map(code => ({ roleCode: code, count: byCode[code] })).filter(r => r.count > 0);
    }
    return [
      { roleCode: "CC" as RoleCode, count: (shift as any).requiredCrewChiefs || 0 },
      { roleCode: "RG" as RoleCode, count: (shift as any).requiredRiggers || 0 },
      { roleCode: "RFO" as RoleCode, count: (shift as any).requiredReachForkOperators || 0 },
      { roleCode: "FO" as RoleCode, count: (shift as any).requiredForkOperators || 0 },
      { roleCode: "SH" as RoleCode, count: (shift as any).requiredStagehands || 0 },
    ].filter(r => r.count > 0);
  })();

  const assignmentsByRole = new Map<RoleCode, Assignment[]>();
  shift.assignedPersonnel.forEach(a => {
    const r = a.roleCode as RoleCode;
    if (!assignmentsByRole.has(r)) assignmentsByRole.set(r, []);
    assignmentsByRole.get(r)!.push(a);
  });

  const makeSlots = () => {
    const slots: Array<{ id: string; roleCode: RoleCode; assignment?: Assignment }> = [];
    requirements.forEach(({ roleCode, count }) => {
      const assigned = (assignmentsByRole.get(roleCode) || []).slice();
      for (let i = 0; i < count; i++) {
        const existing = assigned.shift();
        slots.push({ id: existing?.id ?? `placeholder-${roleCode}-${i}`, roleCode, assignment: existing });
      }
      (assignmentsByRole.get(roleCode) || []).slice(count).forEach(extra => {
        slots.push({ id: extra.id, roleCode, assignment: extra });
      });
    });
    return slots;
  };

  const slots = makeSlots();
  const assignedIds = new Set(shift.assignedPersonnel.filter(a => a.userId).map(a => a.userId!));

  return (
    <Dialog open={open} onOpenChange={(o) => !o && onClose()}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Worker Assignments</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {ROLE_ORDER.filter(r => (WORKER_TYPES as any)[r as RoleCode]).map((role) => {
            const roleSlots = slots.filter(s => s.roleCode === role);
            if (roleSlots.length === 0) return null;
            const roleDef = ROLE_DEFINITIONS[role as RoleCode];

            return (
              <div key={role} className="rounded-md border p-3">
                <div className="mb-2">
                  <Badge className={roleDef.badgeClasses}>{roleDef.name}</Badge>
                </div>
                <div className="space-y-2">
                  {roleSlots.map((slot) => {
                    const selectedId = slot.assignment?.userId ?? null;
                    const filteredUsers = users.filter(u => (selectedId ? true : !assignedIds.has(u.id)));
                    return (
                      <div key={slot.id} className="flex gap-3 items-center">
                        <EnhancedWorkerSelector
                          users={filteredUsers}
                          selectedUserId={selectedId}
                          requiredRole={slot.roleCode}
                          onChange={(userId) => onAssign(slot.id, userId, slot.roleCode)}
                          disabled={isLoading}
                        />
                        {!selectedId && <Badge variant="outline">Empty Slot</Badge>}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </DialogContent>
    </Dialog>
  );
}