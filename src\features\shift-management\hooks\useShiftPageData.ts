import { useShift, useUsers, useApiMutation } from '@/hooks/use-api';
import { apiService } from '@/lib/services/api';
import { useQueryClient } from '@tanstack/react-query';
import { RoleCode, ShiftWithDetails, Assignment, User } from '@/lib/types';
import { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createSmartCacheKey } from '@/lib/query-config';

// Helper function to generate placeholder assignments based on worker requirements
function generateAssignmentsWithPlaceholders(shift: any) {
  if (!shift) return [];

  const existingAssignments = shift.assignedPersonnel || [];
  const assignments = [...existingAssignments];

  // 1) De-duplicate by userId so the same worker doesn't appear in multiple slots
  //    Keep the first occurrence, convert subsequent ones into placeholders for UI
  const seenUserIds = new Set<string>();
  for (let i = 0; i < assignments.length; i++) {
    const a = assignments[i];
    const uid = a?.userId as string | null;
    if (uid && a.status !== 'NoShow') {
      if (seenUserIds.has(uid)) {
        // Convert duplicate to a placeholder slot
        assignments[i] = {
          ...a,
          userId: null,
          user: null,
          status: 'Assigned',
          isPlaceholder: true,
        };
      } else {
        seenUserIds.add(uid);
      }
    }
  }

  // Define role requirements mapping
  const roleRequirements = (() => {
    const byCode: Record<string, number> = {};
    const reqs = Array.isArray(shift?.workerRequirements) ? shift.workerRequirements : [];
    reqs.forEach((r: any) => {
      const code = r.roleCode || r.workerTypeCode;
      if (!code) return;
      byCode[code] = (byCode[code] || 0) + (Number(r.requiredCount) || 0);
    });
    // Ensure at least 1 Crew Chief by default if not specified
    if ((byCode['CC'] ?? 0) < 1) byCode['CC'] = 1;
    return [
      { roleCode: 'CC' as RoleCode, required: byCode['CC'] || 0 },
      { roleCode: 'SH' as RoleCode, required: byCode['SH'] || 0 },
      { roleCode: 'FO' as RoleCode, required: byCode['FO'] || 0 },
      { roleCode: 'RFO' as RoleCode, required: byCode['RFO'] || 0 },
      { roleCode: 'RG' as RoleCode, required: byCode['RG'] || 0 },
      { roleCode: 'GL' as RoleCode, required: byCode['GL'] || 0 },
      { roleCode: 'SUP' as RoleCode, required: byCode['SUP'] || 0 },
    ];
  })();

  // For each role, ensure we show exactly the required number of slots
  // 1) Cap existing real assignments at 'required'
  // 2) Add placeholders to reach 'required' when fewer exist
  // 3) Do not exceed 'required' in the UI regardless of DB count
  const finalAssignments: any[] = [];

  roleRequirements.forEach(({ roleCode, required }) => {
    const existingForRole = assignments.filter(a => a.roleCode === roleCode && a.status !== 'NoShow');

    // Keep only up to 'required' existing items
    const kept = existingForRole.slice(0, Math.max(required, 0));
    finalAssignments.push(...kept);

    const needed = Math.max(required - kept.length, 0);

    // Create placeholder assignments for unfilled positions
    for (let i = 0; i < needed; i++) {
      finalAssignments.push({
        id: `placeholder-${roleCode}-${i + kept.length}`,
        shiftId: shift.id,
        userId: null,
        user: null,
        roleCode,
        status: 'Assigned',
        timeEntries: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isPlaceholder: true,
      });
    }
  });

  return finalAssignments;
}

export const useShiftPageData = (shiftId: string | null) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const [isUpdatingAssignment, setIsUpdatingAssignment] = useState<Record<string, boolean>>({});

  const {
    data: shift,
    isLoading: isShiftLoading,
    error: shiftError,
    isFetching: isShiftFetching,
    refetch,
  } = useShift(shiftId, {
    staleTime: 1000 * 20,
    enabled: !!shiftId, // Only fetch if shiftId is provided
    throwOnError: false, // Prevent throwing on 404
  });

  const { data: usersResponse } = useUsers({ fetchAll: true });
  const users = useMemo(() => usersResponse?.users ?? [], [usersResponse]);

  const assignments = useMemo(() => {
    return generateAssignmentsWithPlaceholders(shift);
  }, [shift]);

  const invalidateShiftQueries = () => {
    if (!shiftId) return;
    // Invalidate using both legacy and smart keys to ensure all dependent views refresh
    queryClient.invalidateQueries({ queryKey: createSmartCacheKey('shift', { id: shiftId }) });
    queryClient.invalidateQueries({ queryKey: ['shift', shiftId] });
    queryClient.invalidateQueries({ queryKey: ['shifts'] });
    queryClient.invalidateQueries({ queryKey: ['shift-assignments', shiftId] });
    queryClient.invalidateQueries({ queryKey: ['timesheets'] });
  };

  const assignWorker = useApiMutation<void, { userId: string, roleCode: string, ignoreConflicts?: boolean, assignmentId?: string }>(
    ({ userId, roleCode, ignoreConflicts = false, assignmentId }) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.assignWorker(shiftId, userId, roleCode, ignoreConflicts, assignmentId);
    },
    {
      optimisticUpdater: {
        queryKey: createSmartCacheKey('shift', { id: shiftId }),
        updateFn: (oldShift: ShiftWithDetails, { userId, roleCode, assignmentId }) => {
          const user = users.find(u => u.id === userId);
          const base: Partial<Assignment> = {
            userId,
            roleCode,
            status: 'Assigned',
            user: user as User,
            isPlaceholder: false,
          };

          // If assigning into a placeholder slot, append a new assignment to the list
          if (assignmentId && assignmentId.startsWith('placeholder-')) {
            const newAssignment: Assignment = {
              id: `temp-${Math.random()}`,
              shiftId: shiftId,
              timeEntries: [],
              createdAt: new Date(),
              updatedAt: new Date(),
              ...base,
            } as Assignment;
            return {
              ...oldShift,
              assignedPersonnel: [...oldShift.assignedPersonnel, newAssignment],
            };
          }

          // If an existing DB assignment id is provided, update it
          if (assignmentId) {
            return {
              ...oldShift,
              assignedPersonnel: oldShift.assignedPersonnel.map(a =>
                a.id === assignmentId ? { ...a, ...base } : a
              ),
            };
          }

          // No assignmentId provided: append new
          const newAssignment: Assignment = {
            id: `temp-${Math.random()}`,
            shiftId: shiftId,
            timeEntries: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            ...base,
          } as Assignment;
          return {
            ...oldShift,
            assignedPersonnel: [...oldShift.assignedPersonnel, newAssignment],
          };
        },
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const unassignWorker = useApiMutation<void, string>(
    (assignmentId) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.unassignWorker(shiftId, assignmentId);
    },
    {
      optimisticUpdater: {
        queryKey: createSmartCacheKey('shift', { id: shiftId }),
        updateFn: (oldShift: ShiftWithDetails, assignmentId) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a =>
            a.id === assignmentId ? { ...a, userId: null, user: null, isPlaceholder: true } : a
          ),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const markNoShow = useApiMutation<void, string>(
    (assignmentId) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.markNoShow(shiftId, assignmentId);
    },
    {
      optimisticUpdater: {
        queryKey: createSmartCacheKey('shift', { id: shiftId }),
        updateFn: (oldShift: ShiftWithDetails, assignmentId) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a => 
            a.id === assignmentId ? { ...a, status: 'NoShow' } : a
          ),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  // ... other mutations (clockIn, clockOut, etc.) can be refactored similarly
  // For now, they will use the basic invalidation

  const clockIn = useApiMutation<void, string>(
    (assignmentId) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.clockIn(shiftId, assignmentId);
    },
    {
      optimisticUpdater: {
        queryKey: createSmartCacheKey('shift', { id: shiftId }),
        updateFn: (oldShift: ShiftWithDetails, assignmentId) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a => {
            if (a.id !== assignmentId) return a;
            const nextEntryNumber = Math.min(
              (a.timeEntries?.reduce((max, e) => Math.max(max, e.entryNumber || 1), 0) || 0) + 1,
              3
            );
            const newEntry = {
              id: `temp-time-${Math.random()}`,
              assignedPersonnelId: a.id,
              entryNumber: nextEntryNumber,
              clockIn: new Date(),
              clockOut: null,
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date(),
            } as any;
            return {
              ...a,
              status: 'ClockedIn',
              timeEntries: [...(a.timeEntries || []), newEntry],
            };
          }),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const clockOut = useApiMutation<void, string>(
    (assignmentId) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.clockOut(shiftId, assignmentId);
    },
    {
      optimisticUpdater: {
        queryKey: createSmartCacheKey('shift', { id: shiftId }),
        updateFn: (oldShift: ShiftWithDetails, assignmentId) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a => {
            if (a.id !== assignmentId) return a;
            const timeEntries = [...(a.timeEntries || [])];
            // Find last active entry (no clockOut)
            for (let i = timeEntries.length - 1; i >= 0; i--) {
              if (!timeEntries[i].clockOut) {
                timeEntries[i] = {
                  ...timeEntries[i],
                  clockOut: new Date(),
                  isActive: false,
                } as any;
                break;
              }
            }
            return { ...a, status: 'ClockedOut', timeEntries };
          }),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const endWorkerShift = useApiMutation<void, string>(
    (assignmentId) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.endWorkerShift(shiftId, assignmentId);
    },
    {
      optimisticUpdater: {
        queryKey: createSmartCacheKey('shift', { id: shiftId }),
        updateFn: (oldShift: ShiftWithDetails, assignmentId) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a => {
            if (a.id !== assignmentId) return a;
            // Clock out any active time entries
            const timeEntries = [...(a.timeEntries || [])];
            for (let i = timeEntries.length - 1; i >= 0; i--) {
              if (!timeEntries[i].clockOut) {
                timeEntries[i] = {
                  ...timeEntries[i],
                  clockOut: new Date(),
                  isActive: false,
                } as any;
                break;
              }
            }
            return { ...a, status: 'ShiftEnded', timeEntries };
          }),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const endShift = useApiMutation<void, void>(
    () => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.endShift(shiftId);
    },
    {
      optimisticUpdater: {
        queryKey: ['shift', shiftId],
        updateFn: (oldShift: ShiftWithDetails) => ({
          ...oldShift,
          status: 'Completed',
          endTime: new Date().toISOString(),
          assignedPersonnel: oldShift.assignedPersonnel.map(a => {
            const lastTimeEntry = a.timeEntries.findLast(entry => entry.type === 'clock_in' && entry.clockOut === null);
            if (lastTimeEntry) {
              return {
                ...a,
                timeEntries: a.timeEntries.map(entry =>
                  entry.id === lastTimeEntry.id ? { ...entry, clockOut: new Date().toISOString() } : entry
                ),
              };
            }
            return a;
          }),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const masterStartBreak = useApiMutation<void, void>(
    () => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.masterStartBreak(shiftId);
    },
    {
      optimisticUpdater: {
        queryKey: ['shift', shiftId],
        updateFn: (oldShift: ShiftWithDetails) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a => ({
            ...a,
            timeEntries: [...a.timeEntries, {
              id: `temp-break-start-${Math.random()}`,
              assignmentId: a.id,
              type: 'break_start',
              clockIn: new Date().toISOString(),
              clockOut: null,
              createdAt: new Date(),
              updatedAt: new Date(),
            }],
          })),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const masterEndShift = useApiMutation<void, void>(
    () => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.masterEndShift(shiftId);
    },
    {
      optimisticUpdater: {
        queryKey: ['shift', shiftId],
        updateFn: (oldShift: ShiftWithDetails) => ({
          ...oldShift,
          assignedPersonnel: oldShift.assignedPersonnel.map(a => {
            const lastBreak = a.timeEntries.findLast(entry => entry.type === 'break_start' && entry.clockOut === null);
            if (lastBreak) {
              return {
                ...a,
                timeEntries: a.timeEntries.map(entry =>
                  entry.id === lastBreak.id ? { ...entry, clockOut: new Date().toISOString() } : entry
                ),
              };
            }
            return a;
          }),
        }),
      },
      onSettled: invalidateShiftQueries,
    }
  );

  const finalizeTimesheet = useApiMutation<{timesheetId: string}, void>(
    () => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.finalizeTimesheet(shiftId);
    },
    {
        onSuccess: (data) => {
            if (data.timesheetId) {
                router.push(`/timesheets/${data.timesheetId}/review`);
            }
            invalidateShiftQueries();
        },
        onError: () => {
            invalidateShiftQueries();
        }
    }
  );

  // Notes update mutation
  const updateNotes = useApiMutation<void, string>(
    (newNotes) => {
      if (!shiftId) throw new Error("Shift ID is not available");
      return apiService.updateShiftNotes(shiftId, newNotes);
    },
    {
      onSuccess: () => {
        invalidateShiftQueries();
      },
    }
  );

  return {
    shift,
    isShiftLoading,
    shiftError,
    assignments,
    users,
    assignWorker,
    unassignWorker,
    clockIn,
    clockOut,
    endShift,
    endWorkerShift,
    markNoShow,
    masterStartBreak,
    masterEndShift,
    finalizeTimesheet,
    refetch,
    isUpdatingAssignment,
    setIsUpdatingAssignment,
    updateNotes,
  };
};
