import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

export async function POST(
  req: Request,
  { params }: { params: { shiftId: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || !['Admin', 'Manager'].includes(session.user.role)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  const { shiftId } = params;
  const { workerId, timeEntryId, newTime, type } = await req.json();

  if (!workerId || !timeEntryId || !newTime || !type) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
  }

  try {
    const timeEntry = await prisma.timeEntry.findUnique({
      where: { id: timeEntryId },
    });

    if (!timeEntry) {
      return NextResponse.json({ error: 'Time entry not found' }, { status: 404 });
    }

    const updateData = {
      [type]: new Date(newTime),
    };

    await prisma.timeEntry.update({
      where: { id: timeEntryId },
      data: updateData,
    });

    return NextResponse.json({ message: 'Time entry updated successfully' });
  } catch (error) {
    console.error('Failed to update time entry:', error);
    return NextResponse.json({ error: 'Failed to update time entry' }, { status: 500 });
  }
}
