import { withPageAuthRequired } from "@/lib/auth/with-page-auth-required";
import { AuthenticatedUser, UserRole } from "@/lib/types";
import ShiftDetailsClient from "./Client";

function ServerPage({ user }: { user: AuthenticatedUser }) {
  return <ShiftDetailsClient user={user} />;
}

export default withPageAuthRequired(ServerPage, {
  allowedRoles: [
    UserRole.Admin,
    UserRole.CrewChief,
    UserRole.StageHand,
    UserRole.CompanyUser,
    UserRole.Manager,
  ],
});
