import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';
import { PDFGenerationOptions } from './unified-pdf-generator';

export interface CacheEntry {
  key: string;
  timesheetId: string;
  options: PDFGenerationOptions;
  pdfUrl: string;
  size: number;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  expiresAt: Date;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  oldestEntry: Date | null;
  newestEntry: Date | null;
  mostAccessed: CacheEntry | null;
}

/**
 * PDF Cache Manager for improved performance
 */
export class PDFCacheManager {
  private static instance: PDFCacheManager;
  private cache = new Map<string, CacheEntry>();
  private cacheDir: string;
  private maxCacheSize = 100 * 1024 * 1024; // 100MB
  private maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 7 days
  private hitCount = 0;
  private missCount = 0;

  constructor() {
    this.cacheDir = path.join(process.cwd(), '.pdf-cache');
    this.ensureCacheDirectory();
    this.loadCacheIndex();
    this.startCleanupTimer();
  }

  static getInstance(): PDFCacheManager {
    if (!PDFCacheManager.instance) {
      PDFCacheManager.instance = new PDFCacheManager();
    }
    return PDFCacheManager.instance;
  }

  /**
   * Generate cache key for timesheet and options
   */
  generateCacheKey(timesheetId: string, options: PDFGenerationOptions): string {
    const optionsString = JSON.stringify({
      includeSignature: options.includeSignature,
      signatureType: options.signatureType,
      templateId: options.templateId,
      format: options.format,
      orientation: options.orientation,
      quality: options.quality,
      watermark: options.watermark,
      customFields: options.customFields
    });
    
    return createHash('sha256')
      .update(`${timesheetId}:${optionsString}`)
      .digest('hex');
  }

  /**
   * Check if PDF exists in cache
   */
  async has(timesheetId: string, options: PDFGenerationOptions): Promise<boolean> {
    const key = this.generateCacheKey(timesheetId, options);
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    // Check if entry has expired
    if (entry.expiresAt < new Date()) {
      await this.remove(key);
      return false;
    }

    // Check if file still exists
    const filePath = path.join(this.cacheDir, `${key}.pdf`);
    try {
      await fs.access(filePath);
      return true;
    } catch {
      await this.remove(key);
      return false;
    }
  }

  /**
   * Get PDF from cache
   */
  async get(timesheetId: string, options: PDFGenerationOptions): Promise<string | null> {
    const key = this.generateCacheKey(timesheetId, options);
    const entry = this.cache.get(key);
    
    if (!entry || entry.expiresAt < new Date()) {
      this.missCount++;
      return null;
    }

    const filePath = path.join(this.cacheDir, `${key}.pdf`);
    try {
      await fs.access(filePath);
      
      // Update access statistics
      entry.lastAccessed = new Date();
      entry.accessCount++;
      this.hitCount++;
      
      return entry.pdfUrl;
    } catch {
      await this.remove(key);
      this.missCount++;
      return null;
    }
  }

  /**
   * Get PDF buffer from cache
   */
  async getBuffer(timesheetId: string, options: PDFGenerationOptions): Promise<Buffer | null> {
    const key = this.generateCacheKey(timesheetId, options);
    const entry = this.cache.get(key);
    
    if (!entry || entry.expiresAt < new Date()) {
      this.missCount++;
      return null;
    }

    const filePath = path.join(this.cacheDir, `${key}.pdf`);
    try {
      const buffer = await fs.readFile(filePath);
      
      // Update access statistics
      entry.lastAccessed = new Date();
      entry.accessCount++;
      this.hitCount++;
      
      return buffer;
    } catch {
      await this.remove(key);
      this.missCount++;
      return null;
    }
  }

  /**
   * Store PDF in cache
   */
  async set(
    timesheetId: string, 
    options: PDFGenerationOptions, 
    pdfBuffer: Buffer, 
    pdfUrl: string
  ): Promise<void> {
    const key = this.generateCacheKey(timesheetId, options);
    const filePath = path.join(this.cacheDir, `${key}.pdf`);
    
    try {
      // Write PDF to cache directory
      await fs.writeFile(filePath, pdfBuffer);
      
      // Create cache entry
      const entry: CacheEntry = {
        key,
        timesheetId,
        options: { ...options },
        pdfUrl,
        size: pdfBuffer.length,
        createdAt: new Date(),
        lastAccessed: new Date(),
        accessCount: 1,
        expiresAt: new Date(Date.now() + this.maxCacheAge)
      };
      
      this.cache.set(key, entry);
      
      // Check cache size and cleanup if necessary
      await this.enforceMaxCacheSize();
      
      console.log(`PDF cached: ${key} (${(pdfBuffer.length / 1024).toFixed(2)} KB)`);
    } catch (error) {
      console.error('Failed to cache PDF:', error);
    }
  }

  /**
   * Remove entry from cache
   */
  async remove(key: string): Promise<void> {
    const entry = this.cache.get(key);
    if (entry) {
      const filePath = path.join(this.cacheDir, `${key}.pdf`);
      try {
        await fs.unlink(filePath);
      } catch {
        // File might not exist, ignore error
      }
      this.cache.delete(key);
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    for (const key of this.cache.keys()) {
      await this.remove(key);
    }
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    const totalRequests = this.hitCount + this.missCount;
    
    let oldestEntry: Date | null = null;
    let newestEntry: Date | null = null;
    let mostAccessed: CacheEntry | null = null;
    
    if (entries.length > 0) {
      oldestEntry = entries.reduce((oldest, entry) => 
        entry.createdAt < oldest ? entry.createdAt : oldest, entries[0].createdAt);
      
      newestEntry = entries.reduce((newest, entry) => 
        entry.createdAt > newest ? entry.createdAt : newest, entries[0].createdAt);
      
      mostAccessed = entries.reduce((most: CacheEntry | null, entry) => 
        entry.accessCount > (most?.accessCount || 0) ? entry : most, null);
    }

    return {
      totalEntries: entries.length,
      totalSize,
      hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0,
      oldestEntry,
      newestEntry,
      mostAccessed
    };
  }

  /**
   * Get cache entries for specific timesheet
   */
  getTimesheetEntries(timesheetId: string): CacheEntry[] {
    return Array.from(this.cache.values()).filter(entry => entry.timesheetId === timesheetId);
  }

  /**
   * Invalidate cache entries for specific timesheet
   */
  async invalidateTimesheet(timesheetId: string): Promise<void> {
    const entries = this.getTimesheetEntries(timesheetId);
    for (const entry of entries) {
      await this.remove(entry.key);
    }
    console.log(`Invalidated ${entries.length} cache entries for timesheet ${timesheetId}`);
  }

  /**
   * Cleanup expired entries
   */
  async cleanup(): Promise<void> {
    const now = new Date();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }
    
    for (const key of expiredKeys) {
      await this.remove(key);
    }
    
    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * Preload cache with commonly requested PDFs
   */
  async preloadCache(timesheetIds: string[], options: PDFGenerationOptions): Promise<void> {
    console.log(`Preloading cache for ${timesheetIds.length} timesheets`);
    
    // This would be implemented to generate PDFs in background
    // For now, just log the intent
    for (const timesheetId of timesheetIds) {
      const key = this.generateCacheKey(timesheetId, options);
      console.log(`Would preload: ${key}`);
    }
  }

  /**
   * Export cache statistics
   */
  exportStats(): string {
    const stats = this.getStats();
    const entries = Array.from(this.cache.values());
    
    const report = {
      summary: stats,
      entries: entries.map(entry => ({
        key: entry.key,
        timesheetId: entry.timesheetId,
        size: entry.size,
        accessCount: entry.accessCount,
        createdAt: entry.createdAt,
        lastAccessed: entry.lastAccessed,
        expiresAt: entry.expiresAt
      }))
    };
    
    return JSON.stringify(report, null, 2);
  }

  /**
   * Ensure cache directory exists
   */
  private async ensureCacheDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create cache directory:', error);
    }
  }

  /**
   * Load cache index from disk
   */
  private async loadCacheIndex(): Promise<void> {
    const indexPath = path.join(this.cacheDir, 'index.json');
    try {
      const indexData = await fs.readFile(indexPath, 'utf-8');
      const index = JSON.parse(indexData);
      
      for (const entryData of index.entries || []) {
        const entry: CacheEntry = {
          ...entryData,
          createdAt: new Date(entryData.createdAt),
          lastAccessed: new Date(entryData.lastAccessed),
          expiresAt: new Date(entryData.expiresAt)
        };
        this.cache.set(entry.key, entry);
      }
      
      this.hitCount = index.hitCount || 0;
      this.missCount = index.missCount || 0;
      
      console.log(`Loaded ${this.cache.size} cache entries from index`);
    } catch {
      // Index file doesn't exist or is corrupted, start fresh
      console.log('Starting with empty cache');
    }
  }

  /**
   * Save cache index to disk
   */
  private async saveCacheIndex(): Promise<void> {
    const indexPath = path.join(this.cacheDir, 'index.json');
    const entries = Array.from(this.cache.values());
    
    const index = {
      entries,
      hitCount: this.hitCount,
      missCount: this.missCount,
      lastSaved: new Date()
    };
    
    try {
      await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
    } catch (error) {
      console.error('Failed to save cache index:', error);
    }
  }

  /**
   * Enforce maximum cache size
   */
  private async enforceMaxCacheSize(): Promise<void> {
    const stats = this.getStats();
    
    if (stats.totalSize <= this.maxCacheSize) {
      return;
    }
    
    // Sort entries by last accessed time (oldest first)
    const entries = Array.from(this.cache.values())
      .sort((a, b) => a.lastAccessed.getTime() - b.lastAccessed.getTime());
    
    let removedSize = 0;
    let removedCount = 0;
    
    for (const entry of entries) {
      if (stats.totalSize - removedSize <= this.maxCacheSize) {
        break;
      }
      
      await this.remove(entry.key);
      removedSize += entry.size;
      removedCount++;
    }
    
    if (removedCount > 0) {
      console.log(`Removed ${removedCount} cache entries (${(removedSize / 1024 / 1024).toFixed(2)} MB) to enforce size limit`);
    }
  }

  /**
   * Start periodic cleanup timer
   */
  private startCleanupTimer(): void {
    // Cleanup every hour
    setInterval(async () => {
      await this.cleanup();
      await this.saveCacheIndex();
    }, 60 * 60 * 1000);
    
    // Save index every 5 minutes
    setInterval(async () => {
      await this.saveCacheIndex();
    }, 5 * 60 * 1000);
  }
}

// Export singleton instance
export const pdfCacheManager = PDFCacheManager.getInstance();