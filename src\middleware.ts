// src/middleware.ts
import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  async function middleware(req) {
    const { nextUrl, nextauth } = req;
    const token = nextauth.token;
    const pathname = nextUrl.pathname;

    // Unauthenticated users → redirect to login
    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    // Redirect authenticated users from root to their dashboard
    if (pathname === "/") {
      return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    // Role-based restrictions
    if (pathname.startsWith("/admin") && token.role !== "Admin") {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/manager") && !["Admin", "Manager"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/crew-chief") && !["Admin", "Manager", "CrewChief"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/stagehand") && !["Admin", "Manager", "CrewChief", "StageHand"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/employee") && !["Admin", "Manager", "CrewChief", "StageHand"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/stagehands") && !["Admin", "Manager", "CrewChief"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/companies") && !["Admin", "Manager"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    if (pathname.startsWith("/timesheets") && !["Admin", "Manager", "CrewChief", "StageHand", "CompanyUser"].includes(token.role)) {
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token, // Only require a valid token
    },
  }
);

export const config = {
  matcher: [
    // Protected routes that require authentication
    "/admin/:path*",
    "/manager/:path*",
    "/crew-chief/:path*",
    "/stagehand/:path*",
    "/employee/:path*",
    "/stagehands/:path*",
    "/companies/:path*",
    "/timesheets/:path*",
    "/jobs/:path*",
    "/shifts/:path*",
    "/dashboard/:path*",
    // Root path to handle authenticated user redirects
    "/",
  ],
};
