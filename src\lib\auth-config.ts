import type { NextAuthOptions, SessionStrategy } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import bcrypt from 'bcryptjs';

export const AUTH_ENABLED = true; // Set to false to disable authentication

// authOptions is required by next-auth, even if AUTH_ENABLED is false.
// It should be exported to satisfy the import in the route handler.
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  secret: process.env.NEXTAUTH_SECRET || "uivbienbuivenbivrenbiiinbrenbirenbiivrevnurnei", // Provide a default secret for development
  session: {
    strategy: "jwt" as SessionStrategy,
  },
  providers: [
    // Google OAuth provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
          scope: 'openid email profile'
        }
      },
    }),

    // Credentials provider for email/password
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        });

        if (user && user.passwordHash && await bcrypt.compare(credentials.password, user.passwordHash)) {
          return user;
        }
        return null;
      }
    })
  ],
  debug: process.env.NODE_ENV === 'development', // Add this line for debugging and type re-evaluation
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // When a user is present (initial sign-in), return a fresh token built from the user to avoid leaking prior session fields
      if (user) {
        const accessToken = account?.access_token || `${(user as any).id}-${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;
        return {
          id: (user as any).id,
          role: (user as any).role,
          companyId: (user as any).companyId ?? null,
          avatarUrl: (user as any).avatarUrl || (user as any).image || null,
          name: user.name as string | undefined,
          email: user.email as string | undefined,
          upForGrabsNotifications: (user as any).upForGrabsNotifications,
          accessToken,
          provider: account?.provider,
          refreshToken: account?.refresh_token,
        } as any;
      }
      // When linking/refreshing provider tokens during an active session, merge provider token fields
      if (account) {
        return {
          ...(token as any),
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
          provider: account.provider,
        } as any;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        (session.user as any).id = (token as any).id;
        (session.user as any).role = (token as any).role;
        (session.user as any).companyId = (token as any).companyId;
        (session.user as any).avatarUrl = (token as any).avatarUrl;
        session.user.name = (token as any).name;
        session.user.email = (token as any).email;
        (session.user as any).upForGrabsNotifications = (token as any).upForGrabsNotifications;
        (session as any).accessToken = (token as any).accessToken;
      }
      return session;
    },
  },
  // Add other necessary configurations if needed, or keep it minimal
};