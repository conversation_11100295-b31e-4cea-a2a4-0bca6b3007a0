import type { NextAuthOptions, SessionStrategy } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import bcrypt from 'bcryptjs';

export const AUTH_ENABLED = true; // Set to false to disable authentication

// Validate critical environment variables
if (!process.env.NEXTAUTH_SECRET) {
  throw new Error('NEXTAUTH_SECRET environment variable is required for secure authentication');
}

// authOptions is required by next-auth, even if AUTH_ENABLED is false.
// It should be exported to satisfy the import in the route handler.
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  secret: process.env.NEXTAUTH_SECRET, // NEVER use a default secret - this causes security issues
  session: {
    strategy: "jwt" as SessionStrategy,
  },
  providers: [
    // Google OAuth provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
          scope: 'openid email profile'
        }
      },
    }),

    // Credentials provider for email/password
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        });

        if (user && user.passwordHash && await bcrypt.compare(credentials.password, user.passwordHash)) {
          console.log(`✅ Authentication successful for user: ${user.email} (ID: ${user.id})`);
          return user;
        }
        console.log(`❌ Authentication failed for email: ${credentials.email}`);
        return null;
      }
    })
  ],
  debug: process.env.NODE_ENV === 'development', // Add this line for debugging and type re-evaluation
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // When a user is present (initial sign-in), return a fresh token built from the user to avoid leaking prior session fields
      if (user) {
        // Generate secure access token using crypto
        const crypto = require('crypto');
        const accessToken = account?.access_token || crypto.randomBytes(32).toString('hex');

        console.log(`🔑 Creating JWT token for user: ${user.email} (ID: ${(user as any).id})`);

        return {
          id: (user as any).id,
          role: (user as any).role,
          companyId: (user as any).companyId ?? null,
          avatarUrl: (user as any).avatarUrl || (user as any).image || null,
          name: user.name as string | undefined,
          email: user.email as string | undefined,
          upForGrabsNotifications: (user as any).upForGrabsNotifications,
          accessToken,
          provider: account?.provider,
          refreshToken: account?.refresh_token,
          iat: Math.floor(Date.now() / 1000), // Add issued at time
          exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // Add expiration (24 hours)
        } as any;
      }
      // When linking/refreshing provider tokens during an active session, merge provider token fields
      if (account) {
        return {
          ...(token as any),
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
          provider: account.provider,
        } as any;
      }

      // Validate token hasn't expired
      if ((token as any).exp && Date.now() / 1000 > (token as any).exp) {
        console.warn('Token expired, forcing re-authentication');
        return null; // Force re-authentication
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        // Validate token integrity
        if (!(token as any).id || !(token as any).email) {
          console.error('❌ Invalid token structure, missing required fields');
          // Return empty session instead of null to avoid type issues
          return { ...session, user: { ...session.user, id: '', email: '', name: '' } };
        }

        console.log(`📋 Creating session for user: ${(token as any).email} (ID: ${(token as any).id})`);

        (session.user as any).id = (token as any).id;
        (session.user as any).role = (token as any).role;
        (session.user as any).companyId = (token as any).companyId;
        (session.user as any).avatarUrl = (token as any).avatarUrl;
        session.user.name = (token as any).name;
        session.user.email = (token as any).email;
        (session.user as any).upForGrabsNotifications = (token as any).upForGrabsNotifications;
        (session as any).accessToken = (token as any).accessToken;
      }
      return session;
    },
  },
  // Add other necessary configurations if needed, or keep it minimal
};