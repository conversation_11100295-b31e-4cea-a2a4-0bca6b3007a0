import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import { mkdir, writeFile } from 'fs/promises';
import { PDFDocument } from 'pdf-lib';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json({ error: 'Expected multipart/form-data' }, { status: 400 });
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const templateType = formData.get('templateType') as string || 'overlay';

    if (!(file instanceof File)) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json({ error: 'Only PDF files are allowed' }, { status: 400 });
    }

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const dir = path.join(process.cwd(), 'pdf-templates');
    await mkdir(dir, { recursive: true });

    const filename = templateType === 'form' ? 'timesheet-form-template.pdf' : 'uploaded-template.pdf';
    const finalPath = path.join(dir, filename);
    await writeFile(finalPath, buffer);

    // Extract form fields
    const pdfDoc = await PDFDocument.load(buffer);
    const form = pdfDoc.getForm();
    const fields = form.getFields();
    const formFields = fields.map(field => {
      const widgets = field.acroField.getWidgets();
      const firstWidget = widgets[0];
      const { x, y, width, height } = firstWidget.getRectangle();
      return {
        name: field.getName(),
        type: field.constructor.name,
        x,
        y,
        width,
        height,
      };
    });

    return NextResponse.json({
      success: true,
      path: '/api/admin/pdf-config/template',
      formFields,
    });
  } catch (err: any) {
    console.error('Template upload failed:', err);
    return NextResponse.json({ error: 'Upload failed', details: err?.message }, { status: 500 });
  }
}

