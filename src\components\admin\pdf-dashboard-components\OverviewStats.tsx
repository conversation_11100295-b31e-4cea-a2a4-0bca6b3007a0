'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { FileText, Clock, Database } from 'lucide-react';
import { PDFStats } from '@/types/pdf-stats';
import { getHealthColor, getHealthIcon } from '@/lib/pdf-management-helpers';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  children?: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, children }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {children}
    </CardContent>
  </Card>
);

interface OverviewStatsProps {
  overview: PDFStats['overview'];
  systemHealth: PDFStats['systemHealth'];
}

export const OverviewStats: React.FC<OverviewStatsProps> = ({ overview, systemHealth }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title="Total Generations"
        value={overview.totalGenerations.toLocaleString()}
        icon={<FileText className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Avg Generation Time"
        value={`${overview.averageGenerationTime}ms`}
        icon={<Clock className="h-4 w-4 text-muted-foreground" />}
      />
      <StatCard
        title="Cache Hit Rate"
        value={`${overview.cacheHitRate.toFixed(1)}%`}
        icon={<Database className="h-4 w-4 text-muted-foreground" />}
      >
        <Progress value={overview.cacheHitRate} className="mt-2" />
      </StatCard>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">System Health</CardTitle>
          {getHealthIcon(systemHealth.status)}
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold capitalize ${getHealthColor(systemHealth.status)}`}>
            {systemHealth.status}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
