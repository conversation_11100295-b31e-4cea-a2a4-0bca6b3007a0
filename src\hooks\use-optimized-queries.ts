"use client";
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useCallback, useEffect, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { useUser } from './use-user';
import { apiService } from '@/lib/services/api';
import { ShiftWithDetails, Job, Company, TimesheetDetails, User } from '@/lib/types';
import { createSmartCacheKey, QUERY_CONFIG } from '@/lib/query-config';

// Enhanced shifts hook with intelligent caching
export const useOptimizedShifts = (
  filters?: {
    date?: string;
    status?: string;
    companyId?: string;
    search?: string;
    jobId?: string;
  },
  options?: {
    enabled?: boolean;
    prefetch?: boolean;
    background?: boolean;
    forceRefresh?: boolean;
  }
) => {
  const { data: session } = useSession();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Create smart cache key with special handling for jobId
  const queryKey = useMemo(() => {
    // Ensure jobId is properly included in the cache key
    const enhancedFilters = { ...filters };
    if (filters?.jobId) {
      enhancedFilters.jobId = filters.jobId;
    }
    
    return createSmartCacheKey('shifts', enhancedFilters, [user?.id || 'anonymous']);
  }, [filters, user?.id]);

  // Force refetch when jobId is present to ensure fresh data
  const shouldForceRefresh = filters?.jobId && (options?.forceRefresh !== false);

  const query = useQuery({
    queryKey,
    queryFn: () => apiService.getShifts(filters),
    enabled: options?.enabled !== false,
    // Favor freshness for active shift views
    staleTime: shouldForceRefresh ? 20 : 60000,
    gcTime: QUERY_CONFIG.CACHE_TIMES.SHORT,
    retry: QUERY_CONFIG.RETRY.ATTEMPTS,
    retryDelay: QUERY_CONFIG.RETRY.DELAY,
  });

  // Debug logging for query results
  useEffect(() => {
    console.log(`🔍 [useOptimizedShifts] Query state:`, {
      isLoading: query.isLoading,
      isError: query.isError,
      data: query.data ? `${query.data.length} shifts` : 'no data',
      filters,
      shouldForceRefresh
    });
    if (query.error) {
      console.error(`❌ [useOptimizedShifts] Query error:`, query.error);
    }
  }, [query.isLoading, query.isError, query.data, query.error, filters, shouldForceRefresh]);

  return query;
};

// Enhanced single shift hook
export const useOptimizedShift = (
  shiftId: string,
  options?: {
    enabled?: boolean;
    prefetchAssignments?: boolean;
  }
) => {
  const queryClient = useQueryClient();

  const queryKey = useMemo(() =>
    createSmartCacheKey('shift', { id: shiftId }),
    [shiftId]
  );

  const query = useQuery({
    queryKey,
    queryFn: () => apiService.getShift(shiftId),
    enabled: !!shiftId && (options?.enabled !== false),
    staleTime: QUERY_CONFIG.STALE_TIMES.DYNAMIC,
    gcTime: QUERY_CONFIG.CACHE_TIMES.SHORT,
  });

  return query;
};

// Enhanced companies hook
export const useOptimizedCompanies = (
  filters?: { page?: number; pageSize?: number; search?: string },
  options?: {
    enabled?: boolean;
    prefetchLogos?: boolean;
  }
) => {
  const { data: session } = useSession();
  const { user } = useUser();
  const queryKey = useMemo(() =>
    createSmartCacheKey('companies', filters),
    [filters]
  );

  const query = useQuery({
    queryKey,
    queryFn: () => apiService.getCompanies(filters),
    enabled: !!user && (options?.enabled !== false),
    staleTime: QUERY_CONFIG.STALE_TIMES.STATIC,
    gcTime: QUERY_CONFIG.CACHE_TIMES.LONG,
  });

  return query;
};

// Enhanced jobs hook
export const useOptimizedJobs = (
  filters?: { status?: string; companyId?: string; search?: string; sortBy?: string },
  options?: {
    enabled?: boolean;
    prefetchShifts?: boolean;
  }
) => {
  const { data: session } = useSession();
  const { user } = useUser();
  const queryKey = useMemo(() =>
    createSmartCacheKey('jobs', filters),
    [filters]
  );

  const query = useQuery({
    queryKey,
    queryFn: () => apiService.getJobs(filters),
    enabled: !!user && (options?.enabled !== false),
    // Ensure jobs list reflects recent mutations quickly
    staleTime: 1000 * 20,
    gcTime: QUERY_CONFIG.CACHE_TIMES.MEDIUM,
  });

  return query;
};

// Enhanced timesheets hook
export const useOptimizedTimesheets = (
  filters?: { status?: string; userId?: string; date?: string },
  options?: {
    enabled?: boolean;
    realTime?: boolean;
  }
) => {
  const queryKey = useMemo(() =>
    createSmartCacheKey('timesheets', filters),
    [filters]
  );

  const query = useQuery({
    queryKey,
    queryFn: () => apiService.getTimesheets(filters),
    enabled: options?.enabled !== false,
    staleTime: options?.realTime ? 2000 : QUERY_CONFIG.STALE_TIMES.REAL_TIME,
    gcTime: QUERY_CONFIG.CACHE_TIMES.SHORT,
  });

  return query;
};

// Enhanced users hook
export const useOptimizedUsers = (
  filters?: {
    page?: number;
    pageSize?: number;
    fetchAll?: boolean;
    role?: string;
    search?: string;
    status?: 'active' | 'inactive';
    excludeCompanyUsers?: boolean;
  },
  options?: {
    enabled?: boolean;
    prefetchAvatars?: boolean;
  }
) => {
  const { data: session } = useSession();
  const { user } = useUser();
  const queryKey = useMemo(() =>
    createSmartCacheKey('users', filters),
    [filters]
  );

  const query = useQuery({
    queryKey,
    queryFn: () => apiService.getUsers(filters),
    enabled: !!user && (options?.enabled !== false),
    staleTime: 60 * 60 * 1000, // 1 hour for user data, including avatars
    gcTime: QUERY_CONFIG.CACHE_TIMES.MEDIUM,
  });

  return query;
};

// Enhanced mutation hook
export const useOptimizedMutation = <
  TData = unknown, 
  TError = Error, 
  TVariables = void,
  TContext = unknown
>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: {
    invalidateQueries?: unknown[][];
    onSuccess?: (data: TData, variables: TVariables, context: TContext) => void;
    onError?: (error: TError, variables: TVariables, context: TContext | undefined) => void;
    optimisticUpdater?: {
      queryKey: any[];
      updateFn: (oldData: TData, variables: TVariables) => TData;
    };
  }
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    onMutate: async (variables: TVariables) => {
      if (!options?.optimisticUpdater) return;

      const { queryKey, updateFn } = options.optimisticUpdater;

      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      queryClient.setQueryData(queryKey, (oldData: any) => {
        if (oldData === undefined) return undefined;
        return updateFn(oldData, variables);
      });

      return { previousData } as unknown as TContext;
    },
    onSuccess: (data, variables, context) => {
      // Invalidate related queries with full keys
      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach((key) => {
          queryClient.invalidateQueries({ queryKey: key });
        });
      }
      options?.onSuccess?.(data, variables, context);
    },
    onError: (error: TError, variables, context) => {
      if (options?.optimisticUpdater && context) {
        const { queryKey } = options.optimisticUpdater;
        const { previousData }: any = context
        if (previousData !== undefined) {
            queryClient.setQueryData(queryKey, previousData);
        }
      }
      options?.onError?.(error, variables, context);
    },
    onSettled: (data, error, variables, context) => {
        if (options?.optimisticUpdater) {
            const { queryKey } = options.optimisticUpdater;
            queryClient.invalidateQueries({ queryKey });
        }
    },
    retry: 2,
  });
};
