"use client"

import React, { use<PERSON>emo, useState } from 'react'
import { withAuth } from '@/lib/withAuth'
import { UserRole } from '@prisma/client'
import { useRouter } from 'next/navigation'
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

function PayrollSettingsPage() {
  const router = useRouter()
  const [baseRate, setBaseRate] = useState('')
  const [workweekStart, setWorkweekStart] = useState('0')

  const save = async () => {
    const resp = await fetch('/api/system-settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        key: 'payroll.settings',
        value: {
          workweekStartDay: Number(workweekStart),
          defaultHourlyRateCents: Math.round((parseFloat(baseRate) || 0) * 100)
        }
      })
    })
    if (resp.ok) router.refresh()
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payroll Settings</h1>
          <p className="text-muted-foreground">Configure payroll defaults and calculations</p>
        </div>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>General</CardTitle>
          <CardDescription>Workweek and defaults</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label>Workweek Start Day</Label>
              <Select value={workweekStart} onValueChange={setWorkweekStart}>
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Sunday</SelectItem>
                  <SelectItem value="1">Monday</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Default Hourly Rate ($)</Label>
              <Input value={baseRate} onChange={(e) => setBaseRate(e.target.value)} placeholder="e.g. 22.50" />
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={save}>Save Settings</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default withAuth(PayrollSettingsPage, UserRole.Admin)
