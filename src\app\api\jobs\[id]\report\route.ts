import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

// GET - Generate job report data
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const jobId = params.id;

    // Get job details with company information
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      include: {
        company: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    // Get all shifts for this job with assigned personnel
    const shifts = await prisma.shift.findMany({
      where: { jobId },
      include: {
        workerRequirements: { select: { workerTypeCode: true, requiredCount: true } },
        assignedPersonnel: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      },
      orderBy: [
        { date: 'asc' },
        { startTime: 'asc' }
      ]
    });

    // Calculate summary statistics
    const summary = {
      totalShifts: shifts.length,
      totalWorkerSlotsRequired: 0,
      totalWorkerSlotsAssigned: 0,
      workerTypeBreakdown: {
        crew_chief: { required: 0, assigned: 0 },
        fork_operator: { required: 0, assigned: 0 },
        stage_hand: { required: 0, assigned: 0 },
        general_labor: { required: 0, assigned: 0 },
        reach_fork_ops: { required: 0, assigned: 0 },
        rigger: { required: 0, assigned: 0 }

      },
      shiftStatusBreakdown: {
        pending: 0,
        active: 0,
        completed: 0,
        cancelled: 0
      },
      fillRate: 0
    };

    shifts.forEach(shift => {
      // Count required workers from normalized workerRequirements
      const reqs = Array.isArray((shift as any).workerRequirements) ? (shift as any).workerRequirements : [];
      const byType: Record<string, number> = {};
      let totalReq = 0;
      reqs.forEach((r: any) => {
        const code = r.roleCode || r.workerTypeCode;
        if (!code) return;
        const count = Number(r.requiredCount) || 0;
        totalReq += count;
        // Map role codes to worker type keys used in summary
        const map: Record<string, keyof typeof summary.workerTypeBreakdown> = {
          CC: 'crew_chief', FO: 'fork_operator', SH: 'stage_hand', GL: 'general_labor', RFO: 'reach_fork_ops', RG: 'rigger'
        };
        const key = map[code] || 'general_labor';
        byType[key] = (byType[key] || 0) + count;
      });
      summary.totalWorkerSlotsRequired += totalReq;

      // Count assigned workers
      const assignedWorkers = shift.assignedPersonnel?.filter(a => a.user) || [];
      summary.totalWorkerSlotsAssigned += assignedWorkers.length;

      // Worker type breakdown
      summary.workerTypeBreakdown.crew_chief.required += byType['crew_chief'] || 0;
      summary.workerTypeBreakdown.fork_operator.required += byType['fork_operator'] || 0;
      summary.workerTypeBreakdown.stage_hand.required += byType['stage_hand'] || 0;
      summary.workerTypeBreakdown.general_labor.required += byType['rigger'] || 0;
      summary.workerTypeBreakdown.reach_fork_ops.required += byType['reach_fork_ops'] || 0;

      assignedWorkers.forEach(assignment => {
        // Map roleCode to worker type for the breakdown
        let workerType: keyof typeof summary.workerTypeBreakdown;
        switch (assignment.roleCode) {
          case 'CC':
            workerType = 'crew_chief';
            break;
          case 'FO':
            workerType = 'fork_operator';
            break;
          case 'RFO':
            workerType = 'reach_fork_ops';
            break;
          case 'SH':
            workerType = 'stage_hand';
            break;
            case 'RG':
            workerType = 'rigger';
            break;
        }
        summary.workerTypeBreakdown[workerType].assigned++;
      });

      // Shift status breakdown
      const status = shift.status.toLowerCase() as keyof typeof summary.shiftStatusBreakdown;
      if (summary.shiftStatusBreakdown[status] !== undefined) {
        summary.shiftStatusBreakdown[status]++;
      }
    });

    // Calculate fill rate
    summary.fillRate = summary.totalWorkerSlotsRequired > 0 
      ? Math.round((summary.totalWorkerSlotsAssigned / summary.totalWorkerSlotsRequired) * 100)
      : 0;

    // Format response data
    const reportData = {
      job: {
        id: job.id,
        name: job.name,
        description: job.description,
        location: job.location,
        startDate: job.startDate?.toISOString(),
        endDate: job.endDate?.toISOString(),
        status: job.status,
        company: job.company
      },
      shifts: shifts.map(shift => ({
        id: shift.id,
        date: shift.date,
        startTime: shift.startTime.toISOString(),
        endTime: shift.endTime.toISOString(),
        description: shift.description,
        status: shift.status,
        workerRequirements: shift.workerRequirements?.map(r => ({
          roleCode: r.roleCode,
          requiredCount: r.requiredCount,
        })) || [],
        assignedPersonnel: shift.assignedPersonnel?.map(assignment => ({
          id: assignment.id,
          roleCode: assignment.roleCode,
          status: assignment.status,
          user: assignment.user
        })) || []
      })),
      summary,
      generatedAt: new Date().toISOString(),
      generatedBy: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email
      }
    };

    return NextResponse.json(reportData);

  } catch (error) {
    console.error('Error generating job report:', error);
    return NextResponse.json(
      { error: 'Failed to generate job report' },
      { status: 500 }
    );
  }
}

// POST - Generate and optionally save job report
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { saveReport, reportName, format } = await request.json();
    const jobId = params.id;

    // Get the report data (reuse GET logic)
    const getResponse = await GET(request, { params });
    const reportData = await getResponse.json();

    if (!getResponse.ok) {
      return getResponse;
    }

    // If saving report, store it in database
    // Note: JobReport model needs to be added to schema.prisma first
    if (saveReport && reportName) {
      // TODO: Add JobReport model to schema.prisma and uncomment this code
      /*
      const savedReport = await prisma.jobReport.create({
        data: {
          jobId,
          name: reportName,
          format: format || 'json',
          data: JSON.stringify(reportData),
          generatedBy: session.user.id,
          generatedAt: new Date()
        }
      });

      return NextResponse.json({
        ...reportData,
        savedReport: {
          id: savedReport.id,
          name: savedReport.name,
          createdAt: savedReport.createdAt
        }
      });
      */
      
      // For now, just return the report data with a note that saving is not implemented
      return NextResponse.json({
        ...reportData,
        note: 'Report saving functionality requires JobReport model to be added to schema'
      });
    }

    return NextResponse.json(reportData);

  } catch (error) {
    console.error('Error generating/saving job report:', error);
    return NextResponse.json(
      { error: 'Failed to generate job report' },
      { status: 500 }
    );
  }
}
