import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { PdfFromExcelGenerator } from '@/lib/pdf-from-excel-generator';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: timesheetId } = params;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id: timesheetId },
    });

    if (!timesheet) {
      return NextResponse.json({ error: 'Timesheet not found' }, { status: 404 });
    }

    const pdfGenerator = new PdfFromExcelGenerator(timesheetId);
    const pdfBuffer = await pdfGenerator.generatePDF();

    return new NextResponse(pdfBuffer as any, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="timesheet-${timesheetId}.pdf"`,
      },
    });
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
