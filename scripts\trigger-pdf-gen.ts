// @ts-nocheck
import { PrismaClient } from '@prisma/client';
import { generateUnsignedTimesheetPdf } from '../src/lib/enhanced-pdf-generator';

const prisma = new PrismaClient();

async function main() {
  try {
    const timesheet = await prisma.timesheet.findFirst({
      where: {
        status: {
          in: ['DRAFT', 'PENDING_COMPANY_APPROVAL'],
        },
      },
    });

    if (!timesheet) {
      console.log('No suitable timesheet found to generate a PDF.');
      return;
    }

    console.log(`Found timesheet with ID: ${timesheet.id}. Triggering PDF generation directly...`);

    // Directly call the function to bypass API authentication for this test script
    const pdfUrl = await generateUnsignedTimesheetPdf(timesheet.id);

    if (pdfUrl) {
      console.log('Successfully generated PDF:');
      console.log(`PDF data URL (first 100 chars): ${pdfUrl.substring(0, 100)}...`);
    } else {
      console.error('Failed to generate PDF.');
    }

  } catch (error) {
    console.error('An error occurred:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
