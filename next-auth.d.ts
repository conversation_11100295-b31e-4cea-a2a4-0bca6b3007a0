import { User as PrismaUser } from '@prisma/client';
import { UserRole } from '@/lib/types';
import NextAuth, { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      role: UserRole;
      companyId?: string | null;
      isAdmin?: boolean;
      upForGrabsNotifications?: boolean;
    } & DefaultSession['user'];
  }

  interface User extends PrismaUser {
    isAdmin?: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: UserRole;
    companyId?: string;
    isAdmin?: boolean;
    upForGrabsNotifications?: boolean;
    lastRefresh?: number;
  }
}
