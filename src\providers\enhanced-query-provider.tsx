"use client";

import React, { useState, useContext, createContext } from 'react';
import { QueryClient, QueryClientProvider, QueryCache, MutationCache, useQueryClient } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { toast } from '@/hooks/use-toast';

// Lightweight query client for maximum speed
const createFastQueryClient = () => {
  const queryCache = new QueryCache({
    onError: (error, query) => {
      // Minimal error handling - only show user-facing errors
      if (error instanceof Error && 
          !error.message.includes('fetch') && 
          !error.message.includes('Network') &&
          !error.message.includes('AbortError')) {
        console
      }
    },
  });

  const mutationCache = new MutationCache({
    onError: (error) => {
      if (error instanceof Error) {
        toast({
          title: "Operation Failed",
          description: error.message,
          variant: "destructive",
        });
      }
    },
  });

  return new QueryClient({
    queryCache,
    mutationCache,
    defaultOptions: {
      queries: {
        // Aggressive caching for speed
        staleTime: 30 * 1000, // 30 seconds
        gcTime: 2 * 60 * 1000, // 2 minutes
        refetchOnWindowFocus: false,
        refetchOnMount: false, // Don't refetch on mount for speed
        refetchOnReconnect: false, // Only refetch when explicitly needed
        retry: 0, // No retries for maximum speed
        networkMode: 'online',
        // suspense is configured per-query or globally via useSuspenseQuery
      },
      mutations: {
        retry: 0, // No retries for speed
        networkMode: 'online',
      },
    },
  });
};

interface FastQueryProviderProps {
  children: React.ReactNode;
}

export const EnhancedQueryProvider = ({ children }: FastQueryProviderProps) => {
  const [queryClient] = useState(() => createFastQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          position="bottom"
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
};

// Performance monitoring hooks
export const useQueryPerformanceStats = () => {
  const queryClient = useQueryClient();
  
  const getStats = () => {
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll();
    
    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.state.fetchStatus === 'fetching').length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      cacheSize: queries.length,
    };
  };

  const clearCache = () => {
    queryClient.clear();
  };

  const invalidateAll = () => {
    queryClient.invalidateQueries();
  };

  const removeStaleQueries = () => {
    queryClient.removeQueries({ stale: true });
  };

  return {
    getStats,
    clearCache,
    invalidateAll,
    removeStaleQueries,
  };
};

export const useCacheManagement = () => {
  const queryClient = useQueryClient();

  const invalidateByPattern = (pattern: string) => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        return query.queryKey.some(key => 
          typeof key === 'string' && key.includes(pattern)
        );
      },
    });
  };

  return {
    invalidateByPattern,
  };
};
