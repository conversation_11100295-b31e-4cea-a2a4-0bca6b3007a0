import js, { defineConfig } from "@eslint/js";
import path from "node:path";
import { fileURLToPath } from "node:url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default defineConfig([{
    extends: compat.extends("next"),

    rules: {
        "@typescript-eslint/no-explicit-any": "on",
        "react/no-unescaped-entities": "off",
        "react/prop-types": "off",
        "no-unused-vars": "off",
        "@next/next/no-img-element": "off",
        "@typescript-eslint/no-unused-vars": "on",
    },
}]);