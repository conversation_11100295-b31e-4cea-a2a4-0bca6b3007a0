"use client";

import React, { useEffect } from 'react';
import { ShiftWithDetails } from '@/lib/types';

interface GoogleMapProps {
  shift: ShiftWithDetails;
}

const GoogleMap: React.FC<GoogleMapProps> = ({ shift }) => {
  const mapRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadScript = () => {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places&callback=initMap`;
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    };

    let map: google.maps.Map;
    let service: google.maps.places.PlacesService;
    let infowindow: google.maps.InfoWindow;

    function initMap(): void {
      const sydney = new google.maps.LatLng(-33.867, 151.195);

      infowindow = new google.maps.InfoWindow();

      if (mapRef.current) {
        map = new google.maps.Map(mapRef.current, {
          center: sydney,
          zoom: 15,
        });

        const request = {
          query: shift.location || shift.job?.location || shift.description || "USA",
          fields: ["name", "geometry"],
        };

        service = new google.maps.places.PlacesService(map);

        service.findPlaceFromQuery(
          request,
          (
            results: google.maps.places.PlaceResult[] | null,
            status: google.maps.places.PlacesServiceStatus
          ) => {
            if (status === google.maps.places.PlacesServiceStatus.OK && results) {
              for (let i = 0; i < results.length; i++) {
                createMarker(results[i]);
              }
              if (results[0].geometry?.location) {
                map.setCenter(results[0].geometry.location);
              }
            }
          }
        );
      }
    }

    function createMarker(place: google.maps.places.PlaceResult) {
      if (!place.geometry || !place.geometry.location) return;

      const marker = new google.maps.Marker({
        map,
        position: place.geometry.location,
      });

      google.maps.event.addListener(marker, "click", () => {
        infowindow.setContent(place.name || "");
        infowindow.open(map);
      });
    }

    (window as any).initMap = initMap;

    if (!window.google) {
      loadScript();
    } else {
      initMap();
    }

  }, [shift]);

  const location = shift.location || shift.job?.location || shift.description;
  const encodedLocation = encodeURIComponent(location || "");
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedLocation}`;

  return (
    <div className="mt-4">
      <div ref={mapRef} className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden" style={{ height: '250px' }}>
      </div>
      <a
        href={directionsUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="mt-2 inline-block text-sm font-medium text-blue-600 hover:text-blue-800"
      >
        Get Directions
      </a>
    </div>
  );
};

export default GoogleMap;
