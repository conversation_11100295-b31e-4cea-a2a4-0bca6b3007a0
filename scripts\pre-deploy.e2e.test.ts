import fetch from 'node-fetch';
import { URLSearchParams } from 'url';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const API_URL = 'http://localhost:3000/api';
const prisma = new PrismaClient();

describe('Pre-deploy E2E Tests', () => {
  let adminToken: string;
  let companyId: string;
  let jobId: string;
  let shiftId: string;
  let companyUserId: string;
  let crewChiefId: string;
  let stagehandId: string;
  let createdUserIds: string[] = [];
  let createdCompanyIds: string[] = [];
  let createdJobIds: string[] = [];
  let createdShiftIds: string[] = [];
  let createdAssignmentIds: string[] = [];

  const adminCredentials = {
    email: `admin-${Date.now()}@example.com`,
    password: 'password123',
    name: 'Admin User',
    role: 'ADMIN',
  };

  const companyUserCredentials = {
    email: `company-${Date.now()}@example.com`,
    password: 'password123',
    name: 'Company User',
    role: 'COMPANY_USER',
  };

  const crewChiefCredentials = {
    email: `crewchief-${Date.now()}@example.com`,
    password: 'password123',
    name: 'Crew Chief',
    role: 'CREW_CHIEF',
  };

  const stagehandCredentials = {
    email: `stagehand-${Date.now()}@example.com`,
    password: 'password123',
    name: 'Stagehand User',
    role: 'STAGEHAND',
  };

  // Helper function for API requests
  const apiRequest = async (
    endpoint: string,
    method: string = 'GET',
    body?: any,
    token?: string
  ) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const options: RequestInit = {
      method,
      headers,
    };
    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(`${API_URL}${endpoint}`, options);
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.error?.message || `API Error: ${response.status} ${response.statusText}`);
    }
    return data;
  };

  beforeAll(async () => {
    // Hash passwords
    const adminPasswordHash = await bcrypt.hash(adminCredentials.password, 10);
    const companyUserPasswordHash = await bcrypt.hash(companyUserCredentials.password, 10);
    const crewChiefPasswordHash = await bcrypt.hash(crewChiefCredentials.password, 10);
    const stagehandPasswordHash = await bcrypt.hash(stagehandCredentials.password, 10);

    // 1. Create Admin User directly in DB
    const adminUser = await prisma.user.create({
      data: {
        email: adminCredentials.email,
        name: adminCredentials.name,
        passwordHash: adminPasswordHash,
        role: 'Admin', // Use the correct enum value
        isActive: true,
      },
    });
    createdUserIds.push(adminUser.id);

    // 2. Create other users directly in DB
    const companyUser = await prisma.user.create({
      data: {
        email: companyUserCredentials.email,
        name: companyUserCredentials.name,
        passwordHash: companyUserPasswordHash,
        role: 'CompanyUser', // Use the correct enum value
        isActive: true,
      },
    });
    companyUserId = companyUser.id;
    createdUserIds.push(companyUserId);

    const crewChiefUser = await prisma.user.create({
      data: {
        email: crewChiefCredentials.email,
        name: crewChiefCredentials.name,
        passwordHash: crewChiefPasswordHash,
        role: 'CrewChief', // Use the correct enum value
        isActive: true,
      },
    });
    crewChiefId = crewChiefUser.id;
    createdUserIds.push(crewChiefId);

    const stagehandUser = await prisma.user.create({
      data: {
        email: stagehandCredentials.email,
        name: stagehandCredentials.name,
        passwordHash: stagehandPasswordHash,
        role: 'StageHand', // Use the correct enum value
        isActive: true,
      },
    });
    stagehandId = stagehandUser.id;
    createdUserIds.push(stagehandId);

    // Login Admin to get token
    const adminLoginResponse = await apiRequest('/auth/login', 'POST', {
      email: adminCredentials.email,
      password: adminCredentials.password,
    });
    expect(adminLoginResponse.success).toBe(true);
    adminToken = adminLoginResponse.data.token;
  });

  afterAll(async () => {
    // Cleanup all created data using cascade delete endpoints
    for (const assignmentId of createdAssignmentIds) {
      try {
        await apiRequest(`/cascade-delete/assignment/${assignmentId}`, 'DELETE', undefined, adminToken);
      } catch (error) {
        console.warn(`Failed to delete assignment ${assignmentId}:`, error);
      }
    }
    for (const shiftId of createdShiftIds) {
      try {
        await apiRequest(`/cascade-delete/shift/${shiftId}`, 'DELETE', undefined, adminToken);
      } catch (error) {
        console.warn(`Failed to delete shift ${shiftId}:`, error);
      }
    }
    for (const jobId of createdJobIds) {
      try {
        await apiRequest(`/cascade-delete/job/${jobId}`, 'DELETE', undefined, adminToken);
      } catch (error) {
        console.warn(`Failed to delete job ${jobId}:`, error);
      }
    }
    for (const companyId of createdCompanyIds) {
      try {
        await apiRequest(`/cascade-delete/company/${companyId}`, 'DELETE', undefined, adminToken);
      } catch (error) {
        console.warn(`Failed to delete company ${companyId}:`, error);
      }
    }
    for (const userId of createdUserIds) {
      try {
        await apiRequest(`/users/${userId}`, 'DELETE', undefined, adminToken);
      } catch (error) {
        console.warn(`Failed to delete user ${userId}:`, error);
      }
    }
  });

  it('should allow admin to create a company', async () => {
    const companyName = `Test Company ${Date.now()}`;
    const companyResponse = await apiRequest('/companies', 'POST', { name: companyName }, adminToken);
    expect(companyResponse.success).toBe(true);
    expect(companyResponse.data.name).toBe(companyName);
    companyId = companyResponse.data.id;
    createdCompanyIds.push(companyId);
  });

  it('should allow admin to create a job for the company', async () => {
    const jobTitle = `Test Job ${Date.now()}`;
    const jobResponse = await apiRequest(`/jobs`, 'POST', {
      title: jobTitle,
      companyId: companyId,
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
    }, adminToken);
    expect(jobResponse.success).toBe(true);
    expect(jobResponse.data.title).toBe(jobTitle);
    jobId = jobResponse.data.id;
    createdJobIds.push(jobId);
  });

  it('should allow admin to create a shift for the job', async () => {
    const shiftName = `Test Shift ${Date.now()}`;
    const shiftResponse = await apiRequest(`/shifts`, 'POST', {
      name: shiftName,
      jobId: jobId,
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
    }, adminToken);
    expect(shiftResponse.success).toBe(true);
    expect(shiftResponse.data.name).toBe(shiftName);
    shiftId = shiftResponse.data.id;
    createdShiftIds.push(shiftId);
  });

  it('should allow admin to assign a stagehand to the shift', async () => {
    const assignResponse = await apiRequest(`/shifts/${shiftId}/assign`, 'POST', {
      workerId: stagehandId,
      role: 'STAGEHAND',
    }, adminToken);
    expect(assignResponse.success).toBe(true);
    expect(assignResponse.data.workerId).toBe(stagehandId);
    expect(assignResponse.data.shiftId).toBe(shiftId);
    createdAssignmentIds.push(assignResponse.data.id);
  });

  it('should allow stagehand to view their assigned shift', async () => {
    const stagehandLoginResponse = await apiRequest('/auth/login', 'POST', {
      email: stagehandCredentials.email,
      password: stagehandCredentials.password,
    });
    expect(stagehandLoginResponse.success).toBe(true);
    const stagehandAuthToken = stagehandLoginResponse.data.token;

    const shiftsResponse = await apiRequest(`/shifts/${shiftId}`, 'GET', undefined, stagehandAuthToken);
    expect(shiftsResponse.success).toBe(true);
    expect(shiftsResponse.data.id).toBe(shiftId);
  });

  it('should prevent unauthorized access to create a company', async () => {
    const companyName = `Unauthorized Company ${Date.now()}`;
    await expect(apiRequest('/companies', 'POST', { name: companyName })).rejects.toThrow('API Error: 401 Unauthorized');
  });

  it('should prevent creating a job with invalid companyId', async () => {
    const jobTitle = `Invalid Job ${Date.now()}`;
    await expect(apiRequest(`/jobs`, 'POST', {
      title: jobTitle,
      companyId: 'invalid-company-id',
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
    }, adminToken)).rejects.toThrow(); // Expecting an error, specific message might vary
  });
});