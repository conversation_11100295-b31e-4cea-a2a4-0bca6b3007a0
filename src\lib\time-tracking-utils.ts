import { formatTimeForDisplay, formatTimeForInput } from '@/lib/utils';
import type { TimeEntry as TimeEntryType, Assignment as WorkerAssignment } from '@/lib/types';

// --- Centralized Worker Status Constants ---
export const WORKER_STATUS = {
  UP_FOR_GRABS: 'up_for_grabs',
  NOT_ASSIGNED: 'not_assigned',
  NOT_STARTED: 'not_started',
  CLOCKED_IN: 'clocked_in',
  ON_BREAK: 'on_break',
  CLOCKED_OUT: 'clocked_out',
  SHIFT_ENDED: 'shift_ended',
  NO_SHOW: 'no_show',
} as const;

export type WorkerStatus = typeof WORKER_STATUS[keyof typeof WORKER_STATUS];

export const getWorkerStatus = (assignment: WorkerAssignment): WorkerStatus => {
  if (assignment.status === 'UpForGrabs') return WORKER_STATUS.UP_FOR_GRABS;
  if (!assignment.userId) return WORKER_STATUS.NOT_ASSIGNED;
  if (assignment.status === 'NoShow') return WORKER_STATUS.NO_SHOW;
  if (assignment.status === 'ShiftEnded') return WORKER_STATUS.SHIFT_ENDED;

  const activeEntry = assignment.timeEntries.find(entry => entry.clockIn && !entry.clockOut);
  if (activeEntry) {
    return WORKER_STATUS.CLOCKED_IN;
  }

  const hasAnyEntry = assignment.timeEntries.length > 0;
  if (hasAnyEntry) {
    return WORKER_STATUS.CLOCKED_OUT;
  }

  return WORKER_STATUS.NOT_STARTED;
};

export const formatTime = (timeString?: string | Date | null, format24hr = false) => {
  if (!timeString) return format24hr ? '' : '-';
  try {
    const date = new Date(timeString);
    if (isNaN(date.getTime())) return format24hr ? '' : '-';

    if (format24hr) {
      return formatTimeForInput(date);
    }
    return formatTimeForDisplay(date);
  } catch (error) {
    return format24hr ? '' : '-';
  }
};

export const calculateTotalHours = (timeEntries: TimeEntryType[]): number => {
  let totalMinutes = 0;
  timeEntries.forEach(entry => {
    if (entry.clockIn && entry.clockOut) {
      const start = new Date(entry.clockIn);
      const end = new Date(entry.clockOut);
      totalMinutes += (end.getTime() - start.getTime()) / (1000 * 60);
    }
  });
  return totalMinutes / 60;
};
