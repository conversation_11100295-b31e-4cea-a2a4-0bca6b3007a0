import { prisma } from '@/lib/prisma';
import { Prisma, Company, Job, Shift, AssignedPersonnel, User, TimeEntry, Timesheet, TimesheetEntry } from '@prisma/client';

// Define a comprehensive type for the timesheet data
export type FullTimesheetData = Timesheet & {
  entries: TimesheetEntry[];
  shift: Shift & {
    job: Job & {
      company: Company;
    };
    assignedPersonnel: (AssignedPersonnel & {
      user: User;
      timeEntries: TimeEntry[];
    })[];
  };
};

/**
 * Fetches the complete timesheet data required for PDF generation.
 * @param timesheetId The ID of the timesheet to fetch.
 * @param tx A Prisma transaction client (optional).
 * @returns The complete timesheet data.
 * @throws Error if the timesheet is not found or data is incomplete.
 */
export async function getTimesheetDataForPdf(
  timesheetId: string,
  tx?: Omit<Prisma.TransactionClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>
): Promise<FullTimesheetData> {
  const db = tx || prisma;
  const timesheet = await db.timesheet.findUnique({
    where: { id: timesheetId },
    include: {
      entries: {
        orderBy: [{ userName: 'asc' }, { entryNumber: 'asc' }],
      },
      shift: {
        include: {
          job: { include: { company: true } },
          assignedPersonnel: {
            include: {
              user: true,
              timeEntries: { orderBy: { createdAt: 'asc' } },
            },
          },
        },
      },
    },
  });

  if (!timesheet || !timesheet.shift || !timesheet.shift.job) {
    throw new Error('Timesheet not found or data is incomplete for PDF generation.');
  }

  return timesheet as FullTimesheetData;
}
