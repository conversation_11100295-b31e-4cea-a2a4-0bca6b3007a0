"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { LoadingSpinner } from "@/components/loading-states";

async function fetchUpForGrabs() {
  const res = await fetch("/api/admin/up-for-grabs", { cache: "no-store" });
  if (!res.ok) throw new Error("Failed to load list");
  return res.json();
}

export default function UpForGrabsClientPage() {
  const router = useRouter();

  const { data: items = [], isLoading: loading } = useQuery<any[]>({
    queryKey: ['up-for-grabs-admin'],
    queryFn: fetchUpForGrabs,
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Up For Grabs Management</CardTitle>
          <CardDescription>Manage current and past up-for-grabs shift offers.</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : items.length === 0 ? (
            <div>No offers found.</div>
          ) : (
            <div className="grid gap-4">
              {items.map((x) => (
                <div key={x.id} className="border rounded p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{x.shift.job.name}</div>
                      <div className="text-sm text-muted-foreground">{x.shift.description ?? 'No description'}</div>
                      <div className="text-xs text-muted-foreground">Client: {x.shift.job.company.name}</div>
                      <div className="text-xs text-muted-foreground">Start: {format(new Date(x.shift.startTime), 'PPP p')}</div>
                      <div className="text-xs text-muted-foreground">Location: {x.shift.job.location ?? 'N/A'}</div>
                    </div>
                    <div className="text-right space-y-1">
                      <Badge variant={x.status === 'UpForGrabs' ? 'default' : 'secondary'}>{x.status}</Badge>
                      <div className="text-xs">Role: {x.roleCode}</div>
                      {x.offeredBy && <div className="text-xs">Offered by: {x.offeredBy.name}</div>}
                      {x.claimedBy && <div className="text-xs">Claimed by: {x.claimedBy.name}</div>}
                      <Button size="sm" variant="outline" onClick={() => router.push(`/shifts/${x.shift.id}`)}>View Shift</Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
