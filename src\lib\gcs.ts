import { Storage } from '@google-cloud/storage';
import { Readable } from 'stream';

// Initialize Google Cloud Storage with proper authentication for Vercel
const storage = new Storage({
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  // For Vercel, use service account key from environment variable
  ...(process.env.GOOGLE_SERVICE_ACCOUNT_KEY && {
    credentials: JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY)
  })
});

const bucketName = process.env.GCS_BUCKET_NAME;

if (!bucketName) {
  console.warn('GCS_BUCKET_NAME environment variable not set. File uploads will be disabled.');
}

/**
 * Uploads a file buffer to Google Cloud Storage.
 * @param buffer The file buffer to upload.
 * @param destination The destination path in the GCS bucket (e.g., 'timesheets/signed-copy.pdf').
 * @param contentType The MIME type of the file.
 * @returns The public URL of the uploaded file.
 */
export async function uploadToGCS(buffer: Buffer, destination: string, contentType: string): Promise<string> {
  if (!bucketName) {
    throw new Error('Google Cloud Storage bucket name is not configured.');
  }

  const bucket = storage.bucket(bucketName);
  const file = bucket.file(destination);

  const stream = new Readable();
  stream.push(buffer);
  stream.push(null); // Signifies the end of the stream

  return new Promise((resolve, reject) => {
    const writeStream = file.createWriteStream({
      metadata: {
        contentType: contentType,
      },
      resumable: false,
    });

    writeStream.on('error', (err) => {
      console.error(`Error uploading to GCS: ${err.message}`, err);
      reject(err);
    });

    writeStream.on('finish', () => {
      const publicUrl = `https://storage.googleapis.com/${bucketName}/${destination}`;
      resolve(publicUrl);
    });

    stream.pipe(writeStream);
  });
}

/**
 * Lists files in the bucket under a given prefix.
 */
export async function listFromGCS(prefix: string): Promise<{ name: string; size: number; updated: Date }[]> {
  if (!bucketName) throw new Error('GCS bucket not configured');
  const [files] = await storage.bucket(bucketName).getFiles({ prefix });
  return files.map((f) => ({
    name: f.name,
    size: Number(f.metadata?.size || 0),
    updated: new Date(f.metadata?.updated || Date.now()),
  }));
}

/**
 * Generates a signed URL for a file to allow temporary access (download/view).
 */
export async function getSignedUrlGCS(path: string, expiresInSeconds = 3600): Promise<string> {
  if (!bucketName) throw new Error('GCS bucket not configured');
  const [url] = await storage
    .bucket(bucketName)
    .file(path)
    .getSignedUrl({
      action: 'read',
      expires: Date.now() + expiresInSeconds * 1000,
    });
  return url;
}

/**
 * Checks whether a file exists in the bucket.
 */
export async function existsInGCS(path: string): Promise<boolean> {
  if (!bucketName) return false;
  const [exists] = await storage.bucket(bucketName).file(path).exists();
  return exists;
}