import { UserRole } from '@prisma/client';

declare module 'next-auth' {
  interface Session {
    accessToken?: string;
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role: UserRole;
      companyId?: string;
      avatarUrl?: string | null;
      upForGrabsNotifications?: boolean;
    };
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role: UserRole;
    passwordHash: string;
    isActive: boolean;
    crew_chief_eligible: boolean;
    fork_operator_eligible: boolean;
    OSHA_10_Certifications: boolean;
    phone?: string | null;
    payrollType: string;
    payrollBaseRateCents: number;
    ssnEncrypted?: string | null;
    ssnLast4?: string | null;
    addressLine1?: string | null;
    addressLine2?: string | null;
    city?: string | null;
    state?: string | null;
    postalCode?: string | null;
    upForGrabsNotifications?: boolean;
    companyId?: string | null;
    avatarUrl?: string | null;
    emailVerified?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    lastLogin?: Date | null;
    lastPasswordChange?: Date | null;
    isTwoFactorEnabled?: boolean;
    twoFactorSecret?: string | null;
    twoFactorConfirmed?: boolean;
    resetPasswordToken?: string | null;
    resetPasswordExpires?: Date | null;
    verificationToken?: string | null;
    verificationExpires?: Date | null;
    accessToken?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: UserRole;
    companyId?: string;
    avatarUrl?: string | null;
    upForGrabsNotifications?: boolean;
    accessToken?: string;
    error?: string;
  }
}
