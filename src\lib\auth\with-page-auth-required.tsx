import { redirect } from "next/navigation";
import { getSession } from "@/lib/auth/session";
import { UserRole } from "@/lib/types";
import React from "react";
import { getDashboardPath } from "@/lib/routing";

type PageAuthProps = {
  allowedRoles?: UserRole[];
  redirectTo?: string;
};

// This HOC is for Server Components
export function withPageAuthRequired<P extends object>(
  Page: React.ComponentType<P>,
  options: PageAuthProps = {}
) {
  return async function WithPageAuthRequired(props: P) {
    const session = await getSession();

    if (!session?.user) {
      redirect(options.redirectTo || "/login");
    }

    const user = session.user;

    if (
      options.allowedRoles &&
      options.allowedRoles.length > 0 &&
      !options.allowedRoles.includes(user.role as UserRole)
    ) {
      redirect(getDashboardPath(user.role as UserRole, (user as any).companyId) || "/unauthorized");
    }

    // @ts-ignore Server Component
    return <Page {...props} user={user} />;
  };
}