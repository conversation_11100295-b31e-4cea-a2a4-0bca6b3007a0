// Enhanced Scheduling Timeline API Route
// Provides comprehensive shift and staffing data for the scheduling timeline dashboard
// Supports advanced filtering, meeting mode features, and real-time updates

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createPacificDateTime } from '@/lib/utils'

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const jobId = params.id
    console.log(`🚀 [API] Starting scheduling timeline fetch for jobId: ${jobId}`);
    
    if (!jobId) {
      console.error(`❌ [API] No jobId provided`);
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }
    
    console.log(`🔍 [API] Fetching job details for jobId: ${jobId}`);
    // Fetch the job with company
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      include: {
        company: {
          select: {
            id: true,
            name: true,
            company_logo_url: true
          }
        }
      }
    })
    
    console.log(`📋 [API] Job query result:`, job ? { id: job.id, name: job.name } : 'Not found');
    if (!job) {
      console.error(`❌ [API] Job not found for jobId: ${jobId}`);
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }
    
    console.log(`🔍 [API] Fetching shifts for jobId: ${jobId}`);
    // Fetch all shifts for this job with assigned personnel
    const shifts = await prisma.shift.findMany({
      where: { jobId },
      orderBy: [
        { date: 'asc' },
        { startTime: 'asc' }
      ],
      include: {
        assignedPersonnel: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatarUrl: true,
                role: true
              }
            }
          }
        }
      }
    })
    
    // Log raw shifts data for debugging
    console.log(`🔄 [API] Found ${shifts.length} shifts for job ${jobId}`);
    console.log(`📊 [API] Shifts query completed successfully`);
    if (shifts.length > 0) {
      console.log(`📋 [API] Sample shift data:`, {
        id: shifts[0].id,
        date: shifts[0].date,
        startTime: shifts[0].startTime,
        endTime: shifts[0].endTime,
        assignedPersonnelCount: shifts[0].assignedPersonnel?.length || 0,
        sampleAssignment: shifts[0].assignedPersonnel?.[0] ? {
          id: shifts[0].assignedPersonnel[0].id,
          roleCode: shifts[0].assignedPersonnel[0].roleCode,
          hasUser: !!shifts[0].assignedPersonnel[0].user,
          userName: shifts[0].assignedPersonnel[0].user?.name || 'No user'
        } : 'No assignments'
      });
    }

    console.log(`🔧 [API] Starting transformation of ${shifts.length} shifts`);

    // Transform the data for the timeline
    const transformedShifts = shifts.map((shift, index) => {
      try {
        console.log(`🔧 [API] Processing shift ${index + 1}/${shifts.length}: ${shift.id}`);

        // Separate crew chiefs from regular workers
        const crewChiefs = shift.assignedPersonnel
          .filter(ap => ap.roleCode === 'CC' && ap.user) // Ensure user exists
          .map(ap => ({
            id: ap.user!.id, // Use user ID for consistent filtering
            assignmentId: ap.id, // Keep assignment ID for reference
            name: ap.user!.name,
            avatar: ap.user!.avatarUrl || '',
            status: ap.status,
            roleCode: ap.roleCode
          }))
      
        // Group workers by role code
        const workersByType = shift.assignedPersonnel
          .filter(ap => ap.roleCode !== 'CC' && ap.user) // Ensure user exists
          .reduce((acc, ap) => {
            const roleCode = ap.roleCode
            if (!acc[roleCode]) {
              acc[roleCode] = []
            }
            
            acc[roleCode].push({
              id: ap.user!.id, // Use user ID for consistent filtering
              assignmentId: ap.id, // Keep assignment ID for reference
              name: ap.user!.name,
              avatar: ap.user!.avatarUrl || '',
              status: ap.status,
              roleCode: ap.roleCode
            })
            
            return acc
          }, {} as Record<string, any>)

        // Construct Date objects for start and end times using shift.date in Pacific timezone
        const shiftDate = new Date(shift.date);
        const startDateTime = createPacificDateTime(shiftDate, shift.startTime);
        const endDateTime = createPacificDateTime(shiftDate, shift.endTime);
        
        return {
          id: shift.id,
          date: shift.date.toISOString(),
          startTime: startDateTime,
          endTime: endDateTime,
          status: shift.status,
          location: shift.location || '',
          description: shift.description || '',
          notes: shift.notes || '',
          requiredCrewChiefs: shift.requiredCrewChiefs || 0,
          requiredStagehands: shift.requiredStagehands || 0,
          requiredForkOperators: shift.requiredForkOperators || 0,
          requiredReachForkOperators: shift.requiredReachForkOperators || 0,
          requiredRiggers: shift.requiredRiggers || 0,
          crewChiefs,
          workers: workersByType,
          // Additional metadata for enhanced features (handle missing fields gracefully)
          createdAt: shift.createdAt ? shift.createdAt.toISOString() : new Date().toISOString(),
          updatedAt: shift.updatedAt ? shift.updatedAt.toISOString() : new Date().toISOString(),
          // Calculate staffing metrics
          totalAssigned: crewChiefs.length + Object.values(workersByType).flat().length,
          staffingCompletion: Math.round(
            ((crewChiefs.length + Object.values(workersByType).flat().length) /
             Math.max(1, 1)) * 100
          )
        }
      } catch (shiftError) {
        console.error(`❌ [API] Error transforming shift ${shift.id}:`, shiftError);
        throw new Error(`Failed to transform shift ${shift.id}: ${shiftError instanceof Error ? shiftError.message : 'Unknown error'}`);
      }
    })

    // Log transformed data for debugging
    console.log(`✅ [API] Transformed ${transformedShifts.length} shifts`);
    if (transformedShifts.length > 0) {
      console.log(`📊 [API] Sample transformed shift:`, {
        id: transformedShifts[0].id,
        crewChiefsCount: transformedShifts[0].crewChiefs.length,
        workersKeys: Object.keys(transformedShifts[0].workers),
        totalAssigned: transformedShifts[0].totalAssigned,
        startTime: transformedShifts[0].startTime,
        endTime: transformedShifts[0].endTime
      });
    }
    
    return NextResponse.json({
      job: {
        id: job.id,
        name: job.name,
        description: job.description || '',
        status: job.status,
        startDate: job.startDate?.toISOString() || new Date().toISOString(),
        endDate: job.endDate?.toISOString() || new Date().toISOString(),
        location: job.location || '',
        company: {
          id: job.company.id,
          name: job.company.name,
          logo: job.company.company_logo_url || ''
        }
      },
      shifts: transformedShifts
    })
  } catch (error) {
    console.error('❌ [API] Error fetching scheduling timeline data:', error)
    console.error('❌ [API] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      jobId: params.id,
      timestamp: new Date().toISOString()
    })
    return NextResponse.json(
      { error: 'Failed to fetch scheduling timeline data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
