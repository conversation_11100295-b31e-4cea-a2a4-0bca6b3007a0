import { performance } from 'perf_hooks';

export interface PDFPerformanceMetrics {
  timesheetId: string;
  generationTime: number;
  pdfSize: number;
  templateId: string;
  includeSignature: boolean;
  cacheHit: boolean;
  errorCount: number;
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  timestamp: Date;
}

export interface PDFPerformanceStats {
  totalGenerations: number;
  averageGenerationTime: number;
  averagePdfSize: number;
  cacheHitRate: number;
  errorRate: number;
  peakMemoryUsage: number;
  slowestGeneration: PDFPerformanceMetrics | null;
  fastestGeneration: PDFPerformanceMetrics | null;
}

/**
 * Performance monitoring for PDF generation
 */
export class PDFPerformanceMonitor {
  private static instance: PDFPerformanceMonitor;
  private metrics: PDFPerformanceMetrics[] = [];
  private maxMetricsHistory = 1000; // Keep last 1000 generations
  private performanceThresholds = {
    slowGenerationMs: 5000, // 5 seconds
    largePdfBytes: 5 * 1024 * 1024, // 5MB
    highMemoryMB: 100 // 100MB
  };

  static getInstance(): PDFPerformanceMonitor {
    if (!PDFPerformanceMonitor.instance) {
      PDFPerformanceMonitor.instance = new PDFPerformanceMonitor();
    }
    return PDFPerformanceMonitor.instance;
  }

  /**
   * Start performance tracking for a PDF generation
   */
  startTracking(timesheetId: string, templateId: string): PDFGenerationTracker {
    return new PDFGenerationTracker(this, timesheetId, templateId);
  }

  /**
   * Record performance metrics
   */
  recordMetrics(metrics: PDFPerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // Log performance warnings
    this.checkPerformanceThresholds(metrics);
  }

  /**
   * Get performance statistics
   */
  getStats(timeRangeMs?: number): PDFPerformanceStats {
    let relevantMetrics = this.metrics;
    
    // Filter by time range if specified
    if (timeRangeMs) {
      const cutoffTime = new Date(Date.now() - timeRangeMs);
      relevantMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
    }

    if (relevantMetrics.length === 0) {
      return this.getEmptyStats();
    }

    const totalGenerations = relevantMetrics.length;
    const totalTime = relevantMetrics.reduce((sum, m) => sum + m.generationTime, 0);
    const totalSize = relevantMetrics.reduce((sum, m) => sum + m.pdfSize, 0);
    const cacheHits = relevantMetrics.filter(m => m.cacheHit).length;
    const errors = relevantMetrics.reduce((sum, m) => sum + m.errorCount, 0);
    
    const memoryUsages = relevantMetrics.map(m => m.memoryUsage.heapUsed);
    const peakMemory = Math.max(...memoryUsages);
    
    const sortedByTime = [...relevantMetrics].sort((a, b) => a.generationTime - b.generationTime);

    return {
      totalGenerations,
      averageGenerationTime: totalTime / totalGenerations,
      averagePdfSize: totalSize / totalGenerations,
      cacheHitRate: (cacheHits / totalGenerations) * 100,
      errorRate: (errors / totalGenerations) * 100,
      peakMemoryUsage: peakMemory,
      slowestGeneration: sortedByTime[sortedByTime.length - 1] || null,
      fastestGeneration: sortedByTime[0] || null
    };
  }

  /**
   * Get metrics for specific timesheet
   */
  getTimesheetMetrics(timesheetId: string): PDFPerformanceMetrics[] {
    return this.metrics.filter(m => m.timesheetId === timesheetId);
  }

  /**
   * Get template performance comparison
   */
  getTemplateComparison(): Map<string, PDFPerformanceStats> {
    const templateGroups = new Map<string, PDFPerformanceMetrics[]>();
    
    // Group metrics by template
    this.metrics.forEach(metric => {
      if (!templateGroups.has(metric.templateId)) {
        templateGroups.set(metric.templateId, []);
      }
      templateGroups.get(metric.templateId)!.push(metric);
    });

    // Calculate stats for each template
    const comparison = new Map<string, PDFPerformanceStats>();
    templateGroups.forEach((metrics, templateId) => {
      const stats = this.calculateStatsForMetrics(metrics);
      comparison.set(templateId, stats);
    });

    return comparison;
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      return this.exportAsCSV();
    }
    return JSON.stringify(this.metrics, null, 2);
  }

  /**
   * Clear metrics history
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const stats = this.getStats();
    const recommendations: string[] = [];

    if (stats.averageGenerationTime > this.performanceThresholds.slowGenerationMs) {
      recommendations.push('Consider optimizing PDF templates or implementing caching');
    }

    if (stats.cacheHitRate < 50) {
      recommendations.push('Improve caching strategy to reduce generation time');
    }

    if (stats.errorRate > 5) {
      recommendations.push('Investigate and fix PDF generation errors');
    }

    if (stats.averagePdfSize > this.performanceThresholds.largePdfBytes) {
      recommendations.push('Consider PDF compression or template optimization');
    }

    if (stats.peakMemoryUsage > this.performanceThresholds.highMemoryMB * 1024 * 1024) {
      recommendations.push('Monitor memory usage and consider batch processing limits');
    }

    return recommendations;
  }

  /**
   * Check performance thresholds and log warnings
   */
  private checkPerformanceThresholds(metrics: PDFPerformanceMetrics): void {
    const warnings: string[] = [];

    if (metrics.generationTime > this.performanceThresholds.slowGenerationMs) {
      warnings.push(`Slow PDF generation: ${metrics.generationTime}ms for ${metrics.timesheetId}`);
    }

    if (metrics.pdfSize > this.performanceThresholds.largePdfBytes) {
      warnings.push(`Large PDF generated: ${(metrics.pdfSize / 1024 / 1024).toFixed(2)}MB for ${metrics.timesheetId}`);
    }

    if (metrics.memoryUsage.heapUsed > this.performanceThresholds.highMemoryMB * 1024 * 1024) {
      warnings.push(`High memory usage: ${(metrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB for ${metrics.timesheetId}`);
    }

    if (metrics.errorCount > 0) {
      warnings.push(`PDF generation errors: ${metrics.errorCount} for ${metrics.timesheetId}`);
    }

    warnings.forEach(warning => console.warn(`[PDF Performance] ${warning}`));
  }

  /**
   * Calculate stats for a set of metrics
   */
  private calculateStatsForMetrics(metrics: PDFPerformanceMetrics[]): PDFPerformanceStats {
    if (metrics.length === 0) {
      return this.getEmptyStats();
    }

    const totalGenerations = metrics.length;
    const totalTime = metrics.reduce((sum, m) => sum + m.generationTime, 0);
    const totalSize = metrics.reduce((sum, m) => sum + m.pdfSize, 0);
    const cacheHits = metrics.filter(m => m.cacheHit).length;
    const errors = metrics.reduce((sum, m) => sum + m.errorCount, 0);
    
    const memoryUsages = metrics.map(m => m.memoryUsage.heapUsed);
    const peakMemory = Math.max(...memoryUsages);
    
    const sortedByTime = [...metrics].sort((a, b) => a.generationTime - b.generationTime);

    return {
      totalGenerations,
      averageGenerationTime: totalTime / totalGenerations,
      averagePdfSize: totalSize / totalGenerations,
      cacheHitRate: (cacheHits / totalGenerations) * 100,
      errorRate: (errors / totalGenerations) * 100,
      peakMemoryUsage: peakMemory,
      slowestGeneration: sortedByTime[sortedByTime.length - 1] || null,
      fastestGeneration: sortedByTime[0] || null
    };
  }

  /**
   * Get empty stats structure
   */
  private getEmptyStats(): PDFPerformanceStats {
    return {
      totalGenerations: 0,
      averageGenerationTime: 0,
      averagePdfSize: 0,
      cacheHitRate: 0,
      errorRate: 0,
      peakMemoryUsage: 0,
      slowestGeneration: null,
      fastestGeneration: null
    };
  }

  /**
   * Export metrics as CSV
   */
  private exportAsCSV(): string {
    if (this.metrics.length === 0) {
      return 'No metrics available';
    }

    const headers = [
      'timesheetId',
      'generationTime',
      'pdfSize',
      'templateId',
      'includeSignature',
      'cacheHit',
      'errorCount',
      'heapUsed',
      'heapTotal',
      'external',
      'timestamp'
    ];

    const rows = this.metrics.map(m => [
      m.timesheetId,
      m.generationTime,
      m.pdfSize,
      m.templateId,
      m.includeSignature,
      m.cacheHit,
      m.errorCount,
      m.memoryUsage.heapUsed,
      m.memoryUsage.heapTotal,
      m.memoryUsage.external,
      m.timestamp.toISOString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}

/**
 * Individual PDF generation tracker
 */
export class PDFGenerationTracker {
  private monitor: PDFPerformanceMonitor;
  private timesheetId: string;
  private templateId: string;
  private startTime: number;
  private startMemory: NodeJS.MemoryUsage;
  private errorCount = 0;
  private cacheHit = false;

  constructor(monitor: PDFPerformanceMonitor, timesheetId: string, templateId: string) {
    this.monitor = monitor;
    this.timesheetId = timesheetId;
    this.templateId = templateId;
    this.startTime = performance.now();
    this.startMemory = process.memoryUsage();
  }

  /**
   * Mark cache hit
   */
  markCacheHit(): void {
    this.cacheHit = true;
  }

  /**
   * Record an error
   */
  recordError(): void {
    this.errorCount++;
  }

  /**
   * Finish tracking and record metrics
   */
  finish(pdfBuffer: Buffer, includeSignature: boolean = false): void {
    const endTime = performance.now();
    const endMemory = process.memoryUsage();
    
    const metrics: PDFPerformanceMetrics = {
      timesheetId: this.timesheetId,
      generationTime: endTime - this.startTime,
      pdfSize: pdfBuffer.length,
      templateId: this.templateId,
      includeSignature,
      cacheHit: this.cacheHit,
      errorCount: this.errorCount,
      memoryUsage: {
        heapUsed: endMemory.heapUsed,
        heapTotal: endMemory.heapTotal,
        external: endMemory.external
      },
      timestamp: new Date()
    };

    this.monitor.recordMetrics(metrics);
  }
}

// Export singleton instance
export const pdfPerformanceMonitor = PDFPerformanceMonitor.getInstance();