'use client'

import Pusher from 'pusher-js'

// Singleton client instance (client-side only)
let pusherClientSingleton: Pusher | null = null

export function getPusherClient(): Pusher | null {
  // Ensure this never runs during SSR/prerender
  if (typeof window === 'undefined') return null

  const key = process.env.NEXT_PUBLIC_PUSHER_KEY
  const cluster = process.env.NEXT_PUBLIC_PUSHER_CLUSTER

  // If not configured, do not initialize
  if (!key || !cluster) {
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Pusher not configured: missing NEXT_PUBLIC_PUSHER_KEY or NEXT_PUBLIC_PUSHER_CLUSTER')
    }
    return null
  }

  if (!pusherClientSingleton) {
    pusherClientSingleton = new Pusher(key, { cluster, forceTLS: true })
  }
  return pusherClientSingleton
}

export function subscribeToTimesheetUpdates(timesheetId: string, callback: (data: any) => void) {
  const client = getPusherClient()
  if (!client) {
    // No-op cleanup
    return () => {}
  }

  const channel = client.subscribe(`timesheet-${timesheetId}`)
  channel.bind('status-update', callback)
  
  return () => {
    channel.unbind('status-update', callback)
    client.unsubscribe(`timesheet-${timesheetId}`)
  }
}
