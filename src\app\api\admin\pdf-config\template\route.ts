import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import { readFile } from 'fs/promises';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(_request: NextRequest) {
  try {
    const baseDir = path.join(process.cwd(), 'pdf-templates');
    // Prefer uploaded template if present, otherwise fall back to bundled file
    const uploaded = path.join(baseDir, 'uploaded-template.pdf');
    const fallback = path.join(baseDir, 'timesheet-template - sheet.pdf');
    let bytes: Buffer | Uint8Array;
    try {
      bytes = await readFile(uploaded);
    } catch {
      bytes = await readFile(fallback);
    }
    return new NextResponse(bytes as any, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Cache-Control': 'public, max-age=60',
      },
    });
  } catch (err) {
    console.error('Failed to read template PDF:', err);
    return NextResponse.json({ error: 'Template PDF not found' }, { status: 404 });
  }
}
