import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const pollSchema = z.object({
  shiftId: z.string(),
  lastUpdatedAt: z.string().datetime(),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { shiftId, lastUpdatedAt } = pollSchema.parse(body);

    const lastUpdate = new Date(lastUpdatedAt);

    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      select: { updatedAt: true },
    });

    if (!shift) {
      return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
    }

    const hasUpdates = shift.updatedAt > lastUpdate;

    return NextResponse.json({ hasUpdates });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    console.error('Polling error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}