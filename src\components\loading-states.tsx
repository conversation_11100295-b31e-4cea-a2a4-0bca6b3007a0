/**
 * Consistent Loading State Components
 * 
 * Provides standardized loading states across the application:
 * - Page-level loading
 * - Component-level loading
 * - List/grid loading
 * - Skeleton components
 * - Progress indicators
 */

"use client";

import React from 'react';
import { Loader2, Calendar, Briefcase, Users, Clock, Building2 } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface LoadingStateProps {
  message?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'pulse' | 'skeleton';
}

interface PageLoadingProps extends LoadingStateProps {
  title?: string;
  description?: string;
  showProgress?: boolean;
  progress?: number;
}

interface ListLoadingProps {
  count?: number;
  variant?: 'card' | 'table' | 'list';
  className?: string;
}

// ============================================================================
// BASIC LOADING COMPONENTS
// ============================================================================

export const LoadingSpinner: React.FC<LoadingStateProps> = ({
  message,
  className,
  size = 'md',
  variant = 'spinner'
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };
  
  const containerClasses = {
    sm: 'gap-2 text-sm',
    md: 'gap-3 text-base',
    lg: 'gap-4 text-lg'
  };
  
  return (
    <div className={cn(
      'flex items-center justify-center',
      containerClasses[size],
      className
    )}>
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
      {message && (
        <span className="text-muted-foreground">{message}</span>
      )}
    </div>
  );
};

export const LoadingDots: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('flex space-x-1', className)}>
    {[0, 1, 2].map((i) => (
      <div
        key={i}
        className="h-2 w-2 bg-primary rounded-full animate-pulse"
        style={{
          animationDelay: `${i * 0.2}s`,
          animationDuration: '1s'
        }}
      />
    ))}
  </div>
);

export const LoadingPulse: React.FC<LoadingStateProps> = ({
  message,
  className,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };
  
  return (
    <div className={cn('flex flex-col items-center justify-center gap-3', className)}>
      <div className={cn(
        'bg-primary/20 rounded-full animate-pulse',
        sizeClasses[size]
      )} />
      {message && (
        <p className="text-sm text-muted-foreground text-center">{message}</p>
      )}
    </div>
  );
};

// ============================================================================
// PAGE-LEVEL LOADING COMPONENTS
// ============================================================================

const PageLoader: React.FC<PageLoadingProps> = ({
  title = 'Loading...',
  description,
  message,
  showProgress = false,
  progress = 0,
  className
}) => (
  <div className={cn(
    'flex items-center justify-center min-h-[60vh] p-8',
    className
  )}>
    <div className="text-center space-y-4 max-w-md">
      <LoadingSpinner size="lg" />
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">{title}</h2>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
        {message && (
          <p className="text-sm text-muted-foreground">{message}</p>
        )}
      </div>
      
      {showProgress && (
        <div className="w-full max-w-xs mx-auto">
          <Progress value={progress} className="h-2" />
          <p className="text-xs text-muted-foreground mt-1">
            {progress}% complete
          </p>
        </div>
      )}
    </div>
  </div>
);

const FullPageLoader: React.FC<PageLoadingProps> = (props) => (
  <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50">
    <PageLoader {...props} className="min-h-screen" />
  </div>
);

// ============================================================================
// SKELETON COMPONENTS
// ============================================================================

const ShiftCardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn('animate-pulse', className)}>
    <CardContent className="p-6 space-y-4">
      <div className="flex items-start justify-between">
        <div className="space-y-2 flex-1">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-5 w-20" />
          </div>
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-28" />
        </div>
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
      
      <div className="space-y-2 bg-muted/50 rounded-lg p-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-4 w-20" />
          </div>
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
      
      <div className="flex items-center justify-between pt-2 border-t">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="h-6 w-16" />
      </div>
    </CardContent>
  </Card>
);

const JobCardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn('animate-pulse', className)}>
    <CardContent className="p-6 space-y-4">
      <div className="flex items-start justify-between">
        <div className="space-y-2 flex-1">
          <Skeleton className="h-6 w-48" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>
      
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
        </div>
        
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-3 border rounded-lg space-y-2">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-12" />
            </div>
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-5 w-16" />
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

const UserCardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn('animate-pulse', className)}>
    <CardContent className="p-6 space-y-4">
      <div className="flex items-center gap-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="h-6 w-16" />
      </div>
      
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>
      
      <div className="flex items-center justify-between pt-2 border-t">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-8 w-20" />
      </div>
    </CardContent>
  </Card>
);

const TableRowSkeleton: React.FC<{ 
  columns?: number;
  className?: string;
}> = ({ columns = 4, className }) => (
  <tr className={cn('animate-pulse', className)}>
    {Array.from({ length: columns }).map((_, i) => (
      <td key={i} className="p-4">
        <Skeleton className="h-4 w-full" />
      </td>
    ))}
  </tr>
);

// ============================================================================
// LIST/GRID LOADING COMPONENTS
// ============================================================================

const ListLoader: React.FC<ListLoadingProps> = ({
  count = 6,
  variant = 'card',
  className
}) => {
  const SkeletonComponent = {
    card: ShiftCardSkeleton,
    table: () => <TableRowSkeleton />,
    list: () => (
      <div className="flex items-center gap-4 p-4 border rounded-lg animate-pulse">
        <Skeleton className="h-10 w-10 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-1/3" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        <Skeleton className="h-6 w-16" />
      </div>
    )
  }[variant];
  
  if (variant === 'table') {
    return (
      <div className={className}>
        <table className="w-full">
          <tbody>
            {Array.from({ length: count }).map((_, i) => (
              <SkeletonComponent key={i} />
            ))}
          </tbody>
        </table>
      </div>
    );
  }
  
  const gridClasses = variant === 'card' 
    ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'
    : 'space-y-4';
  
  return (
    <div className={cn(gridClasses, className)}>
      {Array.from({ length: count }).map((_, i) => (
        <SkeletonComponent key={i} />
      ))}
    </div>
  );
};

const GridLoader: React.FC<ListLoadingProps & {
  columns?: { sm?: number; md?: number; lg?: number };
}> = ({ 
  count = 6, 
  variant = 'card',
  columns = { sm: 1, md: 2, lg: 3 },
  className 
}) => {
  const gridClasses = `grid grid-cols-${columns.sm} md:grid-cols-${columns.md} lg:grid-cols-${columns.lg} gap-6`;
  
  return (
    <div className={cn(gridClasses, className)}>
      {Array.from({ length: count }).map((_, i) => (
        variant === 'card' ? <ShiftCardSkeleton key={i} /> :
        variant === 'list' ? <UserCardSkeleton key={i} /> :
        <JobCardSkeleton key={i} />
      ))}
    </div>
  );
};

// ============================================================================
// SPECIALIZED LOADING COMPONENTS
// ============================================================================

const ShiftsPageLoader: React.FC<{ className?: string }> = ({ className }) => (
  <main className={cn('p-4 sm:p-6 lg:p-8', className)}>
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header Skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-48" />
        </div>
        <Skeleton className="h-10 w-40" />
      </div>
      
      {/* Filters Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-8 w-20" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Grid Skeleton */}
      <GridLoader count={6} variant="card" />
    </div>
  </main>
);

const JobsPageLoader: React.FC<{ className?: string }> = ({ className }) => (
  <main className={cn('p-4 sm:p-6 lg:p-8', className)}>
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-4 w-56" />
        </div>
        <Skeleton className="h-10 w-36" />
      </div>
      
      {/* Filters */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Jobs Grid */}
      <GridLoader count={6} variant="card" />
    </div>
  </main>
);

const DashboardLoader: React.FC<{ className?: string }> = ({ className }) => (
  <main className={cn('p-4 sm:p-6 lg:p-8', className)}>
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-8 w-16" />
                </div>
                <Skeleton className="h-8 w-8" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Content Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="animate-pulse">
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-3 border rounded">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-3 w-1/3" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </CardContent>
        </Card>
        
        <Card className="animate-pulse">
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  </main>
);

// ============================================================================
// INLINE LOADING COMPONENTS
// ============================================================================

const InlineLoader: React.FC<{
  message?: string;
  size?: 'sm' | 'md';
  className?: string;
}> = ({ message = 'Loading...', size = 'sm', className }) => (
  <div className={cn('flex items-center gap-2 text-muted-foreground', className)}>
    <Loader2 className={cn('animate-spin', size === 'sm' ? 'h-4 w-4' : 'h-5 w-5')} />
    <span className={size === 'sm' ? 'text-sm' : 'text-base'}>{message}</span>
  </div>
);

const ButtonLoader: React.FC<{
  loading?: boolean;
  children: React.ReactNode;
  className?: string;
}> = ({ loading, children, className }) => (
  <div className={cn('flex items-center gap-2', className)}>
    {loading && <Loader2 className="h-4 w-4 animate-spin" />}
    {children}
  </div>
);

// ============================================================================
// EXPORT ALL COMPONENTS
// ============================================================================

export {
  // Basic loaders
  LoadingSpinner as Spinner,
  LoadingDots as Dots,
  LoadingPulse as Pulse,
  
  // Page loaders
  PageLoader,
  FullPageLoader,
  
  // Skeleton components
  ShiftCardSkeleton,
  JobCardSkeleton,
  UserCardSkeleton,
  TableRowSkeleton,
  
  // List loaders
  ListLoader,
  GridLoader,
  
  // Specialized loaders
  ShiftsPageLoader,
  JobsPageLoader,
  DashboardLoader,
  
  // Inline loaders
  InlineLoader,
  ButtonLoader,
};

export default LoadingSpinner;
