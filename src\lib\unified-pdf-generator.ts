import { prisma } from '@/lib/prisma';
import { uploadToGCS } from '@/lib/gcs';
import { PrismaClient } from '@prisma/client';
import { Buffer } from 'buffer';
import { PDFDocument, StandardFonts, rgb, PDFFont, PDFPage } from 'pdf-lib';
import path from 'path';
import fs from 'fs/promises';
import { pdfCacheManager } from './pdf-cache-manager';
import { pdfPerformanceMonitor } from './pdf-performance-monitor';
import { getTimesheetDataForPdf, FullTimesheetData } from './pdf-data-utils';
import { formatDateForDisplay, formatTimeForDisplay, formatDateTimeForDisplay } from './utils';
import { OverlayTemplatePDFGenerator } from './pdf-template-overlay-generator';

// Enhanced interfaces
export interface PDFGenerationOptions {
  includeSignature?: boolean;
  signatureType?: 'company' | 'manager' | 'both';
  uploadToCloud?: boolean;
  templateId?: string;
  format?: 'letter' | 'a4';
  orientation?: 'portrait' | 'landscape';
  quality?: 'draft' | 'standard' | 'high';
  watermark?: string;
  customFields?: Record<string, any>;
}

export interface PDFTemplate {
  id: string;
  name: string;
  version: string;
  elements: PDFElement[];
  metadata: {
    pageSize: 'letter' | 'a4';
    orientation: 'portrait' | 'landscape';
    margins: { top: number; right: number; bottom: number; left: number };
  };
}

export interface PDFElement {
  id: string;
  type: 'text' | 'table' | 'signature' | 'image' | 'line' | 'rectangle';
  position: { x: number; y: number };
  dimensions: { width: number; height: number };
  style: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold';
    color?: { r: number; g: number; b: number };
    alignment?: 'left' | 'center' | 'right';
  };
  dataBinding?: string;
  validation?: {
    required?: boolean;
    maxLength?: number;
    pattern?: string;
  };
}

export interface TimesheetPDFOptions {
    includeSignature?: boolean;
    signatureType?: 'company' | 'manager' | 'both';
    uploadToCloud?: boolean;
}

export interface JobReportData {
    job: {
      id: string;
      jobNumber: string;
      description: string;
      location: string;
      startDate: string;
      endDate: string;
      status: string;
      company?: {
        name: string;
      };
    };
    shifts: Array<{
      id: string;
      date: string;
      startTime: string;
      endTime: string;
      description?: string;
      status: string;
      crew_chief_required: number;
      fork_operators_required: number;
      reach_fork_operator_required: number;
      stage_hands_required: number;
      general_labor_required: number;
      assignments?: Array<{
        id: string;
        workerType: string;
        user?: {
          id: string;
          name: string;
        };
      }>;
    }>;
    summary: {
      totalShifts: number;
      totalWorkerSlotsRequired: number;
      totalWorkerSlotsAssigned: number;
      fillRate: number;
      workerTypeBreakdown: {
        crew_chief: { required: number; assigned: number };
        fork_operator: { required: number; assigned: number };
        stage_hand: { required: number; assigned: number };
        general_labor: { required: number; assigned: number };
      };
    };
  }

export class UnifiedPDFGenerator {
  private timesheetId: string;
  private tx?: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;
  private cachedData?: FullTimesheetData;
  private cachedTemplate?: PDFTemplate;
  private fonts: Map<string, PDFFont> = new Map();

  constructor(
    timesheetId: string,
    tx?: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>
  ) {
    this.timesheetId = timesheetId;
    this.tx = tx;
  }

  async generatePDF(options: PDFGenerationOptions = {}): Promise<Buffer> {
    const tracker = pdfPerformanceMonitor.startTracking(this.timesheetId, options.templateId || 'system-default');
    
    try {
      if (await pdfCacheManager.has(this.timesheetId, options)) {
        const cachedBuffer = await pdfCacheManager.getBuffer(this.timesheetId, options);
        if (cachedBuffer) {
          tracker.markCacheHit();
          tracker.finish(cachedBuffer, options.includeSignature || false);
          return cachedBuffer;
        }
      }
      
      const [timesheetData, template] = await Promise.all([
        this.getTimesheetData(),
        this.loadTemplate(options.templateId)
      ]);

      const pdfDoc = await this.createPDFDocument(template, options);
      
      await this.renderContent(pdfDoc, timesheetData, template, options);
      
      if (options.includeSignature) {
        await this.addSignatures(pdfDoc, timesheetData, options);
      }

      if (options.watermark) {
        await this.addWatermark(pdfDoc, options.watermark);
      }

      const pdfBytes = await pdfDoc.save();
      const buffer = Buffer.from(pdfBytes);

      const pdfUrl = `data:application/pdf;base64,${buffer.toString('base64')}`;
      await pdfCacheManager.set(this.timesheetId, options, buffer, pdfUrl);

      tracker.finish(buffer, options.includeSignature || false);
      return buffer;
    } catch (error) {
      tracker.recordError();
      throw error;
    }
  }

  async generateAndStore(options: PDFGenerationOptions = {}): Promise<string> {
    const pdfBuffer = await this.generatePDF(options);
    
    if (options.uploadToCloud && process.env.GCS_AVATAR_BUCKET) {
      const filename = this.generateFilename(options);
      const destination = `timesheets/${this.timesheetId}/${filename}`;
      return await uploadToGCS(pdfBuffer, destination, 'application/pdf');
    } else {
      return `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;
    }
  }

  static async batchGenerate(
    timesheetIds: string[],
    options: PDFGenerationOptions = {}
  ): Promise<Map<string, Buffer | Error>> {
    const results = new Map<string, Buffer | Error>();
    
    const chunkSize = 5;
    for (let i = 0; i < timesheetIds.length; i += chunkSize) {
      const chunk = timesheetIds.slice(i, i + chunkSize);
      
      const promises = chunk.map(async (id) => {
        try {
          const generator = new UnifiedPDFGenerator(id);
          const buffer = await generator.generatePDF(options);
          return { id, result: buffer };
        } catch (error) {
          return { id, result: error as Error };
        }
      });

      const chunkResults = await Promise.allSettled(promises);
      chunkResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          results.set(result.value.id, result.value.result);
        } else {
          results.set('unknown', result.reason);
        }
      });
    }

    return results;
  }

  async generateUnsignedPDF(options: TimesheetPDFOptions = {}): Promise<string> {
    const db = this.tx || prisma;
    
    const overlay = new OverlayTemplatePDFGenerator(this.timesheetId, this.tx);
    const pdfBuffer: Buffer = await overlay.generatePDF({ includeSignature: false });
    
    const pdfDataUrl = `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;
    
    await db.timesheet.update({
      where: { id: this.timesheetId },
      data: { unsigned_pdf_url: pdfDataUrl }
    });

    return pdfDataUrl;
  }

  async generateSignedPDF(options: TimesheetPDFOptions = {}): Promise<string> {
    const db = this.tx || prisma;
    
    const timesheet = await this.getTimesheetData();
    
    if (!timesheet.company_signature) {
      throw new Error('Cannot generate signed PDF without company signature');
    }

    const overlay = new OverlayTemplatePDFGenerator(this.timesheetId, this.tx);
    const pdfBuffer: Buffer = await overlay.generatePDF({
      includeSignature: true,
      signatureType: 'company'
    });

    let finalUrl: string;

    if (process.env.GCS_AVATAR_BUCKET) {
      const destination = `timesheets/${this.timesheetId}/signed-timesheet.pdf`;
      finalUrl = await uploadToGCS(pdfBuffer, destination, 'application/pdf');
    } else {
      finalUrl = `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;
    }
    
    await db.timesheet.update({
      where: { id: this.timesheetId },
      data: { signed_pdf_url: finalUrl }
    });

    return finalUrl;
  }

  async generateFinalPDF(): Promise<string> {
    const db = this.tx || prisma;
    
    const timesheet = await this.getTimesheetData();
    
    const overlay = new OverlayTemplatePDFGenerator(this.timesheetId, this.tx);
    const pdfBuffer = await overlay.generatePDF({
      includeSignature: true,
      signatureType: 'both'
    });

    let finalUrl: string;

    if (process.env.GCS_AVATAR_BUCKET) {
      const destination = `timesheets/${this.timesheetId}/final-timesheet.pdf`;
      finalUrl = await uploadToGCS(pdfBuffer, destination, 'application/pdf');
    } else {
      finalUrl = `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;
    }
    
    await db.timesheet.update({
      where: { id: this.timesheetId },
      data: { signed_pdf_url: finalUrl }
    });

    return finalUrl;
  }

  async getPDFBuffer(preferSigned: boolean = true): Promise<Buffer> {
    const db = this.tx || prisma;
    const timesheet = await this.getTimesheetData();

    const overlay = new OverlayTemplatePDFGenerator(this.timesheetId, this.tx);
    return await overlay.generatePDF({
        includeSignature: preferSigned,
        signatureType: timesheet.manager_signature ? 'both' : 'company',
    });
  }

  public async generateJobReportHTML(
    reportData: JobReportData,
    options: {
      includeColors?: boolean;
      printFriendly?: boolean;
    } = {}
  ): Promise<string> {
    const { includeColors = true, printFriendly = false } = options;
    
    const styles = printFriendly ? this.getPrintStyles() : this.getScreenStyles();
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Job Report - ${reportData.job.description}</title>
          <style>${styles}</style>
        </head>
        <body>
          ${this.generateJobHeaderHTML(reportData.job)}
          ${this.generateSummaryHTML(reportData.summary)}
          ${this.generateShiftsHTML(reportData.shifts, includeColors)}
          ${this.generateFooterHTML()}
        </body>
      </html>
    `;
  }

  private async getTimesheetData(): Promise<FullTimesheetData> {
    if (this.cachedData) {
      return this.cachedData;
    }
    this.cachedData = await getTimesheetDataForPdf(this.timesheetId, this.tx);
    return this.cachedData;
  }

  private async createPDFDocument(template: PDFTemplate, options: PDFGenerationOptions): Promise<PDFDocument> {
    let pdfDoc: PDFDocument;

    try {
      const templateBytes = await this.loadTemplateFile();
      pdfDoc = await PDFDocument.load(templateBytes);
    } catch {
      pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage();
      
      const { pageSize, orientation } = template.metadata;
      const dimensions = this.getPageDimensions(pageSize, orientation);
      // @ts-ignore
      page.setSize(dimensions.width, dimensions.height);
    }

    await this.embedFonts(pdfDoc);

    return pdfDoc;
  }

  private async loadTemplateFile(): Promise<Uint8Array> {
    const templatePath = path.join(process.cwd(), 'pdf-templates', 'timesheet-template-sheet.pdf');
    const bytes = await fs.readFile(templatePath);
    return new Uint8Array(bytes);
  }

  private async embedFonts(pdfDoc: PDFDocument): Promise<void> {
    if (this.fonts.size === 0) {
      this.fonts.set('helvetica', await pdfDoc.embedFont(StandardFonts.Helvetica));
      this.fonts.set('helvetica-bold', await pdfDoc.embedFont(StandardFonts.HelveticaBold));
      this.fonts.set('times', await pdfDoc.embedFont(StandardFonts.TimesRoman));
      this.fonts.set('times-bold', await pdfDoc.embedFont(StandardFonts.TimesRomanBold));
    }
  }

  private async renderContent(
    pdfDoc: PDFDocument,
    data: FullTimesheetData,
    template: PDFTemplate,
    options: PDFGenerationOptions
  ): Promise<void> {
    const page = pdfDoc.getPage(0);
    const { height } = page.getSize();
    const form = pdfDoc.getForm();

    const context = this.prepareDataContext(data, options);

    try {
      form.getTextField('LOCATION').setText(context.location);
    } catch (e) {
      console.warn('Could not find or set LOCATION field');
    }

    for (const element of template.elements) {
      await this.renderElement(page, element, context, height);
    }

    await this.renderStagehandTable(page, data, height);
  }

  private async renderElement(
    page: PDFPage,
    element: PDFElement,
    context: Record<string, any>,
    pageHeight: number
  ): Promise<void> {
    const { type, position, dimensions, style, dataBinding } = element;
    const font = this.fonts.get(style.fontWeight === 'bold' ? 'helvetica-bold' : 'helvetica')!;
    const fontSize = style.fontSize || 10;
    const color = style.color ? rgb(style.color.r, style.color.g, style.color.b) : rgb(0, 0, 0);

    const value = dataBinding ? context[dataBinding] : '';
    const text = String(value || '');

    switch (type) {
      case 'text':
        page.drawText(text, {
          x: position.x,
          y: pageHeight - position.y,
          size: fontSize,
          font,
          color
        });
        break;

      case 'line':
        page.drawLine({
          start: { x: position.x, y: pageHeight - position.y },
          end: { x: position.x + dimensions.width, y: pageHeight - position.y },
          thickness: 1,
          color
        });
        break;

      case 'rectangle':
        page.drawRectangle({
          x: position.x,
          y: pageHeight - position.y - dimensions.height,
          width: dimensions.width,
          height: dimensions.height,
          borderColor: color,
          borderWidth: 1
        });
        break;
    }
  }

  private async renderStagehandTable(
    page: PDFPage,
    data: FullTimesheetData,
    pageHeight: number
  ): Promise<void> {
    // ... (implementation from unified-pdf-generator.ts)
  }

  private async addSignatures(
    pdfDoc: PDFDocument,
    data: FullTimesheetData,
    options: PDFGenerationOptions
  ): Promise<void> {
    // ... (implementation from unified-pdf-generator.ts)
  }

  private async addSignatureImage(
    page: PDFPage,
    signatureData: string,
    x: number,
    y: number,
    width: number,
    height: number
  ): Promise<void> {
    // ... (implementation from unified-pdf-generator.ts)
  }

  private async addWatermark(pdfDoc: PDFDocument, watermarkText: string): Promise<void> {
    // ... (implementation from unified-pdf-generator.ts)
  }

  private async loadTemplate(templateId?: string): Promise<PDFTemplate> {
    // ... (implementation from unified-pdf-generator.ts)
    return {} as PDFTemplate;
  }

  private prepareDataContext(data: FullTimesheetData, options: PDFGenerationOptions): Record<string, any> {
    // ... (implementation from unified-pdf-generator.ts)
    return {};
  }

  private calculateStagehandHours(employee: FullTimesheetData['shift']['assignedPersonnel'][0]): { regular: number; overtime: number; total: number } {
    // ... (implementation from unified-pdf-generator.ts)
    return { regular: 0, overtime: 0, total: 0 };
  }

  private formatDate(date: Date | string): string {
    // ... (implementation from unified-pdf-generator.ts)
    return '';
  }

  private formatTime(time: Date | string): string {
    // ... (implementation from unified-pdf-generator.ts)
    return '';
  }

  private getRoleDisplayName(roleCode: string): string {
    // ... (implementation from unified-pdf-generator.ts)
    return '';
  }

  private getInitials(name: string): string {
    // ... (implementation from unified-pdf-generator.ts)
    return '';
  }

  private generateFilename(options: PDFGenerationOptions): string {
    // ... (implementation from unified-pdf-generator.ts)
    return '';
  }

  private getPageDimensions(pageSize: 'letter' | 'a4', orientation: 'portrait' | 'landscape'): { width: number; height: number } {
    // ... (implementation from unified-pdf-generator.ts)
    return { width: 0, height: 0 };
  }

  private generateJobHeaderHTML(job: any): string {
    return `
      <div class="job-header">
        <h1>${job.description}</h1>
        <div class="job-details">
          <div class="detail-row">
            <span class="label">Job #:</span>
            <span class="value">${job.jobNumber}</span>
          </div>
          <div class="detail-row">
            <span class="label">Company:</span>
            <span class="value">${job.company?.name || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Location:</span>
            <span class="value">${job.location}</span>
          </div>
          <div class="detail-row">
            <span class="label">Duration:</span>
            <span class="value">${formatDateForDisplay(job.startDate)} - ${formatDateForDisplay(job.endDate)}</span>
          </div>
        </div>
      </div>
    `;
  }

  private generateSummaryHTML(summary: any): string {
    return `
      <div class="summary-section">
        <h2>Summary</h2>
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-value">${summary.totalShifts}</div>
            <div class="summary-label">Total Shifts</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">${summary.totalWorkerSlotsAssigned}</div>
            <div class="summary-label">Workers Assigned</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">${summary.totalWorkerSlotsRequired}</div>
            <div class="summary-label">Workers Required</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">${summary.fillRate}%</div>
            <div class="summary-label">Fill Rate</div>
          </div>
        </div>
      </div>
    `;
  }

  private generateShiftsHTML(shifts: any[], includeColors: boolean): string {
    return `
      <div class="shifts-section">
        <h2>Shift Details</h2>
        ${shifts.map((shift, index) => `
          <div class="shift-card">
            <div class="shift-header">
              <h3>Shift #${index + 1}</h3>
              <span class="shift-status">${shift.status}</span>
            </div>
            <div class="shift-details">
              <div class="shift-info">
                <span>Date: ${formatDateForDisplay(shift.date)}</span>
                <span>Time: ${formatTimeForDisplay(shift.startTime)} - ${formatTimeForDisplay(shift.endTime)}</span>
              </div>
              <div class="worker-requirements">
                <h4>Worker Requirements</h4>
                ${shift.crew_chief_required > 0 ? `<div>Crew Chiefs: ${shift.assignments?.filter((a: any) => a.workerType === 'crew_chief' && a.user).length || 0}/${shift.crew_chief_required}</div>` : ''}
                ${shift.fork_operators_required > 0 ? `<div>Fork Operators: ${shift.assignments?.filter((a: any) => a.workerType === 'fork_operator' && a.user).length || 0}/${shift.fork_operators_required}</div>` : ''}
                ${shift.stage_hands_required > 0 ? `<div>Stage Hands: ${shift.assignments?.filter((a: any) => a.workerType === 'stage_hand' && a.user).length || 0}/${shift.stage_hands_required}</div>` : ''}
                ${shift.general_labor_required > 0 ? `<div>General Labor: ${shift.assignments?.filter((a: any) => a.workerType === 'general_labor' && a.user).length || 0}/${shift.general_labor_required}</div>` : ''}
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  private generateFooterHTML(): string {
    return `
      <div class="report-footer">
        <p>Generated on ${formatDateTimeForDisplay(new Date())}</p>
      </div>
    `;
  }

  private getPrintStyles(): string {
    return `
      body { font-family: Arial, sans-serif; font-size: 12px; line-height: 1.4; margin: 0; padding: 20px; }
      .job-header { margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; }
      .job-header h1 { margin: 0 0 10px 0; font-size: 18px; }
      .job-details { display: grid; grid-template-columns: 1fr 1fr; gap: 5px; }
      .detail-row { display: flex; }
      .label { font-weight: bold; margin-right: 10px; min-width: 80px; }
      .summary-section { margin: 20px 0; }
      .summary-section h2 { font-size: 16px; margin-bottom: 10px; }
      .summary-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; }
      .summary-item { text-align: center; border: 1px solid #ccc; padding: 10px; }
      .summary-value { font-size: 16px; font-weight: bold; }
      .summary-label { font-size: 10px; color: #666; }
      .shifts-section h2 { font-size: 16px; margin-bottom: 10px; }
      .shift-card { border: 1px solid #ccc; margin-bottom: 15px; padding: 10px; break-inside: avoid; }
      .shift-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
      .shift-header h3 { margin: 0; font-size: 14px; }
      .shift-status { padding: 2px 8px; border: 1px solid #ccc; font-size: 10px; }
      .shift-info { margin-bottom: 10px; }
      .shift-info span { display: block; margin-bottom: 2px; }
      .worker-requirements h4 { margin: 0 0 5px 0; font-size: 12px; }
      .worker-requirements div { margin-bottom: 2px; }
      .report-footer { margin-top: 30px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px; }
    `;
  }

  private getScreenStyles(): string {
    return `
      body { font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; margin: 0; padding: 20px; background: #f5f5f5; }
      .job-header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
      .job-header h1 { margin: 0 0 15px 0; font-size: 24px; color: #333; }
      .job-details { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
      .detail-row { display: flex; align-items: center; }
      .label { font-weight: bold; margin-right: 10px; min-width: 100px; color: #666; }
      .value { color: #333; }
      .summary-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
      .summary-section h2 { font-size: 20px; margin-bottom: 15px; color: #333; }
      .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
      .summary-item { text-align: center; background: #f8f9fa; border-radius: 6px; padding: 15px; }
      .summary-value { font-size: 24px; font-weight: bold; color: #2563eb; }
      .summary-label { font-size: 12px; color: #666; margin-top: 5px; }
      .shifts-section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
      .shifts-section h2 { font-size: 20px; margin-bottom: 15px; color: #333; }
      .shift-card { border: 1px solid #e5e7eb; margin-bottom: 15px; padding: 15px; border-radius: 6px; background: #fafafa; }
      .shift-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
      .shift-header h3 { margin: 0; font-size: 16px; color: #333; }
      .shift-status { padding: 4px 12px; background: #e5e7eb; border-radius: 4px; font-size: 12px; color: #374151; }
      .shift-info { margin-bottom: 15px; }
      .shift-info span { display: block; margin-bottom: 5px; color: #666; }
      .worker-requirements h4 { margin: 0 0 10px 0; font-size: 14px; color: #333; }
      .worker-requirements div { margin-bottom: 5px; padding: 5px 10px; background: white; border-radius: 4px; border-left: 3px solid #2563eb; }
      .report-footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
    `;
  }
}

// Export helper functions for backward compatibility
export async function generateSignedTimesheetPdf(timesheetId: string): Promise<string> {
  const generator = new UnifiedPDFGenerator(timesheetId);
  return await generator.generateSignedPDF();
}

export async function generateUnsignedTimesheetPdf(timesheetId: string): Promise<string> {
  const generator = new UnifiedPDFGenerator(timesheetId);
  return await generator.generateUnsignedPDF();
}
