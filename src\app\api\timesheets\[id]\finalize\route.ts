import { NextResponse, NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { TimesheetStatus, UserRole } from '@/lib/types';
import { generateTimesheetExcel } from '@/lib/excel-generator';
import { promises as fs } from 'fs';
import path from 'path';

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const shiftId = params.id;
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        assignedPersonnel: {
          include: {
            user: true,
            timeEntries: true,
          },
        },
        job: {
          include: {
            company: true,
          },
        },
      },
    });

    if (!shift) {
      return new NextResponse("Shift not found", { status: 404 });
    }

    const isCrewChief = shift.assignedPersonnel.some(
      (p) => p.userId === user.id && p.roleCode === 'CC'
    );

    if (user.role !== UserRole.Admin && !isCrewChief) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const timesheet = await prisma.timesheet.create({
        data: {
            shiftId: shift.id,
            status: TimesheetStatus.PENDING_COMPANY_APPROVAL,
            submittedBy: user.id,
        }
    });

    // Generate unsigned Excel file
    const workbook = await generateTimesheetExcel(timesheet.id, user);
    const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer;

    // Save the file
    const filename = `timesheet-${timesheet.id}.xlsx`;
    const filepath = path.join(process.cwd(), 'public', 'uploads', filename);
    await fs.writeFile(filepath, buffer);

    await prisma.timesheet.update({
        where: { id: timesheet.id },
        data: {
            unsigned_excel_url: `/uploads/${filename}`,
        },
    });

    return NextResponse.json({
      success: true,
      timesheetId: timesheet.id,
      message: 'Timesheet finalized and is pending company approval.',
    });
  } catch (error) {
    console.error("[TIMESHEET_FINALIZE]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
