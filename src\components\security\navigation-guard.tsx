'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useUser } from "@/hooks/use-user";
import { UserRole } from '@prisma/client';
import { getDashboardPath } from '@/lib/routing';

// Role-based access map
const roleAccess: Record<UserRole, string[]> = {
  [UserRole.Admin]: [''], // unrestricted
  [UserRole.Manager]: [''], // unrestricted (API will filter where needed)
  [UserRole.CrewChief]: [
    '/', '/dashboard', '/crew-chief', '/jobs', '/shifts',
    '/timesheets', '/clients', '/stagehands',
    '/documents', '/profile'
  ],
  [UserRole.StageHand]: [
    '/dashboard', '/employee', '/jobs', '/shifts',
    '/companies', '/documents', '/profile'
  ],
  [UserRole.CompanyUser]: [
    '/dashboard', '/company', '/jobs', '/shifts',
    '/companies', '/documents', '/profile'
  ],
  [UserRole.PendingUser]: [
    '/dashboard', '/profile'
  ],
};

// Core access checker
function canAccess(role: UserRole, path: string): boolean {
  // Admin & Manager have unrestricted access
  if (role === UserRole.Admin || role === UserRole.Manager) {
    return true;
  }

  const allowedPaths = roleAccess[role] || [];
  return allowedPaths.some(rule => path.startsWith(rule));
}

interface NavigationGuardProps {
  children: React.ReactNode;
}

export function NavigationGuard({ children }: NavigationGuardProps) {
  const { user, isLoading } = useUser();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const isPublicRoute = ['/login', '/register', '/'].includes(pathname);

    if (isLoading) {
      return; // wait for session
    }

    // No user → must go to login (unless public)
    if (!user) {
      if (!isPublicRoute) {
        router.push('/login');
      }
      return;
    }

    // Logged in → block public routes
    if (isPublicRoute) {
      router.push(getDashboardPath(user.role as UserRole, user.companyId));
      return;
    }

    // Role-based check
    if (!canAccess(user.role as UserRole, pathname)) {
      router.push(getDashboardPath(user.role as UserRole, user.companyId));
    }
  }, [user, isLoading, pathname, router]);

  // Loading spinner
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Block rendering until redirect is resolved
  if (!user && pathname !== '/login' && pathname !== '/register' && pathname !== '/') {
    return null;
  }

  return <>{children}</>;
}

/**
 * Hook to check if user can access a specific route
 */
export function useRouteAccess() {
  const { user } = useUser();

  const canAccessRoute = (path: string): boolean => {
    if (!user) return false;
    return canAccess(user.role as UserRole, path);
  };

  const getRedirectPath = (path: string): string => {
    if (!user) return '/login';
    return getDashboardPath(user.role as UserRole, user.companyId);
  };

  return {
    canAccessRoute,
    getRedirectPath,
  };
}

/**
 * Component to conditionally render navigation links based on permissions
 */
interface ConditionalLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  fallback?: React.ReactNode;
}

export function ConditionalLink({ href, children, className, fallback }: ConditionalLinkProps) {
  const { canAccessRoute } = useRouteAccess();

  if (!canAccessRoute(href)) {
    return <>{fallback}</>;
  }

  return (
    <a href={href} className={className}>
      {children}
    </a>
  );
}

/**
 * Enhanced navigation component with role-based filtering
 */
interface SecureNavLinkProps {
  href: string;
  label: string;
  icon?: React.ComponentType<any>;
  allowedRoles: UserRole[];
  children?: React.ReactNode;
  className?: string;
}

export function SecureNavLink({ 
  href, 
  label, 
  icon: Icon, 
  allowedRoles, 
  children, 
  className 
}: SecureNavLinkProps) {
  const { user } = useUser();

  if (!user || !allowedRoles.includes(user.role as UserRole)) {
    return null;
  }

  return (
    <a href={href} className={className}>
      {Icon && <Icon className="h-4 w-4" />}
      {label}
      {children}
    </a>
  );
}

/**
 * Utility function to filter navigation items based on user role
 */
export function filterNavItems<T extends { roles: UserRole[] }>(
  items: T[],
  userRole?: UserRole | null
): T[] {
  if (!userRole) return [];
  return items.filter(item => item.roles.includes(userRole));
}

/**
 * Route protection utilities
 */
export const RouteProtection = {
  requiresAuth: (path: string): boolean => {
    const publicRoutes = ['/login', '/register', '/'];
    return !publicRoutes.includes(path);
  },

  isAdminOnly: (path: string): boolean => {
    const adminRoutes = ['/admin', '/stagehands', '/companies', '/import'];
    return adminRoutes.some(route => path.startsWith(route));
  },

  requiresManagement: (path: string): boolean => {
    const managementRoutes = ['/jobs'];
    return managementRoutes.some(route => path.startsWith(route));
  },

  getDashboardPath,
};
