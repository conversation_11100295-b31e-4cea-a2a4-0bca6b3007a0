import { generateTimesheetExcel } from './excel-generator';
import { AuthenticatedUser } from '@/lib/types';
import libre from 'libreoffice-convert';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';

const libreConvertAsync = promisify(libre.convert);

export class PdfFromExcelGenerator {
  private timesheetId: string;

  constructor(timesheetId: string) {
    this.timesheetId = timesheetId;
  }

  async generatePDF(user: AuthenticatedUser): Promise<Buffer> {
    const workbook = await generateTimesheetExcel(this.timesheetId, user);
    const excelBuffer = await workbook.xlsx.writeBuffer();

    const tempDir = os.tmpdir();
    const tempExcelPath = path.join(tempDir, `timesheet-${this.timesheetId}.xlsx`);
    const tempPdfPath = path.join(tempDir, `timesheet-${this.timesheetId}.pdf`);
    
    await fs.writeFile(tempExcelPath, excelBuffer as any);

    try {
      const excelFileBuffer = await fs.readFile(tempExcelPath);
      const pdfBuf = await libreConvertAsync(excelFileBuffer, '.pdf', undefined);
      await fs.writeFile(tempPdfPath, pdfBuf);
      return pdfBuf;
    } catch (error) {
      console.error('Error converting Excel to PDF:', error);
      throw new Error('Failed to convert Excel to PDF. Please ensure LibreOffice is installed and accessible in your system PATH.');
    } finally {
      // Clean up temporary files
      await fs.unlink(tempExcelPath).catch(err => console.error('Failed to delete temp Excel file:', err));
      await fs.unlink(tempPdfPath).catch(err => console.error('Failed to delete temp PDF file:', err));
    }
  }
}
