'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrendingUp, CheckCircle } from 'lucide-react';

interface RecommendationsTabProps {
  recommendations: string[];
}

export const RecommendationsTab: React.FC<RecommendationsTabProps> = ({ recommendations }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>System Recommendations</CardTitle>
        <CardDescription>Automated suggestions for system optimization</CardDescription>
      </CardHeader>
      <CardContent>
        {recommendations.length > 0 ? (
          <div className="space-y-3">
            {recommendations.map((recommendation, index) => (
              <Alert key={index}>
                <TrendingUp className="h-4 w-4" />
                <AlertDescription>{recommendation}</AlertDescription>
              </Alert>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <p className="text-lg font-medium">System is running optimally!</p>
            <p className="text-gray-600">No recommendations at this time.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
