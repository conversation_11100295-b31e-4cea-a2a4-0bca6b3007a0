import React, { createContext, useContext } from 'react';
import type { RoleCode, Assignment as WorkerAssignment, User, TimeEntry as TimeEntryType } from '@/lib/types';

interface TimeTrackingContextProps {
    isTimeTrackingDisabled: boolean;
    isAnyAssignmentUpdating: boolean;
    enableTimeTracking: boolean;
    timeEntryColumns: { number: number; show: boolean }[];
    availableUsers: User[];
    onAssignWorker: (assignmentId: string, userId: string | null, roleCode?: RoleCode) => void;
    onTimeUpdate: (entryId: string, field: 'clockIn' | 'clockOut', value: string) => void;
    actionHandlers: {
        handleClockInClick: (assignmentId: string) => void;
        handleClockOutClick: (assignmentId: string) => void;
        handleEndShiftClick: (assignmentId: string) => void;
        handleNoShowClick: (assignmentId: string) => void;
    };
    isUpdatingAssignment: Record<string, boolean>;
}

const TimeTrackingContext = createContext<TimeTrackingContextProps | undefined>(undefined);

export const TimeTrackingProvider = TimeTrackingContext.Provider;

export const useTimeTracking = () => {
    const context = useContext(TimeTrackingContext);
    if (!context) {
        throw new Error('useTimeTracking must be used within a TimeTrackingProvider');
    }
    return context;
};
