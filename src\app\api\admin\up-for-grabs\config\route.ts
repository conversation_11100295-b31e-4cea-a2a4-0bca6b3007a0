export const dynamic = 'force-dynamic';
export const revalidate = 0;

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || user.role !== UserRole.Admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const cfg = await prisma.upForGrabsConfig.findFirst();
    if (!cfg) {
      return NextResponse.json({ allowedUserIds: [], allowedUsers: [], notifyCrewChiefs: false, notifyEmployees: true });
    }

    const allowedUsers = await prisma.user.findMany({
      where: { id: { in: cfg.allowedUserIds } },
      select: { id: true, name: true, email: true },
    });

    return NextResponse.json({ ...cfg, allowedUsers });
  } catch (error) {
    console.error('Error loading up-for-grabs config', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || user.role !== 'Admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const allowedUserIds: string[] = Array.isArray(body.allowedUserIds) ? body.allowedUserIds : [];
    const notifyCrewChiefs: boolean = !!body.notifyCrewChiefs;
    const notifyEmployees: boolean = body.notifyEmployees !== false;

    const data = { allowedUserIds, notifyCrewChiefs, notifyEmployees };

    const result = await prisma.$transaction(async (tx) => {
      const existing = await tx.upForGrabsConfig.findFirst();
      if (existing) {
        return tx.upForGrabsConfig.update({ where: { id: existing.id }, data });
      } else {
        return tx.upForGrabsConfig.create({ data });
      }
    });

    return NextResponse.json({ success: true, ...result });
  } catch (error) {
    console.error('Error saving up-for-grabs config', error);
    const message = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
