export interface PDFElement {
  id: string;
  type: 'text' | 'table' | 'signature' | 'image' | 'date' | 'checkbox'; // Added date and checkbox from FormField
  label: string;
  x: number;
  y: number;
  width: number;
  height: number;
  dataKey?: string;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold';
  required: boolean;
  value?: string; // From FormField
  placeholder?: string; // From FormField
}
