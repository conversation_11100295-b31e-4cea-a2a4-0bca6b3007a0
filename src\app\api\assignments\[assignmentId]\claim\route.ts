import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

export async function POST(
  req: Request,
  { params }: { params: { assignmentId: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  const { assignmentId } = params;
  const userId = session.user.id;

  try {
    const result = await prisma.$transaction(async (tx) => {
      const assignment = await tx.assignedPersonnel.findUnique({
        where: { id: assignmentId },
        include: { shift: true },
      });

      if (!assignment) {
        throw new Error('Assignment not found');
      }

      if (assignment.status !== 'UpForGrabs') {
        throw new Error('Shift is not up for grabs');
      }

      // Prevent assigning a user to a shift they are already assigned to
      const existingAssignment = await tx.assignedPersonnel.findFirst({
        where: {
          shiftId: assignment.shiftId,
          userId: userId,
        },
      });

      if (existingAssignment) {
        throw new Error('You are already assigned to this shift');
      }

      // Check for scheduling conflicts
      const conflictingShift = await tx.shift.findFirst({
        where: {
          assignedPersonnel: { some: { userId } },
          startTime: { lt: assignment.shift.endTime },
          endTime: { gt: assignment.shift.startTime },
          id: { not: assignment.shiftId },
        },
      });

      if (conflictingShift) {
        throw new Error('You have a conflicting shift at this time');
      }

      const updatedAssignment = await tx.assignedPersonnel.update({
        where: { id: assignmentId },
        data: {
          userId,
          status: 'Assigned',
          claimedById: userId,
          claimedAt: new Date(),
        },
      });

      return updatedAssignment;
    });

    return NextResponse.json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }
    if (error.message.includes('not up for grabs') || error.message.includes('already assigned') || error.message.includes('conflicting shift')) {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }
    console.error('Failed to claim assignment:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
