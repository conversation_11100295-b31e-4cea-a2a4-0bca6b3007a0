"use client";

import React from 'react';
import { JobReport } from '@/components/job-report';

const mockJob = {
  id: "test-job-1",
  jobNumber: "JOB-2024-001",
  title: "Convention Center Setup - Tech Conference 2024",
  description: "Annual technology conference setup",
  location: "Los Angeles Convention Center, Hall A",
  startDate: "2024-01-15T00:00:00Z",
  endDate: "2024-01-18T00:00:00Z",
  status: "active",
  company: {
    name: "TechCorp Events",
    id: "company-1"
  }
};

const mockShifts = [
  {
    id: "shift-1",
    date: "2024-01-15",
    startTime: "2024-01-15T06:00:00Z",
    endTime: "2024-01-15T14:00:00Z",
    description: "Initial setup and stage construction",
    status: "completed",
    crew_chief_required: 1,
    fork_operators_required: 2,
    stage_hands_required: 4,
    general_labor_required: 6,
    assignments: [
      {
        id: "assign-1",
        workerType: "crew_chief",
        user: { id: "user-1", name: "<PERSON>", email: "<EMAIL>" }
      },
    ]
  },
];

export default function SimpleTestPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Simple Job Report Test</h1>
      <JobReport 
        job={mockJob} 
        shifts={mockShifts} 
      />
    </div>
  );
}
