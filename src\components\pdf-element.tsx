'use client';

import React from 'react';
import { PDFElement as PDFElementType } from '@/types/pdf';
import { Move } from 'lucide-react';

interface PdfElementProps {
  element: PDFElementType;
  onMouseDown: (e: React.MouseEvent, element: PDFElementType) => void;
  isSelected: boolean;
  zoom: number;
}

const PdfElement: React.FC<PdfElementProps> = ({ element, onMouseDown, isSelected, zoom }) => {
  const pointsToPixels = (points: number) => points * (96 / 72) * zoom;

  return (
    <div
      onMouseDown={(e) => onMouseDown(e, element)}
      style={{
        position: 'absolute',
        left: pointsToPixels(element.x),
        top: pointsToPixels(element.y),
        width: pointsToPixels(element.width),
        height: pointsToPixels(element.height),
        border: `1px solid ${isSelected ? 'blue' : 'gray'}`,
        cursor: 'move',
      }}
    >
      <Move className="absolute top-0 right-0 w-4 h-4 bg-gray-200 p-1" />
      <div className="p-1 text-xs">{element.label}</div>
    </div>
  );
};

export default PdfElement;
