import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { differenceInHours } from 'date-fns';
import { WorkerStatus } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: { shiftId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { shiftId } = params;

    const assignment = await prisma.assignedPersonnel.findFirst({
      where: {
        shiftId,
        userId: user.id,
      },
      include: {
        shift: {
          include: {
            job: {
              include: {
                company: true,
              },
            },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json({ error: 'You are not assigned to this shift' }, { status: 403 });
    }

    const now = new Date();
    const shiftStartTime = new Date(assignment.shift.startTime);
    const hoursUntilStart = differenceInHours(shiftStartTime, now);

    // If more than 24 hours away, simply unassign
    if (hoursUntilStart > 24) {
      await prisma.assignedPersonnel.delete({ where: { id: assignment.id } });
      return NextResponse.json({ message: 'You have been successfully removed from the shift.' });
    }

    // If user is already unassigned, this is an invalid state
    if (!assignment.userId) {
      return NextResponse.json({ error: 'Assignment is already up for grabs or unassigned.' }, { status: 400 });
    }

    // Move to Up for Grabs and record offer metadata
    const offered = await prisma.assignedPersonnel.update({
      where: { id: assignment.id },
      data: {
        status: WorkerStatus.UpForGrabs,
        userId: null, // Correctly unassign the user
        offeredById: user.id,
        offeredAt: new Date(),
      },
      include: {
        shift: { include: { job: { include: { company: true } } } },
      },
    });

    // Load global config for targeting notifications
    const config = await prisma.upForGrabsConfig.findFirst();

    // Build recipient list
    let recipients = [] as { id: string }[];
    if (config && config.allowedUserIds.length > 0) {
      recipients = await prisma.user.findMany({ where: { id: { in: config.allowedUserIds }, isActive: true } });
    } else {
      // Fallback: notify employees and crew chiefs
      recipients = await prisma.user.findMany({
        where: { isActive: true, OR: [{ role: 'Employee' }, { role: 'CrewChief' }] },
        select: { id: true },
      });
    }

    const assignmentId = offered.id;
    const acceptUrl = `/api/shifts/${shiftId}/claim`; // expects { assignmentId }
    const declineUrl = `/api/shifts/${shiftId}/decline`; // expects { assignmentId }

    const title = 'Shift Available to Claim';
    const msg = `Job: ${offered.shift.job.name} | ${offered.shift.description ?? 'No description'} | Client: ${offered.shift.job.company.name} | Date: ${offered.shift.date.toLocaleDateString()} ${offered.shift.startTime.toLocaleTimeString()} | Location: ${offered.shift.job.location ?? 'N/A'}`;

    // Insert notifications with metadata for actions
    await prisma.$transaction(
      recipients.map(r =>
        prisma.notification.create({
          data: {
            userId: r.id,
            type: 'SHIFT_UP_FOR_GRABS',
            title,
            message: msg,
            relatedShiftId: shiftId,
            metadata: {
              assignmentId,
              acceptUrl,
              declineUrl,
              roleCode: offered.roleCode,
            },
          },
        })
      )
    );

    return NextResponse.json({ message: 'Shift offered up. Notified eligible employees.' });

  } catch (error) {
    console.error('Error dropping (offering) shift:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
