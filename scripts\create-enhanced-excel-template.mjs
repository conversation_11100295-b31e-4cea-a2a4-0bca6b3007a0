shift
 * Script to create an enhanced Excel template with professional styling
 * and proper field alignment for timesheet data
 */

import ExcelJS from 'exceljs';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Enhanced styling configuration
const STYLING = {
  titleFont: { name: '<PERSON><PERSON><PERSON>', size: 16, bold: true },
  headerFont: { name: '<PERSON><PERSON><PERSON>', size: 12, bold: true },
  bodyFont: { name: '<PERSON><PERSON><PERSON>', size: 10 },
  colors: {
    headerBg: 'FF2F5597',      // Professional blue
    alternateRowBg: 'FFF8F9FA', // Light gray
    borderColor: 'FFD1D5DB',    // Light border
    totalRowBg: 'FFE5E7EB',     // Slightly darker for totals
    titleBg: 'FF1E3A8A',       // Darker blue for title
  }
};

async function createEnhancedTemplate() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Timesheet', {
    pageSetup: {
      paperSize: 9, // A4
      orientation: 'portrait',
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      },
      printArea: 'A1:L50'
    }
  });

  // Set column widths for optimal layout
  worksheet.columns = [
    { width: 20 }, // A - Employee Name
    { width: 15 }, // B - Role/Customer
    { width: 12 }, // C - Clock In 1/Job Info
    { width: 12 }, // D - Clock Out 1
    { width: 12 }, // E - Clock In 2/Start Time
    { width: 12 }, // F - Clock Out 2/End Time
    { width: 12 }, // G - Clock In 3/Location
    { width: 12 }, // H - Clock Out 3
    { width: 12 }, // I - Regular Hours
    { width: 12 }, // J - Overtime Hours/Location
    { width: 12 }, // K - Total Hours
    { width: 25 }, // L - Notes/Job Number
    { width: 15 }, // M - Additional info
    { width: 15 }, // N - Additional info
  ];

  // ============================================================================
  // TITLE SECTION
  // ============================================================================
  
  const titleCell = worksheet.getCell('A1');
  titleCell.value = 'PROFESSIONAL TIMESHEET REPORT';
  titleCell.font = { ...STYLING.titleFont, color: { argb: 'FFFFFFFF' } };
  titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
  titleCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: STYLING.colors.titleBg }
  };
  
  // Merge title cells
  worksheet.mergeCells('A1:N1');
  worksheet.getRow(1).height = 25;
  
  // Add title border
  const titleRange = worksheet.getCell('A1');
  titleRange.border = {
    top: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
    left: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
    bottom: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
    right: { style: 'medium', color: { argb: STYLING.colors.borderColor } }
  };

  // ============================================================================
  // HEADER INFORMATION SECTION
  // ============================================================================
  
  // Job Number (L2 - matches Excel generator config)
  worksheet.getCell('K2').value = 'Job #:';
  worksheet.getCell('K2').font = STYLING.headerFont;
  worksheet.getCell('L2').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Company Name (B5 - matches config)
  worksheet.getCell('A5').value = 'Company:';
  worksheet.getCell('A5').font = STYLING.headerFont;
  worksheet.getCell('B5').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('B5:F5');
  
  // Company Phone (M6 - matches config)
  worksheet.getCell('L6').value = 'Phone:';
  worksheet.getCell('L6').font = STYLING.headerFont;
  worksheet.getCell('M6').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Job Name (J5 - matches config)
  worksheet.getCell('I5').value = 'Job:';
  worksheet.getCell('I5').font = STYLING.headerFont;
  worksheet.getCell('J5').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('J5:K5');
  
  // Job Description (I6 - matches config)
  worksheet.getCell('H6').value = 'Description:';
  worksheet.getCell('H6').font = STYLING.headerFont;
  worksheet.getCell('I6').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('I6:K6');
  
  // Job Start Date (M5 - matches config)
  worksheet.getCell('L5').value = 'Start:';
  worksheet.getCell('L5').font = STYLING.headerFont;
  worksheet.getCell('M5').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Job End Date (N5 - matches config)
  worksheet.getCell('N5').value = 'End:';
  worksheet.getCell('N5').font = STYLING.headerFont;
  worksheet.getCell('N5').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Shift Date (B6 - matches config)
  worksheet.getCell('A6').value = 'Shift Date:';
  worksheet.getCell('A6').font = STYLING.headerFont;
  worksheet.getCell('B6').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Shift Start Time (C6 - matches config)
  worksheet.getCell('C6').value = 'Start:';
  worksheet.getCell('C6').font = STYLING.headerFont;
  worksheet.getCell('C6').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Shift End Time (D6 - matches config)
  worksheet.getCell('D6').value = 'End:';
  worksheet.getCell('D6').font = STYLING.headerFont;
  worksheet.getCell('D6').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  
  // Shift Description (E9 - matches config)
  worksheet.getCell('A9').value = 'Shift Description:';
  worksheet.getCell('A9').font = STYLING.headerFont;
  worksheet.getCell('E9').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('E9:N9');
  
  // Shift Notes (E10 - matches config)
  worksheet.getCell('A10').value = 'Notes:';
  worksheet.getCell('A10').font = STYLING.headerFont;
  worksheet.getCell('E10').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('E10:N10');

  // ============================================================================
  // EMPLOYEE DATA TABLE
  // ============================================================================
  
  // Table headers (starting at row 12 to match config startRow: 12)
  const headerRow = 12;
  const headers = [
    'Employee Name',      // A (nameColumn: 1)
    'Worker Type',        // B (workerTypeColumn: 2)
    'Clock In 1',         // C (in1Column: 3)
    'Clock Out 1',        // D (out1Column: 4)
    'Clock In 2',         // E (in2Column: 5)
    'Clock Out 2',        // F (out2Column: 6)
    'Clock In 3',         // G (in3Column: 7)
    'Clock Out 3',        // H (out3Column: 8)
    'Regular Hours',      // I (regularColumn: 9)
    'Overtime Hours',     // J (otColumn: 10)
    'Total Hours',        // K (totalColumn: 11)
    'Notes'               // L (notesColumn: 12)
  ];

  headers.forEach((header, index) => {
    const cell = worksheet.getCell(headerRow, index + 1);
    cell.value = header;
    cell.font = { ...STYLING.headerFont, color: { argb: 'FFFFFFFF' } };
    cell.alignment = { horizontal: 'center', vertical: 'middle' };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: STYLING.colors.headerBg }
    };
    cell.border = {
      top: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
      left: { style: 'thin', color: { argb: STYLING.colors.borderColor } },
      bottom: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
      right: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
    };
  });
  
  worksheet.getRow(headerRow).height = 20;

  // ============================================================================
  // EMPLOYEE DATA ROWS (Pre-formatted for 35 rows)
  // ============================================================================
  
  for (let i = 0; i < 35; i++) {
    const row = headerRow + 1 + i;
    const isAlternate = i % 2 === 1;
    
    for (let col = 1; col <= 12; col++) {
      const cell = worksheet.getCell(row, col);
      cell.font = STYLING.bodyFont;
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      
      if (isAlternate) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: STYLING.colors.alternateRowBg }
        };
      }
      
      cell.border = {
        top: { style: 'thin', color: { argb: STYLING.colors.borderColor } },
        left: { style: 'thin', color: { argb: STYLING.colors.borderColor } },
        bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } },
        right: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
      };
      
      // Add number formatting for time and hours columns
      if (col >= 3 && col <= 8) { // Time columns
        cell.numFmt = 'h:mm AM/PM';
      } else if (col >= 9 && col <= 11) { // Hours columns
        cell.numFmt = '0.00';
      }
    }
    
    worksheet.getRow(row).height = 18;
  }

  // ============================================================================
  // TOTALS ROW
  // ============================================================================
  
  const totalsRow = headerRow + 36;
  
  // Total label
  const totalLabelCell = worksheet.getCell(totalsRow, 1);
  totalLabelCell.value = 'TOTAL HOURS:';
  totalLabelCell.font = { ...STYLING.headerFont, bold: true };
  totalLabelCell.alignment = { horizontal: 'right', vertical: 'middle' };
  
  // Total cells with formulas
  const totalRegularCell = worksheet.getCell(totalsRow, 9);
  totalRegularCell.value = { formula: `SUM(I${headerRow + 1}:I${headerRow + 35})` };
  totalRegularCell.numFmt = '0.00';
  
  const totalOvertimeCell = worksheet.getCell(totalsRow, 10);
  totalOvertimeCell.value = { formula: `SUM(J${headerRow + 1}:J${headerRow + 35})` };
  totalOvertimeCell.numFmt = '0.00';
  
  const grandTotalCell = worksheet.getCell(totalsRow, 11);
  grandTotalCell.value = { formula: `SUM(K${headerRow + 1}:K${headerRow + 35})` };
  grandTotalCell.numFmt = '0.00';
  
  // Style totals row
  for (let col = 1; col <= 12; col++) {
    const cell = worksheet.getCell(totalsRow, col);
    cell.font = { ...STYLING.headerFont, bold: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: STYLING.colors.totalRowBg }
    };
    cell.border = {
      top: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
      left: { style: 'thin', color: { argb: STYLING.colors.borderColor } },
      bottom: { style: 'medium', color: { argb: STYLING.colors.borderColor } },
      right: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
    };
  }
  
  worksheet.getRow(totalsRow).height = 22;

  // ============================================================================
  // SIGNATURE SECTION
  // ============================================================================
  
  const signatureRow = totalsRow + 3;
  
  // Client Contact (K49 - matches config)
  worksheet.getCell('A49').value = 'Client Contact:';
  worksheet.getCell('A49').font = STYLING.headerFont;
  worksheet.getCell('K49').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('K49:N49');
  
  // Client Signature (K50 - matches config)
  worksheet.getCell('A50').value = 'Client Signature:';
  worksheet.getCell('A50').font = STYLING.headerFont;
  worksheet.getCell('K50').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('K50:N50');
  
  // Manager signature
  worksheet.getCell('A51').value = 'Manager Signature:';
  worksheet.getCell('A51').font = STYLING.headerFont;
  worksheet.getCell('E51').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('E51:H51');
  
  // Date fields
  worksheet.getCell('A52').value = 'Date:';
  worksheet.getCell('A52').font = STYLING.headerFont;
  worksheet.getCell('B52').border = {
    bottom: { style: 'thin', color: { argb: STYLING.colors.borderColor } }
  };
  worksheet.mergeCells('B52:D52');

  // ============================================================================
  // FINAL FORMATTING
  // ============================================================================
  
  // Set print settings
  worksheet.pageSetup.printTitlesRow = '1:12';
  worksheet.pageSetup.fitToPage = true;
  worksheet.pageSetup.fitToWidth = 1;
  worksheet.pageSetup.fitToHeight = 0;
  
  // Add header and footer
  worksheet.headerFooter.oddHeader = '&C&"Calibri,Bold"&14TIMESHEET REPORT';
  worksheet.headerFooter.oddFooter = '&L&D &T&C&"Calibri"&10Page &P of &N&R&"Calibri"&10Generated by HoliTime';

  // Save the template
  const templatePath = path.join(__dirname, '..', 'pdf-templates', 'timesheet-template-enhanced.xlsx');
  
  // Ensure directory exists
  const dir = path.dirname(templatePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  await workbook.xlsx.writeFile(templatePath);
  console.log('✅ Enhanced Excel template created successfully at:', templatePath);
  
  // Also update the original template
  const originalTemplatePath = path.join(__dirname, '..', 'pdf-templates', 'timesheet-template.xlsx');
  await workbook.xlsx.writeFile(originalTemplatePath);
  console.log('✅ Original template updated at:', originalTemplatePath);
}

// Run the script
createEnhancedTemplate().catch(console.error);