# This Cloud Build pipeline builds a Docker image, runs database migrations,
# and deploys the application to Cloud Run. All variables are hardcoded for reliability.

steps:
  # Step 1: Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '--build-arg'
      - 'NODE_ENV=production'
      - '--build-arg'
      - 'BUILD_TIME=true'
      - '-t'
      - 'us-west2-docker.pkg.dev/phrasal-lyceum-469005-e2/holi-repo/holi:latest'
      - '.'

  # Step 1.5: Push the Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    waitFor: ['build-image']
    args:
      - 'push'
      - 'us-west2-docker.pkg.dev/phrasal-lyceum-469005-e2/holi-repo/holi:latest'

  # Step 2: Deploy the main application to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-service'
    waitFor: ['push-image']
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'handson' # Your main service name
      - '--image=us-west2-docker.pkg.dev/phrasal-lyceum-469005-e2/holi-repo/holi:latest'
      - '--region=us-west2'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--port=8080'
      - '--memory=2Gi' # Allocate 2Gi of memory for PDF generation with Puppeteer
      - '--cpu=2'
      - '--timeout=3600s'
      - '--min-instances=0' # Set to 0 to scale to zero and save costs
      - '--max-instances=2' # Set a reasonable max
      - '--set-cloudsql-instances=phrasal-lyceum-469005-e2:us-west2:handsondb'
      # Environment variables are now sourced from Google Secret Manager
      - '--set-env-vars=NEXT_TELEMETRY_DISABLED=1,DATABASE_URL=postgresql://sql-user:CloudSQLPass123%21@************:5432/handsondb,NEXTAUTH_SECRET=uivbienbuivenbivrenbiiinbrenbirenbiivrevnurnei,NEXTAUTH_URL=https://handson-425923946404.us-west2.run.app,GOOGLE_CLIENT_ID=425923946404-pq0rv4c4icm3i0ltum7d3v3kecggpsvo.apps.googleusercontent.com,GOOGLE_CLIENT_SECRET=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,SMTP_HOST=smtp.gmail.com,SMTP_PORT=587,SMTP_USER=<EMAIL>,SMTP_PASS=qrovseyublwyiskx,GOOGLE_AI_API_KEY=AIzaSyDZ2W8uqVhvXm38NqWSXmXneREt4UilG_A,GOOGLE_API_KEY=AIzaSyAaMQ6qq0iVnyt2w1IERTPwXGrllSLnhZQ,GOOGLE_CLOUD_PROJECT_ID=phrasal-lyceum-469005-e2,PROJECT_ID=phrasal-lyceum-469005-e2,ENV=production,GCS_AVATAR_BUCKET=handsonavatarbucket,GCS_BUCKET_NAME=handsonavatarbucket,NODE_ENV=production,DATABASE_CONNECTION_LIMIT=20,PUSHER_APP_ID=Y2040384,NEXT_PUBLIC_PUSHER_KEY=2597264584adc4fe8ee3,PUSHER_SECRET=a4b3f2f4f62a6b5985e2,NEXT_PUBLIC_PUSHER_CLUSTER=us2,VERTEXAI_PROJECT=phrasal-lyceum-469005-e2,VERTEXAI_LOCATION=us-east5'
      - '--no-cpu-throttling'



# Images are now pushed explicitly in the push-image step

timeout: '1800s'  # 30 minutes timeout
options:
  machineType: 'E2_HIGHCPU_8'
