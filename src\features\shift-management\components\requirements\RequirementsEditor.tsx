"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@/components/ui/card";
import { RequirementInput } from "./RequirementInput";
import { ROLE_DEFINITIONS } from "@/lib/constants";
import { RoleCode, ShiftWithDetails } from "@/lib/types";
import { useState, useEffect } from "react";
import { updateWorkerRequirements } from "@/lib/services/shifts";
import { toast } from "sonner";
import { useOptimizedMutation } from "../../../../hooks/use-optimized-queries";

interface RequirementsEditorProps {
  shiftId: string;
  initialRequirements: { roleCode: RoleCode; requiredCount: number }[];
}

export function RequirementsEditor({ shiftId, initialRequirements }: RequirementsEditorProps) {
  const [requirements, setRequirements] = useState(initialRequirements);

  useEffect(() => {
    setRequirements(initialRequirements);
  }, [initialRequirements]);

  const mutation = useOptimizedMutation(
    (newRequirements: { roleCode: RoleCode; requiredCount: number }[]) => updateWorkerRequirements(shiftId, newRequirements),
    {
      optimisticUpdater: {
        queryKey: ['shift', shiftId],
        updateFn: (oldShift: ShiftWithDetails, newRequirements) => {
          const newRequirementsMap = new Map(newRequirements.map(r => [r.roleCode, r.requiredCount]));
          
          const updatedWorkerRequirements = oldShift.workerRequirements.map(req => {
            if (newRequirementsMap.has(req.roleCode)) {
              return { ...req, requiredCount: newRequirementsMap.get(req.roleCode)! };
            }
            return req;
          });

          // Add any new requirements that weren't in the original list
          newRequirements.forEach(newReq => {
            if (!oldShift.workerRequirements.some(r => r.roleCode === newReq.roleCode)) {
                updatedWorkerRequirements.push({
                    ...newReq,
                    id: `temp-${Math.random()}`,
                    shiftId: shiftId,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                });
            }
          });

          return {
            ...oldShift,
            workerRequirements: updatedWorkerRequirements,
          };
        },
      },
      onSuccess: () => {
        toast.success("Worker requirements updated successfully.");
      },
      onError: (error) => {
        toast.error(`Failed to update requirements: ${error.message}`);
      },
    }
  );

  const handleRequirementChange = (roleCode: RoleCode, count: number) => {
    setRequirements(prev => {
      const existing = prev.find(r => r.roleCode === roleCode);
      if (existing) {
        return prev.map(r => r.roleCode === roleCode ? { ...r, requiredCount: count } : r);
      }
      return [...prev, { roleCode, requiredCount: count }];
    });
  };

  const handleSaveChanges = () => {
    mutation.mutate(requirements);
  };

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">Worker Requirements</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {(Object.keys(ROLE_DEFINITIONS) as RoleCode[]).map(roleCode => (
            <div key={roleCode} className="bg-gray-700/50 p-4 rounded-lg text-center">
              <RequirementInput
                roleCode={roleCode}
                label={ROLE_DEFINITIONS[roleCode].name}
                value={requirements.find(r => r.roleCode === roleCode)?.requiredCount || 0}
                onChange={handleRequirementChange}
              />
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveChanges} disabled={mutation.isPending} className="bg-indigo-600 hover:bg-indigo-700">
          {mutation.isPending ? "Saving..." : "Save Changes"}
        </Button>
      </CardFooter>
    </Card>
  );
}
