/**
 * Centralized time and date utilities with consistent timezone handling
 * Fixes all time/date conversion issues and format inconsistencies
 */

import { format, parseISO, isValid, differenceInMinutes, addDays, startOfDay, endOfDay } from 'date-fns';
import { zonedTimeToUtc, utcToZonedTime, format as formatTz } from 'date-fns-tz';

// Default timezone for the application (Pacific Time)
export const DEFAULT_TIMEZONE = 'America/Los_Angeles';

// Standard date/time formats
export const DATE_FORMATS = {
  DISPLAY: 'MM/dd/yyyy',
  INPUT: 'yyyy-MM-dd',
  TIME_DISPLAY: 'h:mm a',
  TIME_INPUT: 'HH:mm',
  DATETIME_DISPLAY: 'MM/dd/yyyy h:mm a',
  ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
} as const;

/**
 * CRITICAL FIX: Safe date parsing that handles all input types consistently
 */
export function safeParseDate(input: string | Date | null | undefined): Date | null {
  if (!input) return null;
  
  try {
    if (input instanceof Date) {
      return isValid(input) ? input : null;
    }
    
    if (typeof input === 'string') {
      // Handle ISO strings
      if (input.includes('T') || input.includes('Z')) {
        const parsed = parseISO(input);
        return isValid(parsed) ? parsed : null;
      }
      
      // Handle date-only strings (YYYY-MM-DD)
      if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
        const parsed = parseISO(input + 'T00:00:00');
        return isValid(parsed) ? parsed : null;
      }
      
      // Handle time-only strings (HH:mm)
      if (/^\d{1,2}:\d{2}$/.test(input)) {
        const today = format(new Date(), 'yyyy-MM-dd');
        const parsed = parseISO(`${today}T${input}:00`);
        return isValid(parsed) ? parsed : null;
      }
      
      // Fallback to new Date()
      const parsed = new Date(input);
      return isValid(parsed) ? parsed : null;
    }
    
    return null;
  } catch (error) {
    console.warn('Failed to parse date:', input, error);
    return null;
  }
}

/**
 * CRITICAL FIX: Convert local time to UTC for database storage
 */
export function toUTC(date: string | Date, timezone: string = DEFAULT_TIMEZONE): string {
  const parsedDate = safeParseDate(date);
  if (!parsedDate) {
    throw new Error(`Invalid date input: ${date}`);
  }
  
  // Convert local time to UTC
  const utcDate = zonedTimeToUtc(parsedDate, timezone);
  return utcDate.toISOString();
}

/**
 * CRITICAL FIX: Convert UTC time from database to local timezone
 */
export function fromUTC(utcDate: string | Date, timezone: string = DEFAULT_TIMEZONE): Date {
  const parsedDate = safeParseDate(utcDate);
  if (!parsedDate) {
    throw new Error(`Invalid UTC date input: ${utcDate}`);
  }
  
  // Convert UTC to local timezone
  return utcToZonedTime(parsedDate, timezone);
}

/**
 * CRITICAL FIX: Format date for display with consistent timezone handling
 */
export function formatDate(date: string | Date | null | undefined, formatStr: string = DATE_FORMATS.DISPLAY): string {
  const parsedDate = safeParseDate(date);
  if (!parsedDate) return 'N/A';
  
  try {
    return format(parsedDate, formatStr);
  } catch (error) {
    console.warn('Failed to format date:', date, error);
    return 'Invalid Date';
  }
}

/**
 * CRITICAL FIX: Format time for display with timezone conversion
 */
export function formatTime(date: string | Date | null | undefined, timezone: string = DEFAULT_TIMEZONE): string {
  const parsedDate = safeParseDate(date);
  if (!parsedDate) return '--:--';
  
  try {
    // Convert to local timezone before formatting
    const localDate = utcToZonedTime(parsedDate, timezone);
    return format(localDate, DATE_FORMATS.TIME_DISPLAY);
  } catch (error) {
    console.warn('Failed to format time:', date, error);
    return '--:--';
  }
}

/**
 * CRITICAL FIX: Format datetime for display with timezone conversion
 */
export function formatDateTime(date: string | Date | null | undefined, timezone: string = DEFAULT_TIMEZONE): string {
  const parsedDate = safeParseDate(date);
  if (!parsedDate) return 'N/A';
  
  try {
    // Convert to local timezone before formatting
    const localDate = utcToZonedTime(parsedDate, timezone);
    return format(localDate, DATE_FORMATS.DATETIME_DISPLAY);
  } catch (error) {
    console.warn('Failed to format datetime:', date, error);
    return 'Invalid Date';
  }
}

/**
 * CRITICAL FIX: Format date for HTML input fields
 */
export function formatDateForInput(date: string | Date | null | undefined): string {
  const parsedDate = safeParseDate(date);
  if (!parsedDate) return '';
  
  try {
    return format(parsedDate, DATE_FORMATS.INPUT);
  } catch (error) {
    console.warn('Failed to format date for input:', date, error);
    return '';
  }
}

/**
 * CRITICAL FIX: Format time for HTML input fields
 */
export function formatTimeForInput(date: string | Date | null | undefined, timezone: string = DEFAULT_TIMEZONE): string {
  const parsedDate = safeParseDate(date);
  if (!parsedDate) return '';
  
  try {
    // Convert to local timezone before formatting
    const localDate = utcToZonedTime(parsedDate, timezone);
    return format(localDate, DATE_FORMATS.TIME_INPUT);
  } catch (error) {
    console.warn('Failed to format time for input:', date, error);
    return '';
  }
}

/**
 * CRITICAL FIX: Create datetime from separate date and time inputs
 */
export function createDateTimeFromInputs(dateInput: string, timeInput: string, timezone: string = DEFAULT_TIMEZONE): Date {
  if (!dateInput || !timeInput) {
    throw new Error('Both date and time inputs are required');
  }
  
  try {
    // Create local datetime string
    const localDateTimeString = `${dateInput}T${timeInput}:00`;
    const localDate = parseISO(localDateTimeString);
    
    if (!isValid(localDate)) {
      throw new Error('Invalid date/time combination');
    }
    
    // Convert to UTC for storage
    return zonedTimeToUtc(localDate, timezone);
  } catch (error) {
    console.error('Failed to create datetime from inputs:', { dateInput, timeInput, error });
    throw error;
  }
}

/**
 * CRITICAL FIX: Format time range with consistent timezone handling
 */
export function formatTimeRange(
  startTime: string | Date | null | undefined, 
  endTime: string | Date | null | undefined,
  timezone: string = DEFAULT_TIMEZONE
): string {
  const start = formatTime(startTime, timezone);
  const end = formatTime(endTime, timezone);
  
  if (start === '--:--' && end === '--:--') return '--:-- - --:--';
  if (start === '--:--') return `--:-- - ${end}`;
  if (end === '--:--') return `${start} - --:--`;
  
  return `${start} - ${end}`;
}

/**
 * CRITICAL FIX: Convert 24-hour time to 12-hour format
 */
export function formatTimeTo12Hour(timeString: string | null | undefined): string {
  if (!timeString) return '';
  
  try {
    // Handle both HH:mm and full datetime strings
    let timeOnly = timeString;
    if (timeString.includes('T')) {
      const date = safeParseDate(timeString);
      if (!date) return '';
      timeOnly = format(date, 'HH:mm');
    }
    
    const [hours, minutes] = timeOnly.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return '';
    
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHour = hours % 12 || 12;
    const formattedMinute = minutes.toString().padStart(2, '0');
    
    return `${formattedHour}:${formattedMinute} ${ampm}`;
  } catch (error) {
    console.warn('Failed to format time to 12-hour:', timeString, error);
    return '';
  }
}

/**
 * CRITICAL FIX: Calculate total hours with proper rounding
 */
export function calculateTotalRoundedHours(timeEntries: Array<{ clockIn?: string; clockOut?: string }>): number {
  if (!timeEntries || timeEntries.length === 0) {
    return 0;
  }
  
  const totalMinutes = timeEntries.reduce((acc, entry) => {
    if (entry.clockIn && entry.clockOut) {
      const clockInDate = safeParseDate(entry.clockIn);
      const clockOutDate = safeParseDate(entry.clockOut);
      
      if (clockInDate && clockOutDate) {
        const roundedClockIn = roundTime(clockInDate, 'down');
        const roundedClockOut = roundTime(clockOutDate, 'up');
        return acc + differenceInMinutes(roundedClockOut, roundedClockIn);
      }
    }
    return acc;
  }, 0);
  
  const totalHours = totalMinutes / 60;
  return totalHours;
}

/**
 * CRITICAL FIX: Round time to nearest 15-minute interval
 */
export function roundTime(time: Date, direction: 'up' | 'down'): Date {
  const minutes = time.getMinutes();
  const roundedMinutes = direction === 'up'
    ? Math.ceil(minutes / 15) * 15
    : Math.floor(minutes / 15) * 15;
  
  const newTime = new Date(time);
  newTime.setMinutes(roundedMinutes);
  newTime.setSeconds(0);
  newTime.setMilliseconds(0);
  
  if (roundedMinutes === 60) {
    newTime.setHours(newTime.getHours() + 1);
    newTime.setMinutes(0);
  }
  
  return newTime;
}

/**
 * CRITICAL FIX: Get time entry display with proper formatting
 */
export function getTimeEntryDisplay(entry: { clockIn?: string; clockOut?: string }): string {
  const { clockIn, clockOut } = entry;
  const displayClockIn = clockIn ? formatTimeTo12Hour(clockIn) : 'N/A';
  const displayClockOut = clockOut ? formatTimeTo12Hour(clockOut) : 'N/A';
  return `${displayClockIn} - ${displayClockOut}`;
}

/**
 * CRITICAL FIX: Create Pacific DateTime with proper timezone handling
 */
export function createPacificDateTime(date: Date, time: Date): string {
  try {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(time.getHours()).padStart(2, '0');
    const minutes = String(time.getMinutes()).padStart(2, '0');
    const seconds = String(time.getSeconds()).padStart(2, '0');
    
    const pacificDateTimeString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    const localDate = parseISO(pacificDateTimeString);
    
    // Convert Pacific time to UTC
    return zonedTimeToUtc(localDate, DEFAULT_TIMEZONE).toISOString();
  } catch (error) {
    console.error('Failed to create Pacific datetime:', { date, time, error });
    throw error;
  }
}

/**
 * CRITICAL FIX: Format Pacific time with proper timezone conversion
 */
export function formatPacificTime(dateString?: string | Date | null): string {
  return formatTime(dateString, DEFAULT_TIMEZONE);
}

/**
 * CRITICAL FIX: Format Pacific datetime with proper timezone conversion
 */
export function formatPacificDateTime(dateString?: string | Date | null): string {
  return formatDateTime(dateString, DEFAULT_TIMEZONE);
}

/**
 * CRITICAL FIX: Format Pacific time range with proper timezone conversion
 */
export function formatPacificTimeRange(startTime?: string | Date | null, endTime?: string | Date | null): string {
  return formatTimeRange(startTime, endTime, DEFAULT_TIMEZONE);
}

/**
 * CRITICAL FIX: Convert UTC to Pacific time
 */
export function utcToPacificTime(utcDate: Date): Date {
  return fromUTC(utcDate, DEFAULT_TIMEZONE);
}

/**
 * CRITICAL FIX: Create date from input with proper validation
 */
export function createDateFromInput(dateInput: string): string {
  if (!dateInput) {
    throw new Error('Date input is required');
  }
  
  try {
    const date = safeParseDate(dateInput + 'T00:00:00');
    if (!date) {
      throw new Error('Invalid date input');
    }
    
    return date.toISOString();
  } catch (error) {
    console.error('Error creating date from input:', { dateInput, error });
    throw error;
  }
}

/**
 * CRITICAL FIX: Create end date from input with proper validation
 */
export function createEndDateFromInput(dateInput: string): string {
  if (!dateInput) {
    throw new Error('Date input is required');
  }
  
  try {
    const date = safeParseDate(dateInput + 'T23:59:59.999Z');
    if (!date) {
      throw new Error('Invalid date input');
    }
    
    return date.toISOString();
  } catch (error) {
    console.error('Error creating end date from input:', { dateInput, error });
    throw error;
  }
}

/**
 * CRITICAL FIX: Date range utilities with proper timezone handling
 */
export const dateUtils = {
  addDays: (date: Date | string, days: number): Date => {
    const parsedDate = safeParseDate(date);
    if (!parsedDate) throw new Error('Invalid date');
    return addDays(parsedDate, days);
  },
  
  startOfDay: (date: Date | string): Date => {
    const parsedDate = safeParseDate(date);
    if (!parsedDate) throw new Error('Invalid date');
    return startOfDay(parsedDate);
  },
  
  endOfDay: (date: Date | string): Date => {
    const parsedDate = safeParseDate(date);
    if (!parsedDate) throw new Error('Invalid date');
    return endOfDay(parsedDate);
  },
  
  isToday: (date: Date | string): boolean => {
    const parsedDate = safeParseDate(date);
    if (!parsedDate) return false;
    const today = new Date();
    return parsedDate.toDateString() === today.toDateString();
  },
  
  isThisWeek: (date: Date | string): boolean => {
    const parsedDate = safeParseDate(date);
    if (!parsedDate) return false;
    
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 7);
    
    return parsedDate >= startOfWeek && parsedDate < endOfWeek;
  },
  
  getRelativeTime: (date: Date | string): string => {
    const parsedDate = safeParseDate(date);
    if (!parsedDate) return 'Unknown';
    
    const now = new Date();
    const diffMs = now.getTime() - parsedDate.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return formatDate(parsedDate);
  }
};

/**
 * CRITICAL FIX: API date formatting utilities
 */
export const apiDateUtils = {
  /**
   * Format date for API requests (always UTC)
   */
  formatForAPI: (date: string | Date, timezone: string = DEFAULT_TIMEZONE): string => {
    return toUTC(date, timezone);
  },
  
  /**
   * Parse date from API response (convert from UTC to local)
   */
  parseFromAPI: (utcDate: string, timezone: string = DEFAULT_TIMEZONE): Date => {
    return fromUTC(utcDate, timezone);
  },
  
  /**
   * Create date range for API queries
   */
  createDateRange: (startDate: string, endDate?: string, timezone: string = DEFAULT_TIMEZONE) => {
    const start = toUTC(startDate + 'T00:00:00', timezone);
    const end = endDate 
      ? toUTC(endDate + 'T23:59:59.999', timezone)
      : toUTC(startDate + 'T23:59:59.999', timezone);
    
    return { start, end };
  }
};

// Export commonly used functions for backward compatibility
export {
  formatDate as formatDateForDisplay,
  formatTime as formatTimeForDisplay,
  formatDateTime as formatDateTimeForDisplay,
  apiDateUtils as formatDateForAPI
};
