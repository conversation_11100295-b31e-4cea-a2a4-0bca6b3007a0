/* Enhanced Job Timeline Scheduler Styles */

.timeline-container {
  @apply relative overflow-hidden;
}

.timeline-day {
  @apply relative min-h-full;
  min-width: 180px;
}

.timeline-shift-bar {
  @apply relative cursor-pointer transition-all duration-200 hover:z-20 hover:scale-105;
  transform-origin: center;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-shift-bar:hover {
  filter: brightness(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.timeline-shift-bar.selected {
  @apply z-30 scale-110 ring-2 ring-blue-500 ring-offset-2;
  filter: brightness(1.2);
}

/* Enhanced crew chief color variations */
.crew-chief-bar {
  @apply relative overflow-hidden border-2 border-white shadow-md;
  transition: all 0.2s ease-in-out;
}

.crew-chief-bar:hover {
  @apply shadow-lg;
  transform: scale(1.02);
}

.crew-chief-bar.selected {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Enhanced crew chief assignment indication */
.crew-chief-assigned {
  border-width: 4px !important;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease-in-out;
}

.crew-chief-assigned:hover {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1), 0 6px 16px rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-1px);
}

.crew-chief-unassigned {
  border-width: 2px !important;
  border-style: dashed !important;
  opacity: 0.85;
  animation: crewChiefNeeded 2s ease-in-out infinite;
}

@keyframes crewChiefNeeded {
  0%, 100% { 
    opacity: 0.85;
    border-width: 2px;
  }
  50% { 
    opacity: 0.95;
    border-width: 3px;
  }
}

/* Timeline grid enhancements */
.timeline-grid {
  @apply relative;
  background-image: 
    linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 60px 40px;
}

.timeline-hour-marker {
  @apply absolute top-0 bottom-0 border-r border-dashed;
  border-color: rgba(0, 0, 0, 0.1);
}

.timeline-day-separator {
  @apply absolute top-0 bottom-0 border-r;
  border-color: rgba(0, 0, 0, 0.2);
}

/* Two-tone fill animation */
.shift-fill-transition {
  transition: width 0.5s ease-in-out;
}

/* Worker type color variants */
.worker-type-crew-chief {
  @apply bg-purple-600 text-purple-100;
}

.worker-type-crew-chief-light {
  @apply bg-purple-200 text-purple-800;
}

.worker-type-fork-operator {
  @apply bg-orange-600 text-orange-100;
}

.worker-type-fork-operator-light {
  @apply bg-orange-200 text-orange-800;
}

.worker-type-stage-hand {
  @apply bg-blue-600 text-blue-100;
}

.worker-type-stage-hand-light {
  @apply bg-blue-200 text-blue-800;
}

.worker-type-general-labor {
  @apply bg-gray-600 text-gray-100;
}

.worker-type-general-labor-light {
  @apply bg-gray-200 text-gray-800;
}

/* Fulfillment status colors */
.fulfillment-critical {
  @apply bg-red-500;
}

.fulfillment-low {
  @apply bg-orange-500;
}

.fulfillment-medium {
  @apply bg-yellow-500;
}

.fulfillment-high {
  @apply bg-green-500;
}

.fulfillment-complete {
  @apply bg-emerald-500;
}

/* Timeline grid */
.timeline-hour-grid {
  @apply absolute inset-0 pointer-events-none;
}

.timeline-hour-line {
  @apply absolute left-0 right-0 border-t border-gray-200;
  opacity: 0.3;
}

/* Responsive timeline scaling */
@media (min-width: 1920px) {
  .timeline-day {
    min-width: 250px;
  }
  
  .timeline-shift-bar {
    min-height: 56px;
  }
}

@media (min-width: 2560px) {
  .timeline-day {
    min-width: 320px;
  }
  
  .timeline-shift-bar {
    min-height: 64px;
  }
}

/* Shift detail panel animations */
.shift-detail-panel {
  @apply fixed top-20 right-6 w-96 max-h-[80vh] overflow-y-auto shadow-2xl z-50 border-2;
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.shift-detail-panel.closing {
  animation: slideOutToRight 0.3s ease-in;
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Worker slot styling */
.worker-slot {
  @apply relative overflow-hidden transition-all duration-200;
}

.worker-slot.filled {
  @apply cursor-pointer;
}

.worker-slot.filled:hover {
  @apply scale-105 shadow-md;
}

.worker-slot.empty {
  @apply border-dashed border-2 opacity-75;
}

.worker-slot.empty:hover {
  @apply opacity-80 border-blue-300;
}

/* Timeline navigation */
.timeline-nav {
  @apply flex items-center justify-between p-4 bg-card border-b;
}

.timeline-controls {
  @apply flex items-center gap-2;
}

/* Progress indicators */
.shift-progress-bar {
  @apply h-2 bg-gray-200 rounded-full overflow-hidden;
}

.shift-progress-fill {
  @apply h-full transition-all duration-500 ease-out;
}

/* Tooltip styling for shift bars */
.shift-tooltip {
  @apply absolute -top-16 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs rounded-md px-2 py-1 opacity-0 pointer-events-none transition-opacity duration-200 z-50;
}

.timeline-shift-bar:hover .shift-tooltip {
  @apply opacity-100;
}

/* Fullscreen mode styles */
.timeline-fullscreen {
  @apply fixed inset-0 z-50 bg-background overflow-auto;
}

.timeline-fullscreen .timeline-day {
  min-width: 220px;
}

.timeline-fullscreen .timeline-shift-bar {
  min-height: 40px;
}

/* Zoom level adjustments */
.timeline-zoom-50 .timeline-day {
  min-width: 90px;
}

.timeline-zoom-75 .timeline-day {
  min-width: 135px;
}

.timeline-zoom-125 .timeline-day {
  min-width: 225px;
}

.timeline-zoom-150 .timeline-day {
  min-width: 270px;
}

.timeline-zoom-200 .timeline-day {
  min-width: 360px;
}

/* Enhanced shift bar styles */
.shift-bar-filled {
  @apply opacity-100;
}

.shift-bar-unfilled {
  @apply opacity-75;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 4px,
    rgba(255,255,255,0.3) 4px,
    rgba(255,255,255,0.3) 8px
  );
}

/* Crew chief legend styles */
.crew-chief-legend {
  @apply flex flex-wrap gap-4 p-4 bg-muted/30 rounded-lg;
}

.crew-chief-legend-item {
  @apply flex items-center gap-2 px-3 py-1 rounded-full border;
}

/* Timeline ruler */
.timeline-ruler {
  @apply flex justify-between text-xs text-muted-foreground px-2;
}

/* Animation for shift selection */
@keyframes shiftPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.shift-bar-selected {
  animation: shiftPulse 0.6s ease-in-out;
}

/* Print styles for reports */
@media print {
  .timeline-nav,
  .shift-detail-panel,
  .timeline-controls,
  .timeline-fullscreen-toggle {
    @apply hidden;
  }
  
  .timeline-container {
    @apply shadow-none;
  }
  
  .timeline-shift-bar {
    @apply shadow-none;
  }
  
  .timeline-fullscreen {
    @apply relative inset-auto z-auto;
  }
}