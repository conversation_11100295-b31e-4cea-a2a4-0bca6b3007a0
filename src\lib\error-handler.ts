'use client';

import { useCallback } from 'react'
import { toast } from "@/hooks/use-toast"
import { PDFGenerationOptions } from './unified-pdf-generator';

// Type Definitions
export interface ErrorContext {
  [key: string]: any
}

export interface AppError extends Error {
  code: string
  statusCode: number
  retryable: boolean
  context?: ErrorContext
}

export interface RetryConfig {
  maxAttempts: number
  baseDelay: number  
  maxDelay: number
  backoffFactor: number
  retryableErrors: string[]
}

export interface PDFError {
    code: string;
    message: string;
    details?: any;
    timestamp: Date;
    timesheetId: string;
    options: PDFGenerationOptions;
    retryCount: number;
    isRetryable: boolean;
}
  

// Error Classes
export class NetworkError extends Error implements AppError {
  code = 'NETWORK_ERROR'
  statusCode = 0
  retryable = true
  
  constructor(message: string, public context?: ErrorContext) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class ValidationError extends Error implements AppError {
  code = 'VALIDATION_ERROR'
  statusCode = 400
  retryable = false
  
  constructor(message: string, public context?: ErrorContext) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends Error implements AppError {
  code = 'AUTH_ERROR'
  statusCode = 401
  retryable = false
  
  constructor(message: string, public context?: ErrorContext) {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class ConflictError extends Error implements AppError {
  code = 'CONFLICT_ERROR'
  statusCode = 409
  retryable = false
  
  constructor(message: string, public context?: ErrorContext) {
    super(message)
    this.name = 'ConflictError'
  }
}

export class ServerError extends Error implements AppError {
  code = 'SERVER_ERROR'
  statusCode = 500
  retryable = true
  
  constructor(message: string, public context?: ErrorContext) {
    super(message)
    this.name = 'ServerError'
  }
}

export function handleHttpError(
  statusCode: number, 
  error: {message?: string}, 
  context?: ErrorContext
): AppError {
  if (statusCode >= 400 && statusCode < 500) {
    return new ValidationError(error.message || 'Client error occurred.', context)
  }
  return new ServerError(error.message || 'Server error occurred.', context)
}

export function classifyError(error: any, context?: ErrorContext): AppError {
  if (error instanceof NetworkError || 
      error instanceof ValidationError || 
      error instanceof AuthenticationError || 
      error instanceof ConflictError || 
      error instanceof ServerError) {
    return error
  }

  if (error instanceof TypeError && typeof error.message === 'string' && error.message.includes('fetch')) {
    return new NetworkError('Network connection failed. Please check your internet connection.', context)
  }

  if (error.name === 'AbortError') {
    return new NetworkError('Request was cancelled or timed out.', context)
  }

  if (error.status || error.statusCode) {
    const statusCode = error.status || error.statusCode
    return handleHttpError(statusCode, error, context)
  }
  
  return new ServerError(error.message || 'An unexpected error occurred.', context)
}

// User-friendly message mapping
export function getUserFriendlyMessage(error: AppError): string {
  if (typeof error.message === 'string' && !error.message.includes('prisma') && !error.message.includes('database')) {
    return error.message;
  }

  switch (error.code) {
    case 'NETWORK_ERROR':
      return 'Network connection failed. Please check your internet connection and try again.';
    
    case 'AUTH_ERROR':
      return 'Authentication failed. Please log in again.';
    
    case 'VALIDATION_ERROR':
      return error.message || 'Invalid input. Please check your data and try again.';
    
    case 'CONFLICT_ERROR':
      return error.message || 'This action conflicts with existing data. Please refresh and try again.';
    
    case 'SERVER_ERROR':
      if (typeof error.message === 'string' && error.message.includes('Foreign key constraint')) {
        return 'Cannot delete this item because it is being used elsewhere. Please remove related items first.';
      }
      if (typeof error.message === 'string' && error.message.includes('Unique constraint')) {
        return 'This item already exists. Please use a different name or identifier.';
      }
      return error.message || 'A server error occurred. Please try again later.';
    
    default:
      return error.message || 'Failed to perform action. Please try again.';
  }
}

// Enhanced logging function with detailed context
export function logError(error: AppError, additionalContext?: Record<string, any>): void {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    code: error.code,
    message: error.message,
    statusCode: error.statusCode,
    retryable: error.retryable,
    context: { ...error.context, ...additionalContext },
    stack: error.stack,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
    url: typeof window !== 'undefined' ? window.location.href : 'Server'
  };

  // Main error log
  console.error(`🚨 [${timestamp}] Application Error:`, errorInfo);
  
  // Category-specific logging
  if (error.code === 'NETWORK_ERROR') {
    console.error('🌐 [NETWORK] Connection failed:', {
      message: error.message,
      context: error.context,
      retryable: error.retryable
    });
  } else if (error.code === 'AUTH_ERROR') {
    console.error('🔐 [AUTH] Authentication failed:', {
      message: error.message,
      context: error.context
    });
  } else if (error.code === 'VALIDATION_ERROR') {
    console.error('✋ [VALIDATION] Input validation failed:', {
      message: error.message,
      context: error.context
    });
  } else if (error.code === 'SERVER_ERROR') {
    console.error('🔥 [SERVER] Server error occurred:', {
      message: error.message,
      statusCode: error.statusCode,
      context: error.context
    });
  } else if (error.code === 'CONFLICT_ERROR') {
    console.error('⚠️ [CONFLICT] Resource conflict:', {
      message: error.message,
      context: error.context
    });
  }
  
  // Stack trace for debugging
  if (process.env.NODE_ENV === 'development' && error.stack) {
    console.error('📍 [STACK TRACE]:', error.stack);
  }
}

// Retry Utilities
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 5,
  baseDelay: 500,
  maxDelay: 2000,
  backoffFactor: 1.5,
  retryableErrors: ['NETWORK_ERROR', 'SERVER_ERROR']
}

export function calculateDelay(attempt: number, config: RetryConfig): number {
  const delay = Math.min(
    config.baseDelay * Math.pow(config.backoffFactor, attempt - 1),
    config.maxDelay
  )
  const jitter = delay * 0.25 * (Math.random() * 2 - 1)
  return Math.max(0, delay + jitter)
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  context?: ErrorContext,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config }
  let lastError: AppError = new ServerError('An unexpected error occurred.', context);
  
  for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = classifyError(error, context)
      
      if (!lastError.retryable || !retryConfig.retryableErrors.includes(lastError.code || '')) {
        throw lastError
      }
      
      if (attempt === retryConfig.maxAttempts) {
        throw lastError
      }
      
      const delay = calculateDelay(attempt, retryConfig)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  throw lastError
}

// Additional logging utilities for different contexts
export function logAPIError(endpoint: string, method: string, error: any, requestData?: any): void {
  const timestamp = new Date().toISOString();
  console.error(`🌐 [${timestamp}] API Error:`, {
    endpoint,
    method,
    error: error.message || error,
    status: error.status || error.statusCode,
    requestData,
    stack: error.stack
  });
}

export function logDatabaseError(operation: string, error: any, query?: string): void {
  const timestamp = new Date().toISOString();
  console.error(`💾 [${timestamp}] Database Error:`, {
    operation,
    error: error.message || error,
    code: error.code,
    query,
    stack: error.stack
  });
}

export function logAuthError(operation: string, error: any, userId?: string): void {
  const timestamp = new Date().toISOString();
  console.error(`🔐 [${timestamp}] Auth Error:`, {
    operation,
    error: error.message || error,
    userId,
    stack: error.stack
  });
}

export function logComponentError(componentName: string, error: any, props?: any): void {
  const timestamp = new Date().toISOString();
  console.error(`⚛️ [${timestamp}] Component Error:`, {
    component: componentName,
    error: error.message || error,
    props,
    stack: error.stack
  });
}

export function logHookError(hookName: string, error: any, dependencies?: any[]): void {
  const timestamp = new Date().toISOString();
  console.error(`🎣 [${timestamp}] Hook Error:`, {
    hook: hookName,
    error: error.message || error,
    dependencies,
    stack: error.stack
  });
}

export function logFormError(formName: string, error: any, formData?: any): void {
  const timestamp = new Date().toISOString();
  console.error(`📝 [${timestamp}] Form Error:`, {
    form: formName,
    error: error.message || error,
    formData,
    stack: error.stack
  });
}

export function logPDFError(operation: string, error: any, timesheetId?: string, options?: any): void {
  const timestamp = new Date().toISOString();
  console.error(`📄 [${timestamp}] PDF Error:`, {
    operation,
    error: error.message || error,
    timesheetId,
    options,
    stack: error.stack
  });
}

export function logFileError(operation: string, filename: string, error: any): void {
  const timestamp = new Date().toISOString();
  console.error(`📁 [${timestamp}] File Error:`, {
    operation,
    filename,
    error: error.message || error,
    stack: error.stack
  });
}

// Client-specific Error Handling with enhanced logging
export function handleClientError(error: any, context?: ErrorContext): void {
    const appError = classifyError(error, context)
    
    // Enhanced logging with context
    logError(appError, {
      clientContext: 'handleClientError',
      timestamp: new Date().toISOString(),
      ...context
    })
    
    const userMessage = getUserFriendlyMessage(appError)
    
    // Log the user-facing message too
    console.warn(`💬 [USER MESSAGE] ${userMessage}`, {
      originalError: error.message,
      errorCode: appError.code
    });
    
    toast({
      title: "Error",
      description: userMessage,
      variant: "destructive",
    })
}

export function useErrorHandler(defaultContext?: ErrorContext) {
  const executeWithRetry = useCallback(
    async <T>(
      operation: () => Promise<T>,
      context?: ErrorContext,
      config?: Partial<RetryConfig>
    ): Promise<T> => {
      try {
        return await withRetry(operation, { ...defaultContext, ...context }, config);
      } catch (error) {
        handleClientError(error as AppError, { ...defaultContext, ...context });
        throw error;
      }
    },
    [defaultContext]
  );

  return { withRetry: executeWithRetry };
}
