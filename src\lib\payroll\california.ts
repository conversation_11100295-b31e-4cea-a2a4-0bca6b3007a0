import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';
import {
  addDays,
  differenceInMinutes,
  isBefore,
  isAfter,
  max as dateMax,
  min as dateMin,
  startOfDay,
  endOfDay,
  parseISO,
  startOfWeek,
  endOfWeek,
  isSameDay,
  addMinutes,
  formatISO,
} from 'date-fns';

export type PayrollType = 'HOURLY' | 'SALARIED';

export type PayrollSettings = {
  timezone?: string; // e.g. 'America/Los_Angeles'
  workweekStartDay?: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday
  dailyOvertimeThresholdHours?: number; // default 8
  dailyDoubletimeThresholdHours?: number; // default 12
  weeklyOvertimeThresholdHours?: number; // default 40
  enableSeventhDayRule?: boolean; // default true (California)
};

export type Interval = { start: Date; end: Date };

export type DailyAllocation = {
  date: string; // ISO date YYYY-MM-DD in timezone
  totalHours: number;
  straightHours: number;
  overtimeHours: number;
  doubletimeHours: number;
};

export type WeeklyAllocation = {
  weekStart: string; // ISO date of week start in timezone
  weekEnd: string;
  straightHours: number;
  overtimeHours: number; // incremental weekly OT in addition to daily OT
  doubletimeHours: number; // only from daily or 7th day rule
};

export type PayrollAllocationSummary = {
  daily: DailyAllocation[];
  weekly: WeeklyAllocation[];
  totals: {
    straightHours: number;
    overtimeHours: number;
    doubletimeHours: number;
    totalHours: number;
  };
};

const toTZ = (d: Date, tz: string) => utcToZonedTime(d, tz);
const startOfDayTZ = (d: Date, tz: string) => startOfDay(toTZ(d, tz));
const endOfDayTZ = (d: Date, tz: string) => endOfDay(toTZ(d, tz));

export function mergeIntervals(intervals: Interval[]): Interval[] {
  const filtered = intervals
    .filter(iv => iv.start && iv.end && isBefore(iv.start, iv.end))
    .sort((a, b) => a.start.getTime() - b.start.getTime());
  if (filtered.length === 0) return [];
  const result: Interval[] = [];
  let current = { ...filtered[0] };
  for (let i = 1; i < filtered.length; i++) {
    const next = filtered[i];
    if (next.start <= current.end) {
      current.end = next.end > current.end ? next.end : current.end;
    } else {
      result.push(current);
      current = { ...next };
    }
  }
  result.push(current);
  return result;
}

export function splitIntervalsByDay(intervals: Interval[], timezone: string): Record<string, number> {
  const hoursByDate: Record<string, number> = {};
  for (const iv of intervals) {
    let cursorStart = iv.start;
    let cursorEnd = iv.end;

    while (!isSameDay(utcToZonedTime(cursorStart, timezone), utcToZonedTime(cursorEnd, timezone))) {
      const dayStart = startOfDayTZ(cursorStart, timezone);
      const dayEnd = endOfDayTZ(cursorStart, timezone);
      const segmentStart = cursorStart;
      const segmentEnd = dateMin([cursorEnd, addMinutes(dayEnd, 0)]);
      const minutes = Math.max(0, differenceInMinutes(segmentEnd, segmentStart));
      const dateKey = formatISO(utcToZonedTime(segmentStart, timezone), { representation: 'date' });
      hoursByDate[dateKey] = (hoursByDate[dateKey] || 0) + minutes / 60;
      cursorStart = addMinutes(dayStart, 24 * 60); // move to next day at same time
    }
    // same day remainder
    const dateKey = formatISO(utcToZonedTime(cursorStart, timezone), { representation: 'date' });
    const minutes = Math.max(0, differenceInMinutes(cursorEnd, cursorStart));
    hoursByDate[dateKey] = (hoursByDate[dateKey] || 0) + minutes / 60;
  }
  return hoursByDate;
}

function applyDailyRules(hoursByDate: Record<string, number>, settings: Required<PayrollSettings>): DailyAllocation[] {
  const daily: DailyAllocation[] = [];
  const otTh = settings.dailyOvertimeThresholdHours;
  const dtTh = settings.dailyDoubletimeThresholdHours;

  for (const date of Object.keys(hoursByDate).sort()) {
    const h = hoursByDate[date];
    const straight = Math.min(h, otTh);
    const ot = Math.min(Math.max(h - otTh, 0), Math.max(dtTh - otTh, 0));
    const dt = Math.max(h - dtTh, 0);
    daily.push({ date, totalHours: h, straightHours: straight, overtimeHours: ot, doubletimeHours: dt });
  }
  return daily;
}

function applySeventhDayRule(daily: DailyAllocation[], settings: Required<PayrollSettings>): void {
  if (!settings.enableSeventhDayRule) return;
  const weekStartDay = settings.workweekStartDay;
  // group by week
  const byWeek: Record<string, DailyAllocation[]> = {};
  for (const d of daily) {
    const dateObj = parseISO(d.date);
    const ws = startOfWeek(dateObj, { weekStartsOn: weekStartDay });
    const key = formatISO(ws, { representation: 'date' });
    byWeek[key] = byWeek[key] || [];
    byWeek[key].push(d);
  }
  for (const key of Object.keys(byWeek)) {
    const days = byWeek[key].sort((a, b) => a.date.localeCompare(b.date));
    // count days worked (>0 hours)
    const workedDays = days.filter(d => d.totalHours > 0);
    if (workedDays.length >= 7) {
      const seventh = workedDays[6];
      // Reclassify seventh day per CA Labor Code 510
      const first8 = Math.min(seventh.totalHours, 8);
      const over8 = Math.max(seventh.totalHours - 8, 0);
      seventh.doubletimeHours = Math.max(seventh.doubletimeHours, over8);
      const alreadyOT = seventh.overtimeHours + seventh.doubletimeHours;
      const neededOT = Math.max(first8 - (seventh.totalHours - alreadyOT), 0);
      // Ensure at least first 8 hours are OT (if not already OT/DT)
      const straightAvailable = Math.max(seventh.straightHours - neededOT, 0);
      seventh.straightHours = straightAvailable;
      seventh.overtimeHours = Math.max(seventh.overtimeHours, Math.min(first8, seventh.totalHours) - seventh.straightHours - seventh.doubletimeHours);
      // Normalize bounds
      const sum = seventh.straightHours + seventh.overtimeHours + seventh.doubletimeHours;
      if (sum > seventh.totalHours) {
        const excess = sum - seventh.totalHours;
        // reduce overtime first
        const reduceOT = Math.min(excess, seventh.overtimeHours);
        seventh.overtimeHours -= reduceOT;
        const still = excess - reduceOT;
        if (still > 0) seventh.straightHours = Math.max(0, seventh.straightHours - still);
      }
    }
  }
}

function applyWeeklyOvertime(daily: DailyAllocation[], settings: Required<PayrollSettings>): WeeklyAllocation[] {
  const weekStartDay = settings.workweekStartDay;
  const byWeek: Record<string, DailyAllocation[]> = {};
  for (const d of daily) {
    const dateObj = parseISO(d.date);
    const ws = startOfWeek(dateObj, { weekStartsOn: weekStartDay });
    const key = formatISO(ws, { representation: 'date' });
    byWeek[key] = byWeek[key] || [];
    byWeek[key].push(d);
  }

  const weekly: WeeklyAllocation[] = [];
  for (const key of Object.keys(byWeek).sort()) {
    const days = byWeek[key];
    // Sum straight hours (post daily and 7th-day rules)
    const straightSum = days.reduce((acc, d) => acc + d.straightHours, 0);
    let weeklyExtraOT = 0;
    if (straightSum > (settings.weeklyOvertimeThresholdHours || 40)) {
      weeklyExtraOT = straightSum - (settings.weeklyOvertimeThresholdHours || 40);
      // Reclassify from straight to overtime proportionally by day where straightHours > 0
      let remaining = weeklyExtraOT;
      for (const d of days) {
        if (remaining <= 0) break;
        const convertible = Math.min(d.straightHours, remaining);
        d.straightHours -= convertible;
        d.overtimeHours += convertible;
        remaining -= convertible;
      }
    }
    const weekStart = key;
    const anyDate = parseISO(key);
    const weekEnd = formatISO(endOfWeek(anyDate, { weekStartsOn: weekStartDay }), { representation: 'date' });
    weekly.push({
      weekStart,
      weekEnd,
      straightHours: days.reduce((a, d) => a + d.straightHours, 0),
      overtimeHours: days.reduce((a, d) => a + d.overtimeHours, 0),
      doubletimeHours: days.reduce((a, d) => a + d.doubletimeHours, 0),
    });
  }
  return weekly;
}

export function summarizeAllocations(daily: DailyAllocation[], weekly: WeeklyAllocation[]): PayrollAllocationSummary {
  const straightHours = daily.reduce((a, d) => a + d.straightHours, 0);
  const overtimeHours = daily.reduce((a, d) => a + d.overtimeHours, 0);
  const doubletimeHours = daily.reduce((a, d) => a + d.doubletimeHours, 0);
  return {
    daily,
    weekly,
    totals: {
      straightHours,
      overtimeHours,
      doubletimeHours,
      totalHours: straightHours + overtimeHours + doubletimeHours,
    },
  };
}

export function calculateAllocationsFromIntervals(
  intervals: Interval[],
  settings?: PayrollSettings
): PayrollAllocationSummary {
  const effective: Required<PayrollSettings> = {
    timezone: settings?.timezone || 'America/Los_Angeles',
    workweekStartDay: settings?.workweekStartDay ?? 0,
    dailyOvertimeThresholdHours: settings?.dailyOvertimeThresholdHours ?? 8,
    dailyDoubletimeThresholdHours: settings?.dailyDoubletimeThresholdHours ?? 12,
    weeklyOvertimeThresholdHours: settings?.weeklyOvertimeThresholdHours ?? 40,
    enableSeventhDayRule: settings?.enableSeventhDayRule ?? true,
  };

  const merged = mergeIntervals(intervals);
  const hoursByDay = splitIntervalsByDay(merged, effective.timezone);
  const daily = applyDailyRules(hoursByDay, effective);
  applySeventhDayRule(daily, effective);
  const weekly = applyWeeklyOvertime(daily, effective);
  return summarizeAllocations(daily, weekly);
}

export function centsFromRate(rate: number): number {
  // round to cents
  return Math.round(rate * 100);
}

export function computeGrossPayCents(
  allocations: PayrollAllocationSummary,
  hourlyRateCents: number
): number {
  // CA: overtime = 1.5x, doubletime = 2x
  const straight = allocations.totals.straightHours * hourlyRateCents;
  const ot = allocations.totals.overtimeHours * Math.round(hourlyRateCents * 1.5);
  const dt = allocations.totals.doubletimeHours * (hourlyRateCents * 2);
  return Math.round(straight + ot + dt);
}
