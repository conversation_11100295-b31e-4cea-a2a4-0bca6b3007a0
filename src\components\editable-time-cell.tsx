'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

interface EditableTimeCellProps {
  time?: string;
  onUpdate: (newTime: string) => Promise<void>;
  disabled?: boolean;
}

export default function EditableTimeCell({ time, onUpdate, disabled }: EditableTimeCellProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [newTime, setNewTime] = useState(time ? new Date(time).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }) : '');
  const { toast } = useToast();

  useEffect(() => {
    setNewTime(time ? new Date(time).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }) : '');
  }, [time]);

  const handleUpdate = async () => {
    if (disabled) {
      toast({
        title: 'Error',
        description: 'Time update function not available.',
        variant: 'destructive',
      });
      return;
    }
    try {
      await onUpdate(newTime);
      setIsEditing(false);
      toast({
        title: 'Success',
        description: 'Time updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update time.',
        variant: 'destructive',
      });
    }
  };

  const formatDisplayTime = (timeString?: string) => {
    if (!timeString) return '-';
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return '-';
    }
  };

  if (isEditing) {
    return (
      <Input
        type="time"
        value={newTime}
        onChange={(e) => setNewTime(e.target.value)}
        onBlur={handleUpdate}
        onKeyDown={(e) => e.key === 'Enter' && handleUpdate()}
        autoFocus
        className="w-24"
      />
    );
  }

  return (
    <div onClick={() => !disabled && setIsEditing(true)} className="cursor-pointer">
      {formatDisplayTime(time)}
    </div>
  );
}
