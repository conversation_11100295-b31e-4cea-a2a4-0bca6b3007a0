import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { TimeEntry, AssignedPersonnel, User, UserRole, WorkerStatus } from '@prisma/client';
import { hasPermission } from '@/lib/authorization';

const getWorkerStatus = (timeEntries: TimeEntry[], shiftStatus: string): WorkerStatus => {
  if (shiftStatus === 'Completed' || shiftStatus === 'Cancelled') return WorkerStatus.ShiftEnded;

  const lastEntry = timeEntries.sort((a, b) => b.entryNumber - a.entryNumber)[0];

  if (!lastEntry) return WorkerStatus.Assigned;
  return lastEntry.isActive ? WorkerStatus.ClockedIn : WorkerStatus.ClockedOut;
};

type RequestContext = {
  params: {
    shiftId: string;
  };
};

type AssignedPersonnelWithDetails = AssignedPersonnel & { user: User, timeEntries: TimeEntry[] };

export async function GET(req: NextRequest, { params }: RequestContext) {
  const user = await getCurrentUser(req);
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Authorization will be checked after we fetch the shift data

  const { shiftId } = params;

  if (!shiftId || typeof shiftId !== 'string') {
    return NextResponse.json({ error: 'Shift ID is required' }, { status: 400 });
  }

  try {
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        job: {
          include: {
            company: true,
          },
        },
        assignedPersonnel: {
          include: {
            user: true,
            timeEntries: {
              orderBy: {
                entryNumber: 'asc',
              },
            },
          },
        },
        timesheets: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    if (!shift) {
      return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
    }

    // Check authorization using new permission system
    const canReadShift = await hasPermission(user, 'SHIFT', 'READ', { resource: shift });
    if (!canReadShift) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const assignedPersonnelWithStatus = (shift as any).assignedPersonnel?.map((p: AssignedPersonnelWithDetails) => ({
      ...p,
      status: getWorkerStatus(p.timeEntries, shift.status),
    }));

    return NextResponse.json({ ...shift, assignedPersonnel: assignedPersonnelWithStatus });
  } catch (error) {
    console.error(`Error fetching shift ${shiftId}:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
