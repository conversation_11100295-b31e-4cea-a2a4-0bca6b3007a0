'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getHealthIcon } from '@/lib/pdf-management-helpers';
import { PDFStats } from '@/types/pdf-stats';

interface SystemHealthAlertProps {
  systemHealth: PDFStats['systemHealth'];
}

export const SystemHealthAlert: React.FC<SystemHealthAlertProps> = ({ systemHealth }) => {
  if (systemHealth.status === 'healthy') {
    return null;
  }

  const isCritical = systemHealth.status === 'critical';
  const borderColorClass = isCritical ? 'border-red-500' : 'border-yellow-500';

  return (
    <Alert className={borderColorClass}>
      {getHealthIcon(systemHealth.status)}
      <AlertTitle>System Health: {systemHealth.status.toUpperCase()}</AlertTitle>
      <AlertDescription>
        {isCritical
          ? 'Critical issues detected. Immediate attention required.'
          : 'Performance issues detected. Consider optimization.'}
      </AlertDescription>
    </Alert>
  );
};
