import React from 'react';
import { Filter, X, <PERSON><PERSON><PERSON><PERSON><PERSON>, Award, MapPin } from 'lucide-react';

interface FilterState {
  searchText: string;
  activeOnly: boolean;
  crewChiefEligible: boolean;
  forkOperatorEligible: boolean;
  hasCertifications: boolean;
  location: string;
}

interface FilterPanelProps {
  filters: FilterState;
  setFilters: React.Dispatch<React.SetStateAction<FilterState>>;
  availableLocations: string[];
  showFilters: boolean;
  setShowFilters: React.Dispatch<React.SetStateAction<boolean>>;
  hasActiveFilters: boolean;
  clearFilters: () => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  setFilters,
  availableLocations,
  showFilters,
  setShowFilters,
  hasActiveFilters,
  clearFilters,
}) => {
  return (
    <>
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-1 px-2 py-1 text-xs rounded-md transition-colors ${
            showFilters || hasActiveFilters 
              ? 'bg-indigo-600 text-white' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          <Filter className="w-3 h-3" />
          Filters {hasActiveFilters && `(${Object.values(filters).filter(v => v && v !== '').length})`}
        </button>
        
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="flex items-center gap-1 px-2 py-1 text-xs text-gray-400 hover:text-white"
          >
            <X className="w-3 h-3" />
            Clear
          </button>
        )}
      </div>

      {showFilters && (
        <div className="mt-3 p-3 bg-gray-900 rounded-md space-y-2">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={filters.activeOnly}
              onChange={(e) => setFilters(prev => ({ ...prev, activeOnly: e.target.checked }))}
              className="rounded border-gray-600 bg-gray-800 text-indigo-500 focus:ring-indigo-500"
            />
            <UserCheck className="w-4 h-4 text-green-400" />
            Active users only
          </label>
          
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={filters.crewChiefEligible}
              onChange={(e) => setFilters(prev => ({ ...prev, crewChiefEligible: e.target.checked }))}
              className="rounded border-gray-600 bg-gray-800 text-indigo-500 focus:ring-indigo-500"
            />
            <span className="text-blue-400 font-medium">CC</span>
            Crew Chief eligible
          </label>
          
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={filters.forkOperatorEligible}
              onChange={(e) => setFilters(prev => ({ ...prev, forkOperatorEligible: e.target.checked }))}
              className="rounded border-gray-600 bg-gray-800 text-indigo-500 focus:ring-indigo-500"
            />
            <span className="text-green-400 font-medium">FO</span>
            Fork Operator eligible
          </label>
          
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={filters.hasCertifications}
              onChange={(e) => setFilters(prev => ({ ...prev, hasCertifications: e.target.checked }))}
              className="rounded border-gray-600 bg-gray-800 text-indigo-500 focus:ring-indigo-500"
            />
            <Award className="w-4 h-4 text-purple-400" />
            Has certifications
          </label>

          {availableLocations.length > 0 && (
            <div>
              <label className="flex items-center gap-2 text-sm mb-1">
                <MapPin className="w-4 h-4 text-orange-400" />
                Location
              </label>
              <select
                value={filters.location || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                className="w-full text-sm bg-gray-800 border border-gray-600 rounded-md text-white focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              >
                <option value="">All locations</option>
                {availableLocations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}
    </>
  );
};
