import Image from 'next/image';
import React, { useState, useEffect, useCallback, memo } from 'react';
import { getInitials, getCompanyInitials } from "@/lib/utils";

interface AvatarProps {
  src?: string | null;
  name: string;
  className?: string;
  userId?: string; // For smart caching
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  enableSmartCaching?: boolean;
  onLoadError?: () => void;
  onLoadSuccess?: () => void;
  type?: 'user' | 'company';
}

const sizeClasses = {
  xs: 'w-8 h-8 text-sm',
  sm: 'w-12 h-12 text-base',
  md: 'w-16 h-16 text-lg',
  lg: 'w-20 h-20 text-xl',
  xl: 'w-24 h-24 text-2xl'
};

export const Avatar = memo(function Avatar({
  src,
  name,
  className = '',
  userId,
  size = 'md',
  enableSmartCaching = true,
  onLoadError,
  onLoadSuccess,
  type = 'user'
}: AvatarProps) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setHasError(false);
  }, [src]);

  const handleImageError = useCallback(() => {
    setHasError(true);
    onLoadError?.();
  }, [onLoadError]);

  const handleImageLoad = useCallback(() => {
    onLoadSuccess?.();
  }, [onLoadSuccess]);

  const sizeClass = sizeClasses[size];
  const initials = type === 'user' ? getInitials(name) : getCompanyInitials(name);
  const hasCustomSize = className && (className.includes('w-') || className.includes('h-'));
  const finalSizeClass = hasCustomSize ? '' : sizeClass;

  return (
    <div className={`relative inline-flex items-center justify-center overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600 ${finalSizeClass} ${className}`}>
      {src && !hasError ? (
        <Image
          src={src}
          alt={name}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover"
          onLoad={handleImageLoad}
          onError={handleImageError}
          unoptimized={true}
        />
      ) : (
        <span className="font-medium text-gray-600 dark:text-gray-300">
          {initials}
        </span>
      )}
    </div>
  );
});
