# HoliTime Cloud Run Deployment Script
# PowerShell script for deploying to Google Cloud Run

param(
    [switch]$SkipVerification,
    [switch]$LocalBuild,
    [string]$ProjectId = "phrasal-lyceum-469005-e2",
    [string]$Region = "us-west2"
)

Write-Host "🚀 HandsOnLabor - WFM - Cloud Run Deployment" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check if gcloud is installed
try {
    $gcloudVersion = gcloud version --format="value(Google Cloud SDK)" 2>$null
    Write-Host "✅ Google Cloud SDK: $gcloudVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Google Cloud SDK not found. Please install gcloud CLI." -ForegroundColor Red
    exit 1
}

# Get current project if not specified
if (-not $ProjectId) {
    try {
        $ProjectId = gcloud config get-value project 2>$null
        if (-not $ProjectId) {
            Write-Host "❌ No project configured. Please set project ID." -ForegroundColor Red
            Write-Host "   Run: gcloud config set project YOUR_PROJECT_ID" -ForegroundColor Yellow
            exit 1
        }
    } catch {
        Write-Host "❌ Failed to get current project. Please configure gcloud." -ForegroundColor Red
        exit 1
    }
}

Write-Host "📋 Project: $ProjectId" -ForegroundColor Blue
Write-Host "🌍 Region: $Region" -ForegroundColor Blue

# Run verification unless skipped
if (-not $SkipVerification) {
    Write-Host "`n🔍 Running deployment verification..." -ForegroundColor Yellow
    try {
        node scripts/deployment-verification.js
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Verification failed. Fix issues before deploying." -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "❌ Verification script failed." -ForegroundColor Red
        exit 1
    }


# Check required APIs
Write-Host "`n🔧 Checking required APIs..." -ForegroundColor Yellow
$requiredApis = @(
    "run.googleapis.com",
    "cloudbuild.googleapis.com",
    "secretmanager.googleapis.com",
    "artifactregistry.googleapis.com"
)

foreach ($api in $requiredApis) {
    try {
        $apiStatus = gcloud services list --enabled --filter="name:$api" --format="value(name)" 2>$null
        if ($apiStatus) {
            Write-Host "✅ $api enabled" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Enabling $api..." -ForegroundColor Yellow
            gcloud services enable $api
        }
    } catch {
        Write-Host "❌ Failed to check API: $api" -ForegroundColor Red
    }
}

# Deploy based on method
if ($LocalBuild) {
    Write-Host "`n🐳 Building and deploying locally..." -ForegroundColor Yellow
    
    # Build Docker image
    $imageName = "gcr.io/$ProjectId/holitime:latest"
    Write-Host "Building image: $imageName" -ForegroundColor Blue
    
    docker build -t $imageName .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker build failed." -ForegroundColor Red
        exit 1
    }
    
    # Push image
    Write-Host "Pushing image..." -ForegroundColor Blue
    docker push $imageName
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker push failed." -ForegroundColor Red
        exit 1
    }
    
    # Deploy to Cloud Run
    Write-Host "Deploying to Cloud Run..." -ForegroundColor Blue
    gcloud run deploy handson `
        --image $imageName `
        --region $Region `
        --platform managed `
        --allow-unauthenticated `
        --memory 2Gi `
        --cpu 2 `
        --timeout 3600 `
        --max-instances 2 `
        --min-instances 0 `
        --port 8080
        
} else {
    Write-Host "`n☁️ Deploying using Cloud Build..." -ForegroundColor Yellow
    
    # Submit to Cloud Build
    gcloud builds submit --config cloudbuild.yaml
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Cloud Build deployment failed." -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Your application should be available at:" -ForegroundColor Cyan

# Get service URL
try {
    $serviceUrl = gcloud run services describe handson --region=$Region --format="value(status.url)" 2>$null
    if ($serviceUrl) {
        Write-Host "   $serviceUrl" -ForegroundColor Blue
    } else {
        Write-Host "   Run 'gcloud run services describe handson --region=$Region' to get URL" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Unable to retrieve service URL automatically" -ForegroundColor Yellow
}

Write-Host "`n📊 Useful commands:" -ForegroundColor Cyan
Write-Host "   View logs: gcloud run services logs read handson --region=$Region" -ForegroundColor Gray
Write-Host "   Check status: gcloud run services describe handson --region=$Region" -ForegroundColor Gray
Write-Host "   Update service: gcloud run services update handson --region=$Region" -ForegroundColor Gray

Write-Host "`n✅ Deployment complete!" -ForegroundColor Green