"use client"

import React, { useState, useMemo } from "react"
import { useRouter } from "next/navigation"
import { useUsers, useCompanies } from "@/hooks/use-api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar } from '@/components/Avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Label } from '@/components/ui/label'
import {
  Users,
  Search,
  MoreHorizontal,
  Key,
  UserCog,
  Shield,
  ShieldCheck,
  Eye,
  EyeOff,
  RefreshCw
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { UserRole } from "@/lib/types"

export default function AdminUsersClientPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { data: usersResponse, isLoading: usersLoading, isError: usersError } = useUsers({ fetchAll: true })
  const users = useMemo(() => {
    if (!usersResponse) return []
    
    try {
      if (usersResponse && typeof usersResponse === 'object' && Array.isArray(usersResponse.users)) {
        return usersResponse.users || []
      }
      
      if (Array.isArray(usersResponse)) return usersResponse
      
      console.warn('Unexpected usersResponse structure:', usersResponse)
      return []
    } catch (error) {
      console.error('Error processing users data:', error)
      return []
    }
  }, [usersResponse])
  const { data: companiesData, isLoading: companiesLoading, isError: companiesError } = useCompanies()
  const companies = useMemo(() => {
    if (!companiesData) return []
    
    try {
      if (companiesData && typeof companiesData === 'object' && Array.isArray(companiesData.companies)) {
        return companiesData.companies || []
      }
      
      if (Array.isArray(companiesData)) return companiesData
      
      console.warn('Unexpected companiesData structure:', companiesData)
      return []
    } catch (error) {
      console.error('Error processing companies data:', error)
      return []
    }
  }, [companiesData])

  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [resetPasswordDialog, setResetPasswordDialog] = useState<{ open: boolean; user: any | null }>({
    open: false,
    user: null
  })
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isResetting, setIsResetting] = useState(false)

  const companyMap = useMemo(() => {
    if (!Array.isArray(companies)) {
      console.warn('Companies is not an array when creating map:', companies)
      return new Map<string, string>()
    }
    return new Map(
      companies
        .filter(c => c && c.id && c.name)
        .map(c => [c.id, c.name] as [string, string])
    )
  }, [companies])

  const filteredUsers = useMemo(() => {
    if (!Array.isArray(users)) {
      console.warn('Users is not an array when filtering:', users)
      return []
    }

    return users.filter(u => {
      const companyName = u.companyId ? companyMap.get(u.companyId) : ''
      const matchesSearch = 
        u.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        u.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        companyName?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesRole = roleFilter === 'all' || u.role === roleFilter

      return matchesSearch && matchesRole
    })
  }, [users, searchTerm, roleFilter, companyMap])

  const handleResetPassword = async () => {
    if (!resetPasswordDialog.user) return

    if (newPassword !== confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match.",
        variant: "destructive",
      })
      return
    }

    if (newPassword.length < 8) {
      toast({
        title: "Error",
        description: "Password must be at least 8 characters long.",
        variant: "destructive",
      })
      return
    }

    setIsResetting(true)

    try {
      const response = await fetch(`/api/users/${resetPasswordDialog.user.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          newPassword,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to reset password')
      }

      toast({
        title: "Password Reset",
        description: `Password has been reset for ${resetPasswordDialog.user.name}.`,
      })

      setResetPasswordDialog({ open: false, user: null })
      setNewPassword("")
      setConfirmPassword("")
      setShowPassword(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reset password. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsResetting(false)
    }
  }

  const generateRandomPassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    setNewPassword(password)
    setConfirmPassword(password)
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'Admin':
        return <Badge variant="destructive"><ShieldCheck className="mr-1 h-3 w-3" />Admin</Badge>
      case 'CrewChief':
        return <Badge variant="default"><Shield className="mr-1 h-3 w-3" />Crew Chief</Badge>
      case 'StageHand':
        return <Badge variant="secondary"><Users className="mr-1 h-3 w-3" />StageHand</Badge>
      case 'CompanyUser':
        return <Badge variant="outline"><UserCog className="mr-1 h-3 w-3" />Company User</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  if (usersLoading || companiesLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-muted-foreground">Loading users...</div>
        </div>
      </div>
    )
  }

  if (usersError || companiesError) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-destructive">Error loading users. Please try again.</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground">
            Manage user accounts and reset passwords
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users ({filteredUsers.length})
          </CardTitle>
          <CardDescription>
            Search and filter users, reset passwords, and manage accounts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search users by name, email, or company..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value={UserRole.Admin}>Admin</SelectItem>
                <SelectItem value={UserRole.CrewChief}>Crew Chief</SelectItem>
                <SelectItem value={UserRole.StageHand}>StageHand</SelectItem>
                <SelectItem value={UserRole.CompanyUser}>Company User</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((u) => (
                  <TableRow key={u.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={u.avatarUrl}
                          name={u.name || u.email || 'U'}
                          userId={u.id}
                          size="sm"
                          enableSmartCaching={true}
                          className="h-8 w-8"
                        />
                        <div>
                          <div className="font-medium">{u.name}</div>
                          <div className="text-sm text-muted-foreground">ID: {u.id.slice(0, 8)}...</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{u.email}</TableCell>
                    <TableCell>{getRoleBadge(u.role)}</TableCell>
                    <TableCell>{u.companyId ? companyMap.get(u.companyId) : u.name}</TableCell>
                    <TableCell>
                      <Badge variant={u.isActive ? "default" : "secondary"}>
                        {u.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => router.push(`/admin/users/${u.id}/edit`)}
                          >
                            <UserCog className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => setResetPasswordDialog({ open: true, user: u })}
                          >
                            <Key className="mr-2 h-4 w-4" />
                            Reset Password
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Users Found</h3>
              <p className="text-muted-foreground">
                No users match your current search criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reset Password Dialog */}
      <Dialog 
        open={resetPasswordDialog.open} 
        onOpenChange={(open) => {
          if (!open) {
            setResetPasswordDialog({ open: false, user: null })
            setNewPassword("")
            setConfirmPassword("")
            setShowPassword(false)
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              Reset password for <strong>{resetPasswordDialog.user?.name}</strong>
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="new-password">New Password</Label>
              <div className="relative">
                <Input
                  id="new-password"
                  type={showPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Enter new password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm Password</Label>
              <Input
                id="confirm-password"
                type={showPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm new password"
              />
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={generateRandomPassword}
              className="w-full"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Generate Random Password
            </Button>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setResetPasswordDialog({ open: false, user: null })}
            >
              Cancel
            </Button>
            <Button
              onClick={handleResetPassword}
              disabled={isResetting || !newPassword || !confirmPassword}
            >
              {isResetting ? "Resetting..." : "Reset Password"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
