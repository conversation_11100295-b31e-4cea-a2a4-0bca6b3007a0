'use client';

import JobCard from '@/components/JobCard';
import { Job } from '@/lib/types';

// Mock data to test completed shift functionality
const mockCompletedJob: any = {
  id: 'test-job-1',
  name: 'Test Event Setup',
  description: 'Setting up for a test event',
  status: 'Active',
  startDate: new Date(),
  endDate: new Date(),
  location: 'Test Venue',
  budget: '$5000',
  notes: 'Test notes',
  isCompleted: false,
  companyId: 'test-company-1',
  createdAt: new Date(),
  updatedAt: new Date(),
  company: {
    id: 'test-company-1',
    name: 'Test Company',
    company_logo_url: null,
    email: '<EMAIL>',
    phone: '555-0123',
    address: '123 Test St',
    isActive: true,
    description: 'Test Description',
    website: 'test.com',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  shifts: [],
  recentShifts: [],
};

export default function TestCompletedShiftPage() {
  const handleView = (job: Job) => {
    console.log('View job:', job.id);
  };

  const handleEdit = (job: Job) => {
    console.log('Edit job:', job.id);
  };

  const handleDelete = (job: Job) => {
    console.log('Delete job:', job.id);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Test Completed Shift Functionality</h1>
        
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Job with Mixed Shift Statuses</h2>
            <p className="text-gray-400 mb-4">
              This demonstrates how completed shifts appear with green background and timesheet badge,
              while active shifts show the normal worker count badge.
            </p>
            <JobCard 
              job={mockCompletedJob} 
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          </div>
          
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Expected Behavior:</h3>
            <ul className="space-y-2 text-gray-300">
              <li>✅ <strong>Completed Shift (First shift):</strong> Green background, timesheet badge with status</li>
              <li>✅ <strong>Active Shift (Second shift):</strong> Normal background, worker count badge (1/3)</li>
              <li>✅ <strong>Timesheet Link:</strong> Clicking the timesheet badge should navigate to /timesheets/test-timesheet-1</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
