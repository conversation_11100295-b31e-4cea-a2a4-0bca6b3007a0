/**
 * Advanced Filtering Components
 * 
 * Provides comprehensive filtering capabilities including:
 * - Date range selection
 * - Multi-select filters
 * - Search with autocomplete
 * - Filter presets
 * - Export/import filter configurations
 */

"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Calendar, Filter, X, Search, ChevronDown, ChevronUp, Download, Upload, RotateCcw } from 'lucide-react';
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface DateRange {
  from: Date | undefined;
  to: Date | undefined;
}

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface AdvancedFilters {
  search: string;
  dateRange: DateRange;
  status: string[];
  companies: string[];
  workers: string[];
  locations: string[];
  customFilters: Record<string, any>;
}

export interface FilterPreset {
  id: string;
  name: string;
  filters: AdvancedFilters;
  isDefault?: boolean;
}

interface AdvancedFiltersProps {
  filters: AdvancedFilters;
  onFiltersChange: (filters: AdvancedFilters) => void;
  statusOptions?: FilterOption[];
  companyOptions?: FilterOption[];
  workerOptions?: FilterOption[];
  locationOptions?: FilterOption[];
  presets?: FilterPreset[];
  onSavePreset?: (name: string, filters: AdvancedFilters) => void;
  onDeletePreset?: (presetId: string) => void;
  showExport?: boolean;
  showImport?: boolean;
  className?: string;
}

// ============================================================================
// DATE RANGE PRESETS
// ============================================================================

const DATE_PRESETS = [
  {
    label: 'Today',
    getValue: () => ({ from: new Date(), to: new Date() })
  },
  {
    label: 'Yesterday',
    getValue: () => {
      const yesterday = subDays(new Date(), 1);
      return { from: yesterday, to: yesterday };
    }
  },
  {
    label: 'Last 7 days',
    getValue: () => ({ from: subDays(new Date(), 6), to: new Date() })
  },
  {
    label: 'Last 30 days',
    getValue: () => ({ from: subDays(new Date(), 29), to: new Date() })
  },
  {
    label: 'This week',
    getValue: () => ({ from: startOfWeek(new Date()), to: endOfWeek(new Date()) })
  },
  {
    label: 'This month',
    getValue: () => ({ from: startOfMonth(new Date()), to: endOfMonth(new Date()) })
  },
];

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatDateRange = (dateRange: DateRange): string => {
  if (!dateRange.from) return 'Select date range';
  if (!dateRange.to) return format(dateRange.from, 'MMM dd, yyyy');
  if (dateRange.from.getTime() === dateRange.to.getTime()) {
    return format(dateRange.from, 'MMM dd, yyyy');
  }
  return `${format(dateRange.from, 'MMM dd')} - ${format(dateRange.to, 'MMM dd, yyyy')}`;
};

const getActiveFilterCount = (filters: AdvancedFilters): number => {
  let count = 0;
  if (filters.search) count++;
  if (filters.dateRange.from || filters.dateRange.to) count++;
  if (filters.status.length > 0) count++;
  if (filters.companies.length > 0) count++;
  if (filters.workers.length > 0) count++;
  if (filters.locations.length > 0) count++;
  return count;
};

// ============================================================================
// MULTI-SELECT COMPONENT
// ============================================================================

interface MultiSelectProps {
  options: FilterOption[];
  selected: string[];
  onSelectionChange: (selected: string[]) => void;
  placeholder: string;
  maxDisplay?: number;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selected,
  onSelectionChange,
  placeholder,
  maxDisplay = 3
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const handleToggle = (value: string) => {
    const newSelected = selected.includes(value)
      ? selected.filter(item => item !== value)
      : [...selected, value];
    onSelectionChange(newSelected);
  };
  
  const selectedOptions = options.filter(option => selected.includes(option.value));
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between text-left font-normal"
        >
          <div className="flex flex-wrap gap-1">
            {selectedOptions.length === 0 ? (
              <span className="text-muted-foreground">{placeholder}</span>
            ) : selectedOptions.length <= maxDisplay ? (
              selectedOptions.map(option => (
                <Badge key={option.value} variant="secondary" className="text-xs">
                  {option.label}
                </Badge>
              ))
            ) : (
              <>
                {selectedOptions.slice(0, maxDisplay).map(option => (
                  <Badge key={option.value} variant="secondary" className="text-xs">
                    {option.label}
                  </Badge>
                ))}
                <Badge variant="secondary" className="text-xs">
                  +{selectedOptions.length - maxDisplay} more
                </Badge>
              </>
            )}
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2 space-y-1 max-h-64 overflow-y-auto">
          {options.map(option => (
            <div
              key={option.value}
              className="flex items-center space-x-2 p-2 hover:bg-muted rounded-sm cursor-pointer"
              onClick={() => handleToggle(option.value)}
            >
              <Checkbox
                checked={selected.includes(option.value)}
                onChange={() => handleToggle(option.value)}
              />
              <span className="flex-1 text-sm">{option.label}</span>
              {option.count !== undefined && (
                <span className="text-xs text-muted-foreground">({option.count})</span>
              )}
            </div>
          ))}
        </div>
        {selected.length > 0 && (
          <>
            <Separator />
            <div className="p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelectionChange([])}
                className="w-full"
              >
                Clear All
              </Button>
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
};

// ============================================================================
// DATE RANGE PICKER COMPONENT
// ============================================================================

interface DateRangePickerProps {
  dateRange: DateRange;
  onDateRangeChange: (dateRange: DateRange) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  dateRange,
  onDateRangeChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between text-left font-normal"
        >
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>{formatDateRange(dateRange)}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3 space-y-3">
          {/* Date Presets */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Quick Select</Label>
            <div className="grid grid-cols-2 gap-2">
              {DATE_PRESETS.map(preset => (
                <Button
                  key={preset.label}
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onDateRangeChange(preset.getValue());
                    setIsOpen(false);
                  }}
                  className="justify-start text-xs"
                >
                  {preset.label}
                </Button>
              ))}
            </div>
          </div>
          
          <Separator />
          
          {/* Calendar */}
          <CalendarComponent
            mode="range"
            selected={dateRange}
            onSelect={(range) => {
              if (range) {
                onDateRangeChange(range as DateRange);
              }
            }}
            numberOfMonths={2}
          />
          
          <Separator />
          
          {/* Actions */}
          <div className="flex justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onDateRangeChange({ from: undefined, to: undefined });
                setIsOpen(false);
              }}
            >
              Clear
            </Button>
            <Button
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

// ============================================================================
// MAIN ADVANCED FILTERS COMPONENT
// ============================================================================

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  statusOptions = [],
  companyOptions = [],
  workerOptions = [],
  locationOptions = [],
  presets = [],
  onSavePreset,
  onDeletePreset,
  showExport = false,
  showImport = false,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [savePresetName, setSavePresetName] = useState('');
  const [showSavePreset, setShowSavePreset] = useState(false);
  
  const activeFilterCount = getActiveFilterCount(filters);
  
  const updateFilters = (updates: Partial<AdvancedFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };
  
  const clearAllFilters = () => {
    onFiltersChange({
      search: '',
      dateRange: { from: undefined, to: undefined },
      status: [],
      companies: [],
      workers: [],
      locations: [],
      customFilters: {}
    });
  };
  
  const applyPreset = (preset: FilterPreset) => {
    onFiltersChange(preset.filters);
  };
  
  const handleSavePreset = () => {
    if (savePresetName.trim() && onSavePreset) {
      onSavePreset(savePresetName.trim(), filters);
      setSavePresetName('');
      setShowSavePreset(false);
    }
  };
  
  const exportFilters = () => {
    const dataStr = JSON.stringify(filters, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `filters-${format(new Date(), 'yyyy-MM-dd')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };
  
  const importFilters = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedFilters = JSON.parse(e.target?.result as string);
          onFiltersChange(importedFilters);
        } catch (error) {
          console.error('Failed to import filters:', error);
        }
      };
      reader.readAsText(file);
    }
  };
  
  return (
    <Card className={cn('bg-card border-border', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-primary" />
            <CardTitle className="text-card-foreground">Advanced Filters</CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFilterCount} active
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>
        </div>
      </CardHeader>
      
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="space-y-6">
            {/* Search */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search across all fields..."
                  value={filters.search}
                  onChange={(e) => updateFilters({ search: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Date Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Date Range</Label>
              <DateRangePicker
                dateRange={filters.dateRange}
                onDateRangeChange={(dateRange) => updateFilters({ dateRange })}
              />
            </div>
            
            {/* Filter Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Status Filter */}
              {statusOptions.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Status</Label>
                  <MultiSelect
                    options={statusOptions}
                    selected={filters.status}
                    onSelectionChange={(status) => updateFilters({ status })}
                    placeholder="Select statuses"
                  />
                </div>
              )}
              
              {/* Company Filter */}
              {companyOptions.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Companies</Label>
                  <MultiSelect
                    options={companyOptions}
                    selected={filters.companies}
                    onSelectionChange={(companies) => updateFilters({ companies })}
                    placeholder="Select companies"
                  />
                </div>
              )}
              
              {/* Worker Filter */}
              {workerOptions.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Workers</Label>
                  <MultiSelect
                    options={workerOptions}
                    selected={filters.workers}
                    onSelectionChange={(workers) => updateFilters({ workers })}
                    placeholder="Select workers"
                  />
                </div>
              )}
              
              {/* Location Filter */}
              {locationOptions.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Locations</Label>
                  <MultiSelect
                    options={locationOptions}
                    selected={filters.locations}
                    onSelectionChange={(locations) => updateFilters({ locations })}
                    placeholder="Select locations"
                  />
                </div>
              )}
            </div>
            
            {/* Presets */}
            {presets.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Filter Presets</Label>
                <div className="flex flex-wrap gap-2">
                  {presets.map(preset => (
                    <div key={preset.id} className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset(preset)}
                        className="text-xs"
                      >
                        {preset.name}
                        {preset.isDefault && <Badge variant="secondary" className="ml-1 text-xs">Default</Badge>}
                      </Button>
                      {onDeletePreset && !preset.isDefault && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeletePreset(preset.id)}
                          className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Actions */}
            <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t">
              <div className="flex flex-wrap items-center gap-2">
                {onSavePreset && (
                  <div className="flex items-center gap-2">
                    {showSavePreset ? (
                      <>
                        <Input
                          placeholder="Preset name"
                          value={savePresetName}
                          onChange={(e) => setSavePresetName(e.target.value)}
                          className="w-32 h-8"
                          onKeyPress={(e) => e.key === 'Enter' && handleSavePreset()}
                        />
                        <Button size="sm" onClick={handleSavePreset}>
                          Save
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setShowSavePreset(false);
                            setSavePresetName('');
                          }}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowSavePreset(true)}
                      >
                        Save Preset
                      </Button>
                    )}
                  </div>
                )}
                
                {showExport && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportFilters}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                )}
                
                {showImport && (
                  <div>
                    <input
                      type="file"
                      accept=".json"
                      onChange={importFilters}
                      className="hidden"
                      id="import-filters"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => document.getElementById('import-filters')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      Import
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="text-xs text-muted-foreground">
                {activeFilterCount > 0 ? `${activeFilterCount} filter${activeFilterCount !== 1 ? 's' : ''} applied` : 'No filters applied'}
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default AdvancedFilters;
