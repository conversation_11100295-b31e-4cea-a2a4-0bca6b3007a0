import { prisma } from '@/lib/prisma';
import { Prisma, UserRole, ShiftStatus, JobStatus } from '@prisma/client';
import { AuthenticatedUser } from '@/lib/types';
import { applyShiftScope, applyJobScope } from '@/lib/permissions';
import { globalCache, cacheKeys, cacheTags } from '@/lib/cache';

// Configuration constants
const CONFIG = {
  CACHE_TIMES: {
    SHIFTS: 2 * 60 * 1000, // 2 minutes
    JOBS: 5 * 60 * 1000,   // 5 minutes
    USERS: 10 * 60 * 1000, // 10 minutes
  },
  TIMEOUTS: {
    DEFAULT: 15000,  // 15 seconds
    JOBS: 20000,     // 20 seconds
    USERS: 10000,    // 10 seconds
  },
  LIMITS: {
    MAX_METRICS_PER_QUERY: 100,
    MAX_TOTAL_METRICS: 1000,
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
  },
} as const;

interface QueryOptions {
  useCache?: boolean;
  cacheTime?: number;
  tags?: string[];
  timeout?: number;
}

interface PaginationOptions {
  page?: number;
  limit?: number;
  pageSize?: number;
  cursor?: string;
}

interface QueryMetrics {
  queryTime: number;
  cacheHit: boolean;
  resultCount: number;
  timestamp: number;
}

interface UserWithAvatar {
  id: string;
  name: string | null;
  email: string;
  role: UserRole;
  isActive: boolean;
  companyId: string | null;
  crew_chief_eligible: boolean | null;
  fork_operator_eligible: boolean | null;
  OSHA_10_Certifications: boolean | null;
  certifications: string | null;
  performance: number | null;
  avatarUrl: string | null;
  location: string | null;
  company?: {
    id: string;
    name: string;
  } | null;
}

interface AssignmentWithAvatar {
  id: string;
  userId: string;
  roleCode: string;
  status?: string;
  user: {
    id: string;
    name: string | null;
    avatarUrl: string | null;
  } | null;
}

export class EnhancedDatabaseService {
  private static instance: EnhancedDatabaseService;
  private queryMetrics: Map<string, QueryMetrics[]> = new Map();

  static getInstance(): EnhancedDatabaseService {
    if (!EnhancedDatabaseService.instance) {
      EnhancedDatabaseService.instance = new EnhancedDatabaseService();
    }
    return EnhancedDatabaseService.instance;
  }

  // ------------------------------
  // Cache & metrics helpers
  // ------------------------------
  private async executeWithCache<T>(
    key: string,
    queryFn: () => Promise<T>,
    options: QueryOptions = {}
  ): Promise<T> {
    const startTime = Date.now();
    const { useCache = true, cacheTime = CONFIG.CACHE_TIMES.SHIFTS, tags = [], timeout = CONFIG.TIMEOUTS.DEFAULT } = options;

    try {
      if (!useCache) {
        const result = await this.executeWithTimeout(queryFn, timeout);
        this.recordMetrics(key, Date.now() - startTime, false, this.getResultCount(result));
        return result;
      }

      const cached = globalCache.get<T>(key);
      if (cached && !cached.isStale) {
        this.recordMetrics(key, Date.now() - startTime, true, this.getResultCount(cached.data));
        return cached.data;
      }

      const result = await this.executeWithTimeout(queryFn, timeout);
      globalCache.set(key, result, cacheTime, tags);
      this.recordMetrics(key, Date.now() - startTime, false, this.getResultCount(result));
      return result;
    } catch (error) {
      console.error(`Database query failed for key: ${key}`, error);
      throw new Error(`Database operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async executeWithTimeout<T>(queryFn: () => Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      queryFn(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout')), timeout)
      ),
    ]);
  }

  private getResultCount(result: any): number {
    if (Array.isArray(result)) return result.length;
    if (result && typeof result === 'object' && 'total' in result) return result.total;
    return 1;
  }

  private recordMetrics(key: string, queryTime: number, cacheHit: boolean, resultCount: number) {
    if (!this.queryMetrics.has(key)) {
      this.queryMetrics.set(key, []);
    }
    const metrics = this.queryMetrics.get(key)!;
    metrics.push({
      queryTime,
      cacheHit,
      resultCount,
      timestamp: Date.now(),
    });

    if (metrics.length > CONFIG.LIMITS.MAX_METRICS_PER_QUERY) {
      metrics.splice(0, metrics.length - CONFIG.LIMITS.MAX_METRICS_PER_QUERY);
    }

    if (this.queryMetrics.size > CONFIG.LIMITS.MAX_TOTAL_METRICS) {
      const keysToDelete = Array.from(this.queryMetrics.keys())
        .slice(0, this.queryMetrics.size - CONFIG.LIMITS.MAX_TOTAL_METRICS + 1);
      keysToDelete.forEach(key => this.queryMetrics.delete(key));
    }
  }

  private generateCacheKey(prefix: string, filters: Record<string, any>): string {
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((result, key) => {
        result[key] = filters[key];
        return result;
      }, {} as Record<string, any>);

    return `${prefix}-${JSON.stringify(sortedFilters)}`;
  }

  private validatePaginationOptions(pagination: PaginationOptions): PaginationOptions {
    const { page = 1, limit, pageSize, cursor } = pagination;

    const validPage = Math.max(1, Math.floor(page));
    const validLimit = limit ? Math.min(Math.max(1, Math.floor(limit)), CONFIG.LIMITS.MAX_PAGE_SIZE) : undefined;
    const validPageSize = pageSize ? Math.min(Math.max(1, Math.floor(pageSize)), CONFIG.LIMITS.MAX_PAGE_SIZE) : CONFIG.LIMITS.DEFAULT_PAGE_SIZE;

    return {
      page: validPage,
      limit: validLimit,
      pageSize: validPageSize,
      cursor: cursor && typeof cursor === 'string' ? cursor : undefined,
    };
  }

  private mapAvatarUrl<T extends { id: string; avatarUrl?: string | null }>(obj: T): T {
    return { ...obj, avatarUrl: `/api/users/${obj.id}/avatar/image` };
  }

  // ------------------------------
  // Shifts
  // ------------------------------
  async getShiftsOptimized(user: AuthenticatedUser, filters: {
    status?: string;
    date?: string;
    search?: string;
    jobId?: string;
    pagination?: PaginationOptions;
  }) {
    const validatedPagination = this.validatePaginationOptions(filters.pagination || {});
    const { page = 1, limit = 50, cursor } = validatedPagination;
    const cacheKey = this.generateCacheKey('shifts-enhanced', { ...filters, pagination: validatedPagination });

    return this.executeWithCache(
      cacheKey,
      async () => {
        const { status, date, search, jobId } = filters;

        let where: Prisma.ShiftWhereInput = {};
        if (jobId) where.jobId = jobId;
        if (status && status !== 'all') {
          const statusMap: Record<string, ShiftStatus> = {
            'Upcoming': ShiftStatus.Pending,
            'Active': ShiftStatus.Active,
            'In Progress': ShiftStatus.InProgress,
            'Completed': ShiftStatus.Completed,
            'Cancelled': ShiftStatus.Cancelled,
          };
          where.status = statusMap[status];
        }

        if (date && date !== 'all') {
          const now = new Date();
          let startDate: Date, endDate: Date;

          switch (date) {
            case 'today':
              startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
              endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
              break;
            case 'tomorrow':
              startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
              endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
              break;
            case 'this_week': {
              const weekStart = new Date(now);
              weekStart.setDate(weekStart.getDate() - weekStart.getDay());
              startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
              endDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
              break;
            }
            case 'this_month':
              startDate = new Date(now.getFullYear(), now.getMonth(), 1);
              endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
              break;
            default:
              startDate = new Date(date);
              endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
          }

          where.date = { gte: startDate, lt: endDate };
        }

        if (search) {
          where.OR = [
            { job: { name: { contains: search, mode: 'insensitive' } } },
            { job: { company: { name: { contains: search, mode: 'insensitive' } } } },
            { location: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ];
        }

        where = applyShiftScope(user, where);

        const select: Prisma.ShiftSelect = {
          id: true,
          date: true,
          startTime: true,
          endTime: true,
          status: true,
          location: true,
          description: true,
          requiredCrewChiefs: true,
          requiredStagehands: true,
          requiredForkOperators: true,
          requiredReachForkOperators: true,
          requiredRiggers: true,
          job: {
            select: {
              id: true,
              name: true,
              company: { select: { id: true, name: true, company_logo_url: true } },
            },
          },
          assignedPersonnel: {
            select: {
              id: true,
              userId: true,
              roleCode: true,
              status: true,
              user: { select: { id: true, name: true, avatarUrl: true } },
            },
          },
          timesheets: { select: { id: true, status: true } },
        };

        const findManyOptions: Prisma.ShiftFindManyArgs = { where, select, orderBy: [{ date: 'desc' }, { startTime: 'asc' }], take: limit };
        if (cursor) { findManyOptions.cursor = { id: cursor }; findManyOptions.skip = 1; }
        else { findManyOptions.skip = (page - 1) * limit; }

        const [shifts, total] = await prisma.$transaction([prisma.shift.findMany(findManyOptions), prisma.shift.count({ where })]);

        type ShiftWithDetails = Prisma.ShiftGetPayload<{
          select: typeof select;
        }>;

        const transformedShifts = shifts.map((shift: ShiftWithDetails) => {
          const assignments = shift.assignedPersonnel || [];
          return {
            ...shift,
            assignedPersonnel: assignments.map(a => a.user ? { ...a, user: this.mapAvatarUrl(a.user) } : a),
          };
        });

        return {
          shifts: transformedShifts,
          total,
          pages: Math.ceil(total / limit),
          currentPage: page,
          hasNextPage: shifts.length === limit,
          nextCursor: shifts.length === limit ? shifts[shifts.length - 1].id : null,
        };
      },
      { useCache: true, cacheTime: CONFIG.CACHE_TIMES.SHIFTS, tags: [cacheTags.shifts, cacheTags.assignments], timeout: CONFIG.TIMEOUTS.DEFAULT }
    );
  }

  // ------------------------------
  // Jobs
  // ------------------------------
  async getJobsOptimized(user: AuthenticatedUser, filters: {
    companyId?: string;
    status?: string;
    search?: string;
    sortBy?: string;
    pagination?: PaginationOptions;
  }) {
    const validatedPagination = this.validatePaginationOptions(filters.pagination || {});
    const { limit = 100, cursor } = validatedPagination;
    const cacheKey = this.generateCacheKey('jobs-enhanced', { ...filters, pagination: validatedPagination });

    return this.executeWithCache(
      cacheKey,
      async () => {
        const { companyId, status, search, sortBy = 'recentShifts' } = filters;

        let where: Prisma.JobWhereInput = {};
        if (status && status !== 'all') where.status = status as JobStatus;
        if (companyId && companyId !== 'all') where.companyId = companyId;
        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { company: { name: { contains: search, mode: 'insensitive' } } },
          ];
        }
        where = applyJobScope(user, where);

        const orderBy = sortBy === 'recentShifts' ? [{ updatedAt: 'desc' }, { createdAt: 'desc' }] : [{ createdAt: 'desc' }];
        const shiftsWhere: Prisma.ShiftWhereInput = applyShiftScope(user, {});

        const findManyOptions: Prisma.JobFindManyArgs = {
          where,
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            startDate: true,
            endDate: true,
            location: true,
            createdAt: true,
            updatedAt: true,
            company: { select: { id: true, name: true, company_logo_url: true } },
            shifts: {
              where: shiftsWhere,
              select: {
                id: true,
                date: true,
                startTime: true,
                endTime: true,
                status: true,
                requiredCrewChiefs: true,
                requiredStagehands: true,
                requiredForkOperators: true,
                requiredReachForkOperators: true,
                requiredRiggers: true,
                assignedPersonnel: { select: { id: true, userId: true, roleCode: true, user: { select: { id: true, name: true, avatarUrl: true } } } },
                timesheets: { select: { id: true, status: true } },
              },
              orderBy: { date: 'desc' },
              take: 3,
            },
            _count: { select: { shifts: { where: shiftsWhere } } },
          },
          orderBy,
          take: limit,
        };

        if (cursor) { findManyOptions.cursor = { id: cursor }; findManyOptions.skip = 1; }

        type JobWithShifts = Prisma.JobGetPayload<{
          select: typeof findManyOptions.select;
        }>;

        const jobs = await prisma.job.findMany(findManyOptions);

        const total = await prisma.job.count({ where });
        const jobsWithFulfillment = jobs.map((job: JobWithShifts) => {
          const recentShifts = (job.shifts || []).map(shift => {
            type ShiftWithAssignedPersonnel = Prisma.ShiftGetPayload<{
              select: {
                id: true,
                date: true,
                startTime: true,
                endTime: true,
                status: true,
                requiredCrewChiefs: true,
                requiredStagehands: true,
                requiredForkOperators: true,
                requiredReachForkOperators: true,
                requiredRiggers: true,
                assignedPersonnel: { select: { id: true, userId: true, roleCode: true, user: { select: { id: true, name: true, avatarUrl: true } } } },
                timesheets: { select: { id: true, status: true } },
              }
            }>
            const required = (shift.requiredCrewChiefs ?? 0) +
                             (shift.requiredStagehands ?? 0) +
                             (shift.requiredForkOperators ?? 0) +
                             (shift.requiredReachForkOperators ?? 0) +
                             (shift.requiredRiggers ?? 0);

            const assigned = ((shift as unknown as ShiftWithAssignedPersonnel).assignedPersonnel || []).filter(p => p.userId).length;

            const transformedAssignments = ((shift as unknown as ShiftWithAssignedPersonnel).assignedPersonnel || []).map(a => a.user ? { ...a, user: this.mapAvatarUrl(a.user) } : a);

            return {
              ...shift,
              assignedPersonnel: transformedAssignments,
              fulfillment: `${assigned}/${required}`,
              fulfillmentPercentage: required > 0 ? Math.round((assigned / required) * 100) : 100,
              totalRequired: required,
              totalAssigned: assigned,
            };
          });

          return {
            ...job,
            recentShifts,
            totalShifts: job._count?.shifts || 0,
          };
        });

        return {
          jobs: jobsWithFulfillment,
          total,
          pages: Math.ceil(total / limit),
          hasNextPage: jobs.length === limit,
          nextCursor: jobs.length === limit ? jobs[jobs.length - 1].id : null,
        };
      },
      { useCache: true, cacheTime: CONFIG.CACHE_TIMES.JOBS, tags: [cacheTags.jobs, cacheTags.shifts], timeout: CONFIG.TIMEOUTS.JOBS }
    );
  }

  // ------------------------------
  // Users
  // ------------------------------
  async getUsersOptimized(filters: {
    role?: UserRole;
    companyId?: string;
    isActive?: boolean;
    excludeCompanyUsers?: boolean;
    search?: string;
    pagination?: PaginationOptions;
    fetchAll?: boolean;
  }) {
    const validatedPagination = this.validatePaginationOptions(filters.pagination || {});
    const { page = 1, pageSize = 20, cursor } = validatedPagination;
    const cacheKey = this.generateCacheKey('users-enhanced', { ...filters, pagination: validatedPagination });

    return this.executeWithCache(
      cacheKey,
      async () => {
        const { role, companyId, isActive = true, excludeCompanyUsers = false, search, fetchAll = false } = filters;

        const where: Prisma.UserWhereInput = { isActive };
        if (role) where.role = role;
        if (companyId) where.companyId = companyId;
        if (excludeCompanyUsers) where.role = { not: UserRole.CompanyUser };
        if (search) {
          where.OR = [{ name: { contains: search, mode: 'insensitive' } }, { email: { contains: search, mode: 'insensitive' } }];
        }

        const select = {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          companyId: true,
          crew_chief_eligible: true,
          fork_operator_eligible: true,
          OSHA_10_Certifications: true,
          certifications: true,
          performance: true,
          avatarUrl: true,
          location: true,
          company: { select: { id: true, name: true } },
        };

        if (fetchAll) {
          const users = await prisma.user.findMany({ where, select, orderBy: { name: 'asc' } });
          const usersWithAvatar = users.map(this.mapAvatarUrl);
          return usersWithAvatar;
        }

        const users = await prisma.user.findMany({
          where,
          select,
          orderBy: { name: 'asc' },
          take: pageSize,
          skip: (page - 1) * pageSize,
          ...(cursor ? { cursor: { id: cursor }, skip: 1 } : {}),
        });

        const total = await prisma.user.count({ where });

        const transformed = users.map(this.mapAvatarUrl);

        return {
          users: transformed,
          total,
          pages: Math.ceil(total / pageSize),
          currentPage: page,
          hasNextPage: users.length === pageSize,
          nextCursor: users.length === pageSize ? users[users.length - 1].id : null,
        };
      },
      { useCache: true, cacheTime: CONFIG.CACHE_TIMES.USERS, tags: [cacheTags.users], timeout: CONFIG.TIMEOUTS.USERS }
    );
  }
}

export default EnhancedDatabaseService.getInstance();