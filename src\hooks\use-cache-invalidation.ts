/**
 * Smart Cache Invalidation System
 * 
 * Provides intelligent cache invalidation strategies that:
 * - Automatically invalidate related queries
 * - Prevent unnecessary refetches
 * - Handle complex entity relationships
 * - Support optimistic updates
 * - Include memory leak prevention
 */

"use client";

import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useRef, useEffect } from 'react';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface InvalidationRule {
  entityType: string;
  relatedEntities: string[];
  conditions?: (queryKey: any[]) => boolean;
  delay?: number;
}

interface InvalidationOptions {
  immediate?: boolean;
  includeRelated?: boolean;
  predicate?: (query: any) => boolean;
  exact?: boolean;
}

interface CacheMetrics {
  totalQueries: number;
  staleQueries: number;
  fetchingQueries: number;
  errorQueries: number;
  memoryUsage: number;
}

// ============================================================================
// INVALIDATION RULES CONFIGURATION
// ============================================================================

const INVALIDATION_RULES: InvalidationRule[] = [
  {
    entityType: 'shifts',
    relatedEntities: ['jobs', 'timesheets', 'users', 'companies'],
    delay: 150, // Small delay to batch invalidations
  },
  {
    entityType: 'jobs',
    relatedEntities: ['shifts', 'companies', 'timesheets'],
    delay: 150,
  },
  {
    entityType: 'users',
    relatedEntities: ['shifts', 'timesheets'],
    delay: 150,
  },
  {
    entityType: 'companies',
    relatedEntities: ['jobs', 'shifts'],
    delay: 200, // Longer delay for less frequently changing data
  },
  {
    entityType: 'timesheets',
    relatedEntities: ['shifts', 'users'],
    delay: 100, // Shorter delay for time-sensitive data
  },
];

// ============================================================================
// MAIN CACHE INVALIDATION HOOK
// ============================================================================

export const useCacheInvalidation = () => {
  const queryClient = useQueryClient();
  const invalidationTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const invalidationQueue = useRef<Set<string>>(new Set());
  
  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      invalidationTimeouts.current.forEach(timeout => clearTimeout(timeout));
      invalidationTimeouts.current.clear();
    };
  }, []);
  
  // ============================================================================
  // CORE INVALIDATION FUNCTIONS
  // ============================================================================
  
  const invalidateByKey = useCallback(async (
    queryKey: string[],
    options: InvalidationOptions = {}
  ) => {
    const { immediate = false, exact = false } = options;
    
    if (immediate) {
      await queryClient.invalidateQueries({ 
        queryKey: exact ? queryKey : undefined,
        predicate: exact ? undefined : (query) => {
          const key = query.queryKey[0] as string;
          return queryKey.includes(key);
        }
      });
    } else {
      // Queue for batch invalidation
      queryKey.forEach(key => invalidationQueue.current.add(key));
      
      // Process queue after a short delay
      setTimeout(() => {
        if (invalidationQueue.current.size > 0) {
          const keysToInvalidate = Array.from(invalidationQueue.current);
          invalidationQueue.current.clear();
          
          keysToInvalidate.forEach(key => {
            queryClient.invalidateQueries({
              predicate: (query) => {
                const queryKey = query.queryKey[0] as string;
                return queryKey === key;
              }
            });
          });
        }
      }, 50);
    }
  }, [queryClient]);
  
  const invalidateByEntityType = useCallback(async (
    entityType: string,
    options: InvalidationOptions = {}
  ) => {
    const { includeRelated = true, immediate = false } = options;
    
    const rule = INVALIDATION_RULES.find(r => r.entityType === entityType);
    const entitiesToInvalidate = includeRelated && rule 
      ? [entityType, ...rule.relatedEntities]
      : [entityType];
    
    const invalidateEntities = async () => {
      await Promise.all(
        entitiesToInvalidate.map(entity =>
          queryClient.invalidateQueries({
            predicate: (query) => {
              const key = query.queryKey[0] as string;
              return key === entity;
            }
          })
        )
      );
    };
    
    if (immediate || !rule?.delay) {
      await invalidateEntities();
    } else {
      // Clear existing timeout for this entity
      const existingTimeout = invalidationTimeouts.current.get(entityType);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }
      
      // Set new timeout
      const timeout = setTimeout(invalidateEntities, rule.delay);
      invalidationTimeouts.current.set(entityType, timeout);
    }
  }, [queryClient]);
  
  const invalidateByEntityId = useCallback(async (
    entityType: string,
    entityId: string,
    options: InvalidationOptions = {}
  ) => {
    await queryClient.invalidateQueries({
      predicate: (query) => {
        const [type, id] = query.queryKey;
        return type === entityType && id === entityId;
      }
    });
    
    // Also invalidate related list queries
    if (options.includeRelated !== false) {
      await invalidateByEntityType(entityType, { ...options, immediate: true });
    }
  }, [queryClient, invalidateByEntityType]);
  
  // ============================================================================
  // MUTATION-BASED INVALIDATION
  // ============================================================================
  
  const invalidateAfterMutation = useCallback(async (
    mutationType: 'create' | 'update' | 'delete',
    entityType: string,
    entityId?: string,
    options: InvalidationOptions = {}
  ) => {
    switch (mutationType) {
      case 'create':
        // Invalidate list queries and related entities
        await invalidateByEntityType(entityType, { 
          includeRelated: true, 
          immediate: false,
          ...options 
        });
        break;
        
      case 'update':
        // Invalidate specific entity and related queries
        if (entityId) {
          await invalidateByEntityId(entityType, entityId, options);
        } else {
          await invalidateByEntityType(entityType, { 
            includeRelated: true, 
            immediate: false,
            ...options 
          });
        }
        break;
        
      case 'delete':
        // Immediately invalidate all related queries
        await invalidateByEntityType(entityType, { 
          includeRelated: true, 
          immediate: true,
          ...options 
        });
        break;
    }
  }, [invalidateByEntityType, invalidateByEntityId]);
  
  // ============================================================================
  // BULK OPERATIONS
  // ============================================================================
  
  const invalidateAll = useCallback(async () => {
    await queryClient.invalidateQueries();
  }, [queryClient]);
  
  const invalidateStale = useCallback(async () => {
    await queryClient.invalidateQueries({
      predicate: (query) => query.isStale()
    });
  }, [queryClient]);
  
  const invalidateErrors = useCallback(async () => {
    await queryClient.invalidateQueries({
      predicate: (query) => query.state.status === 'error'
    });
  }, [queryClient]);
  
  // ============================================================================
  // CACHE MANAGEMENT
  // ============================================================================
  
  const clearCache = useCallback(() => {
    queryClient.clear();
  }, [queryClient]);
  
  const removeQueries = useCallback((predicate: (query: any) => boolean) => {
    queryClient.removeQueries({ predicate });
  }, [queryClient]);
  
  const cancelQueries = useCallback(async (queryKey?: string[]) => {
    if (queryKey) {
      await queryClient.cancelQueries({ queryKey });
    } else {
      await queryClient.cancelQueries();
    }
  }, [queryClient]);
  
  // ============================================================================
  // CACHE METRICS & MONITORING
  // ============================================================================
  
  const getCacheMetrics = useCallback((): CacheMetrics => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      fetchingQueries: queries.filter(q => q.state.fetchStatus === 'fetching').length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      memoryUsage: queries.reduce((acc, q) => {
        const dataSize = JSON.stringify(q.state.data || {}).length;
        return acc + dataSize;
      }, 0),
    };
  }, [queryClient]);
  
  const optimizeCache = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    // Remove queries that haven't been used in the last 10 minutes
    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
    const staleCutoff = Date.now() - 15 * 60 * 1000; // 15 minutes
    
    queries.forEach(query => {
      const lastUsed = query.state.dataUpdatedAt || 0;
      const isVeryStale = lastUsed < staleCutoff;
      const hasError = query.state.status === 'error';
      const isUnused = lastUsed < tenMinutesAgo && !query.getObserversCount();
      
      if (isVeryStale || hasError || isUnused) {
        cache.remove(query);
      }
    });
  }, [queryClient]);
  
  // ============================================================================
  // PREFETCH INVALIDATION
  // ============================================================================
  
  const invalidateAndRefetch = useCallback(async (
    entityType: string,
    entityId?: string
  ) => {
    if (entityId) {
      // Invalidate and immediately refetch specific entity
      await queryClient.invalidateQueries({ queryKey: [entityType, entityId] });
      await queryClient.refetchQueries({ queryKey: [entityType, entityId] });
    } else {
      // Invalidate and refetch all entities of this type
      await queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0] === entityType
      });
      await queryClient.refetchQueries({
        predicate: (query) => query.queryKey[0] === entityType
      });
    }
  }, [queryClient]);
  
  // ============================================================================
  // RETURN API
  // ============================================================================
  
  return {
    // Core invalidation
    invalidateByKey,
    invalidateByEntityType,
    invalidateByEntityId,
    invalidateAfterMutation,
    
    // Bulk operations
    invalidateAll,
    invalidateStale,
    invalidateErrors,
    
    // Cache management
    clearCache,
    removeQueries,
    cancelQueries,
    
    // Monitoring & optimization
    getCacheMetrics,
    optimizeCache,
    
    // Advanced operations
    invalidateAndRefetch,
    
    // Direct query client access for advanced use cases
    queryClient,
  };
};

// ============================================================================
// SPECIALIZED HOOKS
// ============================================================================

export const useShiftInvalidation = () => {
  const { invalidateByEntityType, invalidateAfterMutation } = useCacheInvalidation();
  
  return {
    invalidateShifts: () => invalidateByEntityType('shifts'),
    invalidateAfterShiftCreate: () => invalidateAfterMutation('create', 'shifts'),
    invalidateAfterShiftUpdate: (shiftId: string) => 
      invalidateAfterMutation('update', 'shifts', shiftId),
    invalidateAfterShiftDelete: () => invalidateAfterMutation('delete', 'shifts'),
  };
};

export const useJobInvalidation = () => {
  const { invalidateByEntityType, invalidateAfterMutation } = useCacheInvalidation();
  
  return {
    invalidateJobs: () => invalidateByEntityType('jobs'),
    invalidateAfterJobCreate: () => invalidateAfterMutation('create', 'jobs'),
    invalidateAfterJobUpdate: (jobId: string) => 
      invalidateAfterMutation('update', 'jobs', jobId),
    invalidateAfterJobDelete: () => invalidateAfterMutation('delete', 'jobs'),
  };
};

export const useTimesheetInvalidation = () => {
  const { invalidateByEntityType, invalidateAfterMutation } = useCacheInvalidation();
  
  return {
    invalidateTimesheets: () => invalidateByEntityType('timesheets'),
    invalidateAfterTimesheetCreate: () => invalidateAfterMutation('create', 'timesheets'),
    invalidateAfterTimesheetUpdate: (timesheetId: string) => 
      invalidateAfterMutation('update', 'timesheets', timesheetId),
    invalidateAfterTimesheetApproval: (timesheetId: string) => 
      invalidateAfterMutation('update', 'timesheets', timesheetId),
  };
};

// ============================================================================
// CACHE MONITORING HOOK
// ============================================================================

export const useCacheMonitoring = (options: {
  enableAutoOptimization?: boolean;
  optimizationInterval?: number;
  logMetrics?: boolean;
} = {}) => {
  const { 
    enableAutoOptimization = true, 
    optimizationInterval = 5 * 60 * 1000, // 5 minutes
    logMetrics = process.env.NODE_ENV === 'development'
  } = options;
  
  const { getCacheMetrics, optimizeCache } = useCacheInvalidation();
  
  useEffect(() => {
    if (!enableAutoOptimization) return;
    
    const interval = setInterval(() => {
      const metrics = getCacheMetrics();
      
      if (logMetrics) {
        console.log('📊 Cache Metrics:', metrics);
      }
      
      // Auto-optimize if cache is getting large
      if (metrics.totalQueries > 350 || metrics.memoryUsage > 8192 * 1024) { // 1MB
        optimizeCache();
        
        if (logMetrics) {
          console.log('🧹 Cache optimized');
        }
      }
    }, optimizationInterval);
    
    return () => clearInterval(interval);
  }, [enableAutoOptimization, optimizationInterval, logMetrics, getCacheMetrics, optimizeCache]);
  
  return {
    getCacheMetrics,
    optimizeCache,
  };
};

export default useCacheInvalidation;
