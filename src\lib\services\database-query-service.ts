import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { UserRole, AuthenticatedUser } from '@/lib/types';
import { applyJobScope, applyShiftScope } from '@/lib/permissions';
import { globalCache, cacheKeys, cacheTags } from '@/lib/cache';
import { logDatabaseError } from '@/lib/error-handler';

type ShiftStatus = 'Pending' | 'Active' | 'InProgress' | 'Completed' | 'Cancelled';
type JobStatus = 'Pending' | 'Active' | 'OnHold' | 'Completed' | 'Cancelled';

interface QueryOptions {
  useCache?: boolean;
  cacheTime?: number;
  tags?: string[];
}

export class DatabaseQueryService {
  private static instance: DatabaseQueryService;

  static getInstance(): DatabaseQueryService {
    if (!DatabaseQueryService.instance) {
      DatabaseQueryService.instance = new DatabaseQueryService();
    }
    return DatabaseQueryService.instance;
  }

  private async executeWithCache<T>(
    key: string,
    queryFn: () => Promise<T>,
    options: QueryOptions = {}
  ): Promise<T> {
    const { useCache = true, cacheTime = 5 * 60 * 1000, tags = [] } = options;

    if (!useCache) {
      return queryFn();
    }

    const cached = globalCache.get<T>(key);
    if (cached && !cached.isStale) {
      return cached.data;
    }

    const result = await queryFn();
    globalCache.set(key, result, cacheTime, tags);
    return result;
  }

  // Fast cache key generation (avoid JSON.stringify)
// Optimized job queries with fast cache keys
async getJobsOptimized(user: AuthenticatedUser, filters: {
  companyId?: string;
  status?: string;
  search?: string;
  sortBy?: string;
  limit?: number;
}) {
  const cacheKey = this.createFastCacheKey('jobs', {
    ...filters,
    sortBy: filters.sortBy || 'recentShifts'
  });

  return this.executeWithCache(
    cacheKey,
    async () => {
      const { companyId, status, search, sortBy = 'recentShifts', limit = 100 } = filters;

      let where: any = {};

      if (status && status !== 'all') {
        where.status = status as JobStatus;
      }

      if (companyId && companyId !== 'all') {
        where.companyId = companyId;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { company: { name: { contains: search, mode: 'insensitive' } } },
        ];
      }

      where = applyJobScope(user, where);

      const orderBy = sortBy === 'recentShifts'
        ? [{ updatedAt: 'desc' as const }, { createdAt: 'desc' as const }]
        : [{ createdAt: 'desc' as const }];

      // Build shifts where clause for user filtering
      const shiftsWhere: any = applyShiftScope(user, {});

      const jobs = await prisma.job.findMany({
        where,
        include: {
          company: true,
          shifts: {
            where: shiftsWhere,
            select: {
              id: true,
              date: true,
              startTime: true,
              endTime: true,
              status: true,
              // Legacy numeric fields kept for backward-compat only
              requiredCrewChiefs: true,
              requiredStagehands: true,
              requiredForkOperators: true,
              requiredReachForkOperators: true,
              requiredRiggers: true,
              // Normalized requirements used by UI helpers
              workerRequirements: {
                select: {
                  workerTypeCode: true,
                  requiredCount: true,
                },
              },
              assignedPersonnel: {
                select: {
                  id: true,
                  userId: true,
                  roleCode: true,
                  status: true,
                  user: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
              timesheets: {
                select: {
                  id: true,
                  status: true,
                },
              },
            },
            orderBy: { date: 'desc' },
            take: 3,
          },
          _count: {
            select: {
              shifts: {
                where: shiftsWhere
              },
            },
          },
        },
        orderBy,
        take: limit,
      });

      // Calculate fulfillment efficiently and transform avatar data
      const total = await prisma.job.count({ where });
      const jobsWithFulfillment = await Promise.all(jobs.map(async (job) => {
        const allShifts = await prisma.shift.findMany({
          where: { jobId: job.id },
          select: { status: true },
        });

        const isCompleted = allShifts.every(shift => shift.status === 'Completed');
        const jobStatus = isCompleted ? 'Completed' : job.status;

        const recentShifts = job.shifts.map(shift => {
          // Prefer normalized workerRequirements; fall back to legacy fields if missing
          const normalizedReqs = Array.isArray((shift as any).workerRequirements)
            ? (shift as any).workerRequirements
            : [];
          const required = normalizedReqs.length > 0
            ? normalizedReqs.reduce((sum: number, r: any) => sum + (Number(r.requiredCount) || 0), 0)
            : ((shift.requiredCrewChiefs ?? 0) +
               (shift.requiredStagehands ?? 0) +
               (shift.requiredForkOperators ?? 0) +
               (shift.requiredReachForkOperators ?? 0) +
               (shift.requiredRiggers ?? 0));

          // Only count assignments that have actual users assigned (not placeholders) and exclude no-shows
          const assigned = shift.assignedPersonnel.filter(p => p.userId && p.status !== 'NoShow').length;
          const requested = required;

          // Transform assignedPersonnel to include avatarUrl
          const transformedAssignments = shift.assignedPersonnel.map(assignment => ({
            ...assignment,
            user: assignment.user ? {
              ...assignment.user,
              avatarUrl: `/api/users/${assignment.user.id}/avatar/image`,
            } : null,
          }));

          return {
            ...shift,
            assignedPersonnel: transformedAssignments,
            fulfillment: `${assigned}/${requested}`,
            fulfillmentPercentage: requested > 0 ? Math.round((assigned / requested) * 100) : 100,
            totalRequired: requested,
            totalAssigned: assigned
          };
        });

        return {
          ...job,
          status: jobStatus,
          recentShifts,
          totalShifts: job._count.shifts
        };
      }));

      return { jobs: jobsWithFulfillment, total };
    },
    {
      useCache: true,
      cacheTime: 5 * 60 * 1000, // 5 minutes for jobs
      tags: [cacheTags.jobs, cacheTags.shifts]
    }
  );
}

  // Optimized user queries with fast cache keys
  async getUsersOptimized(user: AuthenticatedUser, filters: {
    role?: UserRole;
    companyId?: string;
    isActive?: boolean;
    excludeCompanyUsers?: boolean;
    search?: string;
    page?: number;
    pageSize?: number;
    fetchAll?: boolean;
  }) {
    const cacheKey = this.createFastCacheKey('users', {
      ...filters,
      isActive: filters.isActive ? 'true' : 'false',
      excludeCompanyUsers: filters.excludeCompanyUsers ? 'true' : 'false',
      fetchAll: filters.fetchAll ? 'true' : 'false'
    });
    
    return this.executeWithCache(
      cacheKey,
      async () => {
        const {
          role,
          companyId,
          isActive = true,
          excludeCompanyUsers = false,
          search,
          page = 1,
          pageSize = 20,
          fetchAll = false
        } = filters;
        
        let where: any = { isActive };
        
        if (role) {
          where.role = role;
        }
        
        if (companyId) {
          where.companyId = companyId;
        }

        // Exclude CompanyUsers if requested
        if (excludeCompanyUsers) {
          where.role = { not: UserRole.CompanyUser };
        }

        // Add search functionality
        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
          ];
        }

        if (user.role === UserRole.CompanyUser) {
          where.companyId = user.companyId;
        }

        const select = {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          companyId: true,
          crew_chief_eligible: true,
          fork_operator_eligible: true,
          OSHA_10_Certifications: true,
          certifications: true,
          performance: true,
          location: true,
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        };

        if (fetchAll) {
          const users = await prisma.user.findMany({
            where,
            select,
            orderBy: { name: 'asc' },
          });
          
          // Transform avatarUrl to avatarUrl for each user
          const usersWithAvatarUrl = users.map(user => ({
            ...user,
            avatarUrl: `/api/users/${user.id}/avatar/image`,
          }));
          
          return {
            users: usersWithAvatarUrl,
            total: users.length,
            pages: 1,
            currentPage: 1,
          };
        }

        const [users, total] = await prisma.$transaction([
          prisma.user.findMany({
            where,
            select,
            orderBy: { name: 'asc' },
            skip: (page - 1) * pageSize,
            take: pageSize,
          }),
          prisma.user.count({ where }),
        ]);

        // Transform avatarUrl to avatarUrl for each user
        const usersWithAvatarUrl = users.map(user => ({
          ...user,
          avatarUrl: `/api/users/${user.id}/avatar/image`,
        }));

        return {
          users: usersWithAvatarUrl,
          total,
          pages: Math.ceil(total / pageSize),
          currentPage: page,
        };
      },
      {
        useCache: true,
        cacheTime: 10 * 60 * 1000, // 10 minutes for users
        tags: ['users']
      }
    );
  }

  // Dashboard data optimization
  async getCompanyDashboardOptimized(companyId: string) {
    const cacheKey = `company-dashboard-${companyId}`;
    
    return this.executeWithCache(
      cacheKey,
      async () => {
        const now = new Date();
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        // Use aggregation queries for better performance
        const [
          activeJobsCount,
          upcomingShiftsCount,
          completedShiftsCount,
          todayShiftsCount,
          weekShiftsCount,
          monthShiftsCount,
          recentJobs,
          upcomingShifts,
        ] = await prisma.$transaction([
          // Active jobs count
          prisma.job.count({
            where: { 
              companyId, 
              status: { in: ['Active', 'Pending'] }
            },
          }),
          
          // Upcoming shifts count
          prisma.shift.count({
            where: {
              job: { companyId },
              status: { in: ['Pending', 'Active'] },
              date: { gte: startOfToday },
            },
          }),
          
          // Completed shifts count
          prisma.shift.count({
            where: {
              job: { companyId },
              status: 'Completed',
            },
          }),
          
          // Today's shifts count
          prisma.shift.count({
            where: {
              job: { companyId },
              date: {
                gte: startOfToday,
                lt: new Date(startOfToday.getTime() + 24 * 60 * 60 * 1000),
              },
            },
          }),
          
          // This week's shifts count
          prisma.shift.count({
            where: {
              job: { companyId },
              date: { gte: startOfWeek },
            },
          }),
          
          // This month's shifts count
          prisma.shift.count({
            where: {
              job: { companyId },
              date: { gte: startOfMonth },
            },
          }),
          
          // Recent jobs
          prisma.job.findMany({
            where: { companyId },
            select: {
              id: true,
              name: true,
              status: true,
              startDate: true,
              endDate: true,
              company: true,
              _count: {
                select: {
                  shifts: true,
                },
              },
            },
            orderBy: { updatedAt: 'desc' },
            take: 5,
          }),
          
          // Upcoming shifts
          prisma.shift.findMany({
            where: {
              job: { companyId },
              status: { in: ['Pending', 'Active'] },
              date: { gte: startOfToday },
            },
            select: {
              id: true,
              date: true,
              startTime: true,
              endTime: true,
              status: true,
              location: true,
              job: {
                select: {
                  id: true,
                  name: true,
                },
              },
              _count: {
                select: {
                  assignedPersonnel: true,
                },
              },
            },
            orderBy: [{ date: 'asc' }, { startTime: 'asc' }],
            take: 5,
          }),
        ]);

        return {
          activeJobsCount,
          upcomingShiftsCount,
          completedShiftsCount,
          todayShiftsCount,
          weekShiftsCount,
          monthShiftsCount,
          recentJobs,
          upcomingShifts,
          lastUpdated: new Date().toISOString(),
        };
      },
      { 
        useCache: true, 
        cacheTime: 3 * 60 * 1000, // 3 minutes for dashboard
        tags: [cacheTags.jobs, cacheTags.shifts] 
      }
    );
  }

  // Clear cache for specific entities
  invalidateCache(tags: string[]) {
    tags.forEach(tag => globalCache.invalidateByTag(tag));
  }

  // Optimized shift queries with fast cache keys
  async getShiftsOptimized(user: AuthenticatedUser, filters: {
    status?: string;
    date?: string;
    search?: string;
    jobId?: string;
    crewChiefId?: string;
    workerId?: string;
    page?: number;
    limit?: number;
  }) {
    const cacheKey = this.createFastCacheKey('shifts', filters);
    
    // Skip cache for job-specific queries to ensure fresh data
    const useCache = !filters.jobId;

    return this.executeWithCache(
      cacheKey,
      async () => {
        const { status, date, search, jobId, crewChiefId, workerId, page = 1, limit = 50 } = filters;

        // Build optimized where clause
        let where: any = {};

        // Job-specific filtering - ensure this is applied correctly
        if (jobId) {
          where.jobId = jobId;
          // Log for debugging
          console.log(`Fetching shifts for job ID: ${jobId}`);
        }

        // CrewChief-specific filtering
        if (crewChiefId) {
          where.assignedPersonnel = {
            some: {
              userId: crewChiefId,
              roleCode: 'CC',
              status: { not: 'NoShow' }
            }
          };
          console.log(`Fetching shifts for crew chief ID: ${crewChiefId}`);
        }

        // Worker-specific filtering
        if (workerId) {
          where.assignedPersonnel = {
            some: {
              userId: workerId,
              status: { not: 'NoShow' }
            }
          };
          console.log(`Fetching shifts for worker ID: ${workerId}`);
        }

        // Additional filters
        if (status && status !== 'all') {
          const statusMap: Record<string, ShiftStatus> = {
            'Upcoming': 'Pending',
            'Active': 'Active',
            'In Progress': 'InProgress',
            'Completed': 'Completed',
            'Cancelled': 'Cancelled',
          };
          where.status = statusMap[status];
        }

        // Date filtering with optimized queries
        if (date && date !== 'all') {
          const now = new Date();
          let startDate: Date, endDate: Date;

          switch (date) {
            case 'today':
              startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
              endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
              break;
            case 'tomorrow':
              startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
              endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
              break;
            case 'this_week': {
              const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
              startDate = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate());
              endDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
              break;
            }
            case 'this_month':
              startDate = new Date(now.getFullYear(), now.getMonth(), 1);
              endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
              break;
            default:
              startDate = new Date(date);
              endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
          }

          where.date = { gte: startDate, lt: endDate };
        }

        // Search optimization
        if (search) {
          where.OR = [
            { job: { name: { contains: search, mode: 'insensitive' } } },
            { job: { company: { name: { contains: search, mode: 'insensitive' } } } },
            { location: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ];
        }

        where = applyShiftScope(user, where);

        // Optimized select with minimal data
        const select = {
          id: true,
          date: true,
          startTime: true,
          endTime: true,
          status: true,
          location: true,
          description: true,
          workerRequirements: {
            select: {
              id: true,
              requiredCount: true,
              workerTypeCode: true,
              workerType: {
                select: {
                  code: true,
                  name: true,
                },
              },
            },
          },
          job: {
            select: {
              id: true,
              name: true,
              company: {
                select: {
                  id: true,
                  name: true,
                  company_logo_url: true,
                },
              },
            },
          },
          assignedPersonnel: {
            select: {
              id: true,
              userId: true,
              roleCode: true,
              status: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  // Explicitly exclude avatarUrl to prevent session bloat
                },
              },
            },
          },
          timesheets: {
            select: {
              id: true,
              status: true,
            },
          },
        };

        // Execute optimized query with transaction
        const [shifts, total] = await prisma.$transaction([
          prisma.shift.findMany({
            where,
            select: {
              ...select, // Keep existing selections
            },
            orderBy: [
              { date: 'desc' },
              { startTime: 'asc' }
            ],
            skip: (page - 1) * limit,
            take: limit,
          }),
          prisma.shift.count({ where }),
        ]);


        return {
          shifts,
          total,
          pages: Math.ceil(total / limit),
          currentPage: page,
        };
      },
      {
        useCache,
        cacheTime: 60 * 1000, // 1 minute for shifts
        tags: ['shifts', ...(filters.jobId ? [`job-${filters.jobId}`] : [])]
      }
    );
  }

  // Fast cache key generation (avoid JSON.stringify)
  private createFastCacheKey = (prefix: string, filters: Record<string, any>): string => {
    const parts = [prefix];
    if (filters.userId) parts.push(`u:${filters.userId}`);
    if (filters.userRole) parts.push(`r:${filters.userRole}`);
    if (filters.companyId) parts.push(`c:${filters.companyId}`);
    if (filters.status) parts.push(`s:${filters.status}`);
    if (filters.date) parts.push(`d:${filters.date}`);
    if (filters.search) parts.push(`q:${filters.search.substring(0, 10)}`);
    if (filters.jobId) parts.push(`j:${filters.jobId}`);
    if (filters.crewChiefId) parts.push(`cc:${filters.crewChiefId}`);
    if (filters.workerId) parts.push(`w:${filters.workerId}`);
    if (filters.page) parts.push(`p:${filters.page}`);
    if (filters.limit) parts.push(`l:${filters.limit}`);
    // Add timestamp for job-specific queries to prevent caching issues
    if (filters.jobId) parts.push(`t:${Date.now()}`);
    return parts.join('-');
  };
}

export const dbQueryService = DatabaseQueryService.getInstance();
