/**
 * Enhanced PDF Generator - Wrapper around UnifiedPDFGenerator with additional features
 */

import { UnifiedPDFGenerator, PDFGenerationOptions } from './unified-pdf-generator';
import { prisma } from './prisma';
import { uploadToGCS } from './gcs';
import { pdfCacheManager } from './pdf-cache-manager';

export interface EnhancedPDFOptions extends PDFGenerationOptions {
  preferSigned?: boolean;
  storageOptions?: {
    saveToDatabase?: boolean;
    uploadToCloud?: boolean;
    cacheResult?: boolean;
  };
}

export class TimesheetPDFGenerator {
  private timesheetId: string;
  private generator: UnifiedPDFGenerator;

  constructor(timesheetId: string) {
    this.timesheetId = timesheetId;
    this.generator = new UnifiedPDFGenerator(timesheetId);
  }

  /**
   * Get PDF buffer with preference for signed version
   */
  async getPDFBuffer(preferSigned: boolean = false): Promise<Buffer> {
    try {
      // First check cache for signed/unsigned versions
      const cacheOptions: PDFGenerationOptions = { includeSignature: preferSigned };
      
      if (await pdfCacheManager.has(this.timesheetId, cacheOptions)) {
        const cachedBuffer = await pdfCacheManager.getBuffer(this.timesheetId, cacheOptions);
        if (cachedBuffer) {
          return cachedBuffer;
        }
      }

      // Generate new PDF with requested signature preference
      const options: PDFGenerationOptions = {
        includeSignature: preferSigned,
        format: 'letter',
        orientation: 'portrait',
        quality: 'standard'
      };

      return await this.generator.generatePDF(options);
    } catch (error) {
      console.error(`Error generating PDF for timesheet ${this.timesheetId}:`, error);
      throw error;
    }
  }

  /**
   * Generate and store PDF with enhanced options
   */
  async generateAndStore(options: EnhancedPDFOptions = {}): Promise<string> {
    try {
      const pdfBuffer = await this.getPDFBuffer(options.includeSignature || false);
      
      // Store in database if requested
      if (options.storageOptions?.saveToDatabase) {
        await this.saveToDatabase(pdfBuffer, options);
      }

      // Upload to cloud if requested
      if (options.storageOptions?.uploadToCloud && process.env.GCS_AVATAR_BUCKET) {
        const filename = this.generateFilename(options);
        const destination = `timesheets/${this.timesheetId}/${filename}`;
        return await uploadToGCS(pdfBuffer, destination, 'application/pdf');
      }

      // Return as data URL by default
      return `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;
    } catch (error) {
      console.error(`Error generating and storing PDF for timesheet ${this.timesheetId}:`, error);
      throw error;
    }
  }

  private async saveToDatabase(pdfBuffer: Buffer, options: EnhancedPDFOptions): Promise<void> {
    try {
      const pdfData = pdfBuffer.toString('base64');
      const fieldName = options.includeSignature ? 'signedPdfData' : 'unsignedPdfData';
      
      await prisma.timesheet.update({
        where: { id: this.timesheetId },
        data: {
          [fieldName]: pdfData,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error(`Error saving PDF to database for timesheet ${this.timesheetId}:`, error);
      throw error;
    }
  }

  private generateFilename(options: EnhancedPDFOptions): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const signatureType = options.includeSignature ? 'signed' : 'unsigned';
    return `timesheet-${this.timesheetId}-${signatureType}-${timestamp}.pdf`;
  }
}

/**
 * Generate unsigned timesheet PDF and store in database
 */
export async function generateUnsignedTimesheetPdf(timesheetId: string): Promise<string> {
  try {
    const generator = new TimesheetPDFGenerator(timesheetId);
    
    const options: EnhancedPDFOptions = {
      includeSignature: false,
      storageOptions: {
        saveToDatabase: true,
        cacheResult: true
      }
    };

    const pdfUrl = await generator.generateAndStore(options);
    
    console.log(`Generated unsigned PDF for timesheet ${timesheetId}`);
    return pdfUrl;
  } catch (error) {
    console.error(`Error generating unsigned PDF for timesheet ${timesheetId}:`, error);
    throw error;
  }
}

/**
 * Generate signed timesheet PDF and store in database
 */
export async function generateSignedTimesheetPdf(timesheetId: string, signatureType: 'company' | 'manager' | 'both' = 'both'): Promise<string> {
  try {
    const generator = new TimesheetPDFGenerator(timesheetId);
    
    const options: EnhancedPDFOptions = {
      includeSignature: true,
      signatureType,
      storageOptions: {
        saveToDatabase: true,
        uploadToCloud: true,
        cacheResult: true
      }
    };

    const pdfUrl = await generator.generateAndStore(options);
    
    console.log(`Generated signed PDF for timesheet ${timesheetId}`);
    return pdfUrl;
  } catch (error) {
    console.error(`Error generating signed PDF for timesheet ${timesheetId}:`, error);
    throw error;
  }
}

/**
 * Batch generate PDFs for multiple timesheets
 */
export async function batchGeneratePDFs(
  timesheetIds: string[], 
  options: EnhancedPDFOptions = {}
): Promise<Map<string, string | Error>> {
  const results = new Map<string, string | Error>();
  
  // Process in chunks to avoid overwhelming the system
  const chunkSize = 3;
  for (let i = 0; i < timesheetIds.length; i += chunkSize) {
    const chunk = timesheetIds.slice(i, i + chunkSize);
    
    const promises = chunk.map(async (timesheetId) => {
      try {
        const generator = new TimesheetPDFGenerator(timesheetId);
        const pdfUrl = await generator.generateAndStore(options);
        return { timesheetId, result: pdfUrl };
      } catch (error) {
        return { timesheetId, result: error as Error };
      }
    });

    const chunkResults = await Promise.allSettled(promises);
    
    chunkResults.forEach((promiseResult) => {
      if (promiseResult.status === 'fulfilled') {
        const { timesheetId, result } = promiseResult.value;
        results.set(timesheetId, result);
      } else {
        console.error('Failed to process timesheet in batch:', promiseResult.reason);
      }
    });
  }

  return results;
}

// Export the main classes and functions
export type { PDFGenerationOptions } from './unified-pdf-generator';