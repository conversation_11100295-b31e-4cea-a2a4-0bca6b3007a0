"use server";
import { AsyncLocalStorage } from 'async_hooks';
import type { AuthenticatedUser } from '@/lib/types';

// Store per-request context so Prisma middleware can access the current user
const als = new AsyncLocalStorage<{ user: AuthenticatedUser | null }>();

export function runWithRequestUser<T>(user: AuthenticatedUser | null, fn: () => Promise<T> | T): Promise<T> | T {
  return als.run({ user }, fn) as Promise<T> | T;
}

export function getRequestUser(): AuthenticatedUser | null {
  const store = als.getStore();
  return store?.user ?? null;
}