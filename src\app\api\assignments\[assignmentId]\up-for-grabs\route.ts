import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { pusher } from '@/lib/pusher';

export async function POST(
  req: Request,
  { params }: { params: { assignmentId: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user || session.user.role !== 'Admin') {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  const { assignmentId } = params;
  const adminUserId = session.user.id;

  try {
    const result = await prisma.$transaction(async (tx) => {
      const assignment = await tx.assignedPersonnel.findUnique({
        where: { id: assignmentId },
        include: {
          shift: {
            include: {
              job: true,
            },
          },
        },
      });

      if (!assignment) {
        throw new Error('Assignment not found');
      }

      if (assignment.status === 'UpForGrabs') {
        throw new Error('Shift is already up for grabs');
      }

      const updatedAssignment = await tx.assignedPersonnel.update({
        where: { id: assignmentId },
        data: {
          status: 'UpForGrabs',
          userId: null,
          offeredById: adminUserId,
          offeredAt: new Date(),
        },
      });

      const upForGrabsWorkers = await tx.user.findMany({
        where: {
          upForGrabsNotifications: true,
        },
      });

      if (upForGrabsWorkers.length > 0) {
        const notifications = upForGrabsWorkers.map(worker => ({
          userId: worker.id,
          type: 'SHIFT_UP_FOR_GRABS',
          title: 'Shift Available',
          message: `A shift is up for grabs for job "${assignment.shift.job.name}" on ${new Date(assignment.shift.startTime).toLocaleDateString()}.`,
          relatedShiftId: assignment.shiftId,
        }));

        await tx.notification.createMany({
          data: notifications,
        });

        for (const worker of upForGrabsWorkers) {
          await pusher.trigger(`user-${worker.id}`, 'notification', {
            type: 'SHIFT_UP_FOR_GRABS',
            title: 'Shift Available',
            message: `A shift is up for grabs for job "${assignment.shift.job.name}" on ${new Date(assignment.shift.startTime).toLocaleDateString()}.`,
            relatedShiftId: assignment.shiftId,
          });
        }
      }

      // Trigger a global event for all clients to refetch shifts
      await pusher.trigger('up-for-grabs', 'new-shift', {
        message: 'A new shift is up for grabs!',
        shiftId: assignment.shiftId,
      });

      return updatedAssignment;
    });

    return NextResponse.json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }
    if (error.message.includes('already up for grabs')) {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }
    console.error('Failed to set assignment as up for grabs:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
