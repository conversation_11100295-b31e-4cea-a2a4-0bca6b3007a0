import { getServerSession } from 'next-auth';
import { authOptions } from './auth-config';

/**
 * Verify if a user has permission to perform signature-based operations
 * This is a simplified version that uses NextAuth sessions
 */
export function verifySignatureRequest(companyId: string, userId: string): boolean {
  // For now, we'll allow any authenticated user to perform signature operations
  // In a production environment, you might want to add more specific checks
  // such as verifying the user belongs to the company or has specific permissions
  return !!userId && !!companyId;
}

/**
 * Get the current authenticated user using NextAuth
 */
export async function getCurrentAuthenticatedUser() {
  try {
    const session = await getServerSession(authOptions);
    return session?.user || null;
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}