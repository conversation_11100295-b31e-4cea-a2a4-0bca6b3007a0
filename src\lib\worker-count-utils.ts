/**
 * Utility functions for consistent worker count calculations across the application
 */

export interface WorkerCountData {
  assignedPersonnel?: Array<{
    user?: any;
    userId?: string;
    roleCode?: string;
    status?: string;
  }>;
  // New normalized requirements source
  workerRequirements?: Array<{ workerTypeCode?: string; roleCode?: string; requiredCount: number }>;
}

export interface WorkerNeeded {
  roleCode: string;
  roleName: string;
  needed: number;
  required: number;
  assigned: number;
}

/**
 * Calculate the total number of assigned personnel
 * This is the numerator in "X/Y workers" display
 * Only counts actual assignments (not placeholders) and excludes no-shows
 */
export function getAssignedWorkerCount(data: WorkerCountData): number {
  if (!data.assignedPersonnel) return 0;
  return data.assignedPersonnel.filter(p => 
    p && // FIXED: Handle null/undefined consistently
    p.userId && // Only count actual assignments, not placeholders
    p.status !== 'NoShow' // Exclude no-shows
  ).length;
}

/**
 * Calculate the total required worker slots (sum of all specific role requirements)
 * This is the denominator in "X/Y workers" display
 * 
 * Example: 2 Crew Chiefs + 3 Stagehands + 1 OSHA10 Stagehand + 1 Fork Operator = 7 total required slots
 */
export function getTotalRequiredWorkers(data: WorkerCountData): number {
  const reqs = Array.isArray(data.workerRequirements) ? data.workerRequirements : [];
  if (reqs.length > 0) {
    return reqs.reduce((sum, r) => sum + (Number(r.requiredCount) || 0), 0);
  }
  return 0;
}

/**
 * Get a formatted string showing assigned vs total workers
 */
export function getWorkerCountDisplay(data: WorkerCountData): string {
  const assigned = getAssignedWorkerCount(data);
  const total = getTotalRequiredWorkers(data);
  return `${assigned} of ${total} Workers Assigned`;
}

/**
 * Get worker count data for display components
 */
export function getWorkerCountData(data: WorkerCountData) {
  const assigned = getAssignedWorkerCount(data);
  const total = getTotalRequiredWorkers(data);
  const progress = total > 0 ? (assigned / total) * 100 : 0;
  
  return {
    assigned,
    total,
    progress,
    display: `${assigned} of ${total} Workers Assigned`
  };
}

/**
 * Calculate additional workers needed by role
 * Returns only roles that need additional workers
 */
export function getWorkersNeeded(data: WorkerCountData): WorkerNeeded[] {
  const reqs = Array.isArray(data.workerRequirements) ? data.workerRequirements : [];
  const byCode: Record<string, number> = {};
  reqs.forEach(r => {
    const code = (r.roleCode || r.workerTypeCode) as string | undefined;
    if (!code) return;
    byCode[code] = (byCode[code] || 0) + (Number(r.requiredCount) || 0);
  });

  const codes = Object.keys(byCode);
  const result: WorkerNeeded[] = [];

  codes.forEach(code => {
    const required = byCode[code] || 0;
    if (required === 0) return;
    const assigned = data.assignedPersonnel?.filter(p => p.roleCode === code && p.userId && p.status !== 'NoShow').length || 0;
    const needed = Math.max(0, required - assigned);
    if (needed > 0) {
      result.push({
        roleCode: code,
        roleName: code,
        needed,
        required,
        assigned,
      });
    }
  });

  return result;
}

/**
 * Get a summary of workers needed for display
 */
export function getWorkersNeededSummary(data: WorkerCountData): {
  totalNeeded: number;
  rolesSummary: string[];
  hasOpenings: boolean;
} {
  const workersNeeded = getWorkersNeeded(data);
  const totalNeeded = workersNeeded.reduce((sum, role) => sum + role.needed, 0);
  const rolesSummary = workersNeeded.map(role => `${role.needed} ${role.roleName}${role.needed > 1 ? 's' : ''}`);
  
  return {
    totalNeeded,
    rolesSummary,
    hasOpenings: totalNeeded > 0
  };
}

/**
 * Calculate total required workers for a shift
 * Standardized function used by both job cards and shift cards
 */
export function calculateShiftRequirements(shift: any): number {
  const reqs = Array.isArray(shift.workerRequirements) ? shift.workerRequirements : [];
  return reqs.reduce((sum: number, r: any) => sum + (Number(r.requiredCount) || 0), 0);
}

/**
 * Calculate assigned workers for a shift
 * Standardized function used by both job cards and shift cards
 * Only counts actual assignments (not placeholders) and excludes no-shows
 */
export function calculateAssignedWorkers(shift: any): number {
  if (!shift.assignedPersonnel) return 0;
  
  return shift.assignedPersonnel.filter((p: any) => {
    return p && p.userId && p.status !== 'NoShow';
  }).length;
}
