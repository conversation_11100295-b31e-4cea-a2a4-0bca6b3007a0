/**
 * Enhanced types for API validation and standardized endpoints
 */

import { z } from 'zod';

// Enhanced pagination schema
export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  pageSize: z.number().int().min(1).max(100).default(20), // alias for limit
});

// Enhanced filter schemas
export const ShiftFiltersSchema = z.object({
  // Basic filters
  jobId: z.string().uuid().optional(),
  companyId: z.string().uuid().optional(),
  date: z.string().optional(), // ISO date string
  dateRange: z.object({
    start: z.string(),
    end: z.string()
  }).optional(),
  
  // Status filters
  status: z.enum(['Scheduled', 'InProgress', 'Completed', 'Cancelled']).optional(),
  
  // Worker type filters
  workerType: z.enum([
    'CrewChief', 
    'Stagehand', 
    'ForkOperator', 
    'ReachForkOperator', 
    'Rigger', 
    'GeneralLaborer', 
    'Manager'
  ]).optional(),
  
  // Assignment filters
  isAssigned: z.boolean().optional(),
  hasUnfilledPositions: z.boolean().optional(),
  
  // Time filters
  startTime: z.string().optional(), // HH:mm format
  endTime: z.string().optional(),
  
  // Location filter
  location: z.string().optional(),
  
  // Search and sorting
  search: z.string().optional(),
  sortBy: z.enum(['date', 'startTime', 'jobName', 'location', 'status']).default('date'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  
  // Pagination
  pagination: PaginationSchema.optional(),
});

export const JobFiltersSchema = z.object({
  // Basic filters
  companyId: z.string().uuid().optional(),
  status: z.enum(['Active', 'Inactive', 'Completed', 'Cancelled']).optional(),
  
  // Date filters
  dateRange: z.object({
    start: z.string(),
    end: z.string()
  }).optional(),
  
  // Job type filters
  hasActiveShifts: z.boolean().optional(),
  hasUnfilledShifts: z.boolean().optional(),
  
  // Search and sorting
  search: z.string().optional(),
  sortBy: z.enum(['name', 'startDate', 'endDate', 'company', 'status']).default('startDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  
  // Pagination
  pagination: PaginationSchema.optional(),
});

export const UserFiltersSchema = z.object({
  // Basic filters
  companyId: z.string().uuid().optional(),
  role: z.enum(['Admin', 'Staff', 'CompanyUser', 'CrewChief', 'Worker']).optional(),
  
  // Status filters
  isActive: z.boolean().optional(),
  isAvailable: z.boolean().optional(),
  
  // Worker type filters (for workers)
  workerType: z.enum([
    'CrewChief', 
    'Stagehand', 
    'ForkOperator', 
    'ReachForkOperator', 
    'Rigger', 
    'GeneralLaborer', 
    'Manager'
  ]).optional(),
  
  // Assignment filters
  hasCurrentAssignments: z.boolean().optional(),
  availableForDate: z.string().optional(), // ISO date string
  
  // Search and sorting
  search: z.string().optional(),
  sortBy: z.enum(['name', 'email', 'role', 'createdAt', 'lastActive']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  
  // Pagination
  pagination: PaginationSchema.optional(),
});

// Enhanced creation schemas
export const CreateShiftSchema = z.object({
  jobId: z.string().uuid('Invalid job ID'),
  date: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date'),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  location: z.string().min(1, 'Location is required').max(200),
  description: z.string().optional(),
  
  // Worker requirements (normalized)
  workerRequirements: z.array(z.object({
    roleCode: z.string().optional(),
    workerTypeCode: z.string().optional(),
    requiredCount: z.number().int().min(0).default(0),
  })).default([]),
  
  // Optional fields
  specialInstructions: z.string().optional(),
  requiredCertifications: z.array(z.string()).optional(),
  payRate: z.number().positive().optional(),
  overtimeRate: z.number().positive().optional(),
});

export const UpdateShiftSchema = CreateShiftSchema.partial().extend({
  id: z.string().uuid('Invalid shift ID'),
  status: z.enum(['Scheduled', 'InProgress', 'Completed', 'Cancelled']).optional(),
});

export const CreateJobSchema = z.object({
  name: z.string().min(1, 'Job name is required').max(200),
  companyId: z.string().uuid('Invalid company ID'),
  description: z.string().optional(),
  
  // Date range
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid start date'),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid end date'),
  
  // Location and contact
  location: z.string().min(1, 'Location is required').max(200),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  contactEmail: z.string().email('Invalid email').optional(),
  
  // Job details
  jobType: z.string().max(50).optional(),
  priority: z.enum(['Low', 'Medium', 'High', 'Critical']).default('Medium'),
  estimatedTotalHours: z.number().positive().optional(),
  budgetedAmount: z.number().positive().optional(),
  
  // Requirements
  specialRequirements: z.string().optional(),
  requiredCertifications: z.array(z.string()).optional(),
  
  // Rates
  basePayRate: z.number().positive().optional(),
  overtimeRate: z.number().positive().optional(),
});

export const UpdateJobSchema = CreateJobSchema.partial().extend({
  id: z.string().uuid('Invalid job ID'),
  status: z.enum(['Active', 'Inactive', 'Completed', 'Cancelled']).optional(),
});

// Enhanced user schemas
export const CreateUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required').max(100),
  role: z.enum(['Admin', 'Staff', 'CompanyUser', 'CrewChief', 'Worker']),
  
  // Optional fields
  phone: z.string().max(20).optional(),
  companyId: z.string().uuid().optional(),
  
  // Worker-specific fields
  workerType: z.enum([
    'CrewChief', 
    'Stagehand', 
    'ForkOperator', 
    'ReachForkOperator', 
    'Rigger', 
    'GeneralLaborer', 
    'Manager'
  ]).optional(),
  
  // Availability and preferences
  isAvailable: z.boolean().default(true),
  preferredShiftTimes: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  emergencyContact: z.object({
    name: z.string(),
    phone: z.string(),
    relationship: z.string()
  }).optional(),
});

export const UpdateUserSchema = CreateUserSchema.partial().extend({
  id: z.string().uuid('Invalid user ID'),
  isActive: z.boolean().optional(),
  lastActiveAt: z.string().optional(),
});

// Enhanced assignment schemas
export const AssignPersonnelSchema = z.object({
  shiftId: z.string().uuid('Invalid shift ID'),
  userId: z.string().uuid('Invalid user ID'),
  workerType: z.enum([
    'CrewChief', 
    'Stagehand', 
    'ForkOperator', 
    'ReachForkOperator', 
    'Rigger', 
    'GeneralLaborer', 
    'Manager'
  ]),
  isConfirmed: z.boolean().default(false),
  notes: z.string().optional(),
});

// Enhanced timesheet schemas
export const TimesheetEntrySchema = z.object({
  timesheetId: z.string().uuid('Invalid timesheet ID'),
  clockIn: z.string().optional(), // ISO datetime string
  clockOut: z.string().optional(), // ISO datetime string
  breakStart: z.string().optional(),
  breakEnd: z.string().optional(),
  notes: z.string().optional(),
});

// API response schemas
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }).optional(),
  meta: z.object({
    requestId: z.string(),
    timestamp: z.string(),
    version: z.string(),
  }).optional(),
});

// Export all types
export type ShiftFilters = z.infer<typeof ShiftFiltersSchema>;
export type JobFilters = z.infer<typeof JobFiltersSchema>;
export type UserFilters = z.infer<typeof UserFiltersSchema>;
export type CreateShift = z.infer<typeof CreateShiftSchema>;
export type UpdateShift = z.infer<typeof UpdateShiftSchema>;
export type CreateJob = z.infer<typeof CreateJobSchema>;
export type UpdateJob = z.infer<typeof UpdateJobSchema>;
export type CreateUser = z.infer<typeof CreateUserSchema>;
export type UpdateUser = z.infer<typeof UpdateUserSchema>;
export type AssignPersonnel = z.infer<typeof AssignPersonnelSchema>;
export type TimesheetEntry = z.infer<typeof TimesheetEntrySchema>;
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;