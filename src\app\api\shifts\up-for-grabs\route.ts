import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { WorkerStatus } from '@prisma/client';
import { isBuildTime } from '@/lib/build-time-check';

export async function GET() {
  // Avoid touching Prisma enums during static generation/build
  if (isBuildTime()) {
    return NextResponse.json([]);
  }

  try {
    const upForGrabsAssignments = await prisma.assignedPersonnel.findMany({
      where: {
        status: WorkerStatus.UpForGrabs,
      },
      include: {
        shift: {
          include: {
            job: {
              include: {
                company: true,
              },
            },
          },
        },
      },
      orderBy: {
        shift: {
          startTime: 'asc',
        },
      },
    });

    return NextResponse.json(upForGrabsAssignments);
  } catch (error) {
    console.error('Error fetching up-for-grabs shifts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}