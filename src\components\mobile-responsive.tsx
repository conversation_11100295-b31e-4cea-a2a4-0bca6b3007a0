/**
 * Mobile Responsive Components
 * 
 * Provides mobile-first responsive components that:
 * - Adapt layouts for different screen sizes
 * - Optimize touch interactions
 * - Improve accessibility on mobile devices
 * - Handle orientation changes
 * - Provide swipe gestures
 */

"use client";

import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, Menu, X, MoreVertical, Filter, Search } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  minItemWidth?: number;
  gap?: number;
  breakpoints?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  onTap?: () => void;
  onLongPress?: () => void;
  swipeActions?: {
    left?: { icon: React.ReactNode; action: () => void; color?: string };
    right?: { icon: React.ReactNode; action: () => void; color?: string };
  };
}

interface MobileNavigationProps {
  title: string;
  onBack?: () => void;
  actions?: React.ReactNode;
  className?: string;
}

interface TouchOptimizedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
}

interface SwipeableListProps {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  onSwipeLeft?: (item: any, index: number) => void;
  onSwipeRight?: (item: any, index: number) => void;
  className?: string;
}

// ============================================================================
// HOOKS
// ============================================================================

const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    media.addListener(listener);
    return () => media.removeListener(listener);
  }, [matches, query]);

  return matches;
};

const useTouch = () => {
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    setIsTouch('ontouchstart' in window || navigator.maxTouchPoints > 0);
  }, []);

  return isTouch;
};

const useLongPress = (callback: () => void, delay: number = 500) => {
  const [longPressTriggered, setLongPressTriggered] = useState(false);
  const timeout = useRef<NodeJS.Timeout>();
  const target = useRef<EventTarget>();

  const start = (event: React.TouchEvent | React.MouseEvent) => {
    if (event.type === 'mousedown' && (event as React.MouseEvent).button !== 0) {
      return;
    }
    
    target.current = event.target;
    timeout.current = setTimeout(() => {
      callback();
      setLongPressTriggered(true);
    }, delay);
  };

  const clear = (event: React.TouchEvent | React.MouseEvent, shouldTriggerClick = true) => {
    timeout.current && clearTimeout(timeout.current);
    shouldTriggerClick && !longPressTriggered && target.current === event.target;
    setLongPressTriggered(false);
  };

  return {
    onMouseDown: start,
    onTouchStart: start,
    onMouseUp: clear,
    onMouseLeave: (e: React.MouseEvent) => clear(e, false),
    onTouchEnd: clear,
  };
};

// ============================================================================
// RESPONSIVE GRID COMPONENT
// ============================================================================

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  minItemWidth = 280,
  gap = 24,
  breakpoints = { sm: 1, md: 2, lg: 3, xl: 4 }
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');
  
  const getColumns = () => {
    if (isMobile) return breakpoints.sm || 1;
    if (isTablet) return breakpoints.md || 2;
    return breakpoints.lg || 3;
  };

  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: `repeat(${getColumns()}, minmax(${minItemWidth}px, 1fr))`,
    gap: `${gap}px`,
  };

  return (
    <div 
      className={cn('w-full', className)}
      style={gridStyle}
    >
      {children}
    </div>
  );
};

// ============================================================================
// MOBILE CARD COMPONENT
// ============================================================================

export const MobileCard: React.FC<MobileCardProps> = ({
  children,
  className,
  onTap,
  onLongPress,
  swipeActions
}) => {
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const startX = useRef(0);
  const currentX = useRef(0);
  
  const longPressProps = useLongPress(onLongPress || (() => {}));
  const isTouch = useTouch();

  const handleTouchStart = (e: React.TouchEvent) => {
    startX.current = e.touches[0].clientX;
    setIsDragging(true);
    if (longPressProps.onTouchStart) {
      longPressProps.onTouchStart(e);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    currentX.current = e.touches[0].clientX;
    const diff = currentX.current - startX.current;
    
    // Limit swipe distance
    const maxSwipe = 100;
    const limitedDiff = Math.max(-maxSwipe, Math.min(maxSwipe, diff));
    setSwipeOffset(limitedDiff);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    setIsDragging(false);
    const diff = currentX.current - startX.current;
    
    // Trigger swipe actions if threshold is met
    if (Math.abs(diff) > 50) {
      if (diff > 0 && swipeActions?.right) {
        swipeActions.right.action();
      } else if (diff < 0 && swipeActions?.left) {
        swipeActions.left.action();
      }
    }
    
    // Reset position
    setSwipeOffset(0);
    
    if (longPressProps.onTouchEnd) {
      longPressProps.onTouchEnd(e);
    }
  };

  const handleClick = () => {
    if (!isDragging && onTap) {
      onTap();
    }
  };

  return (
    <div className="relative overflow-hidden">
      {/* Swipe Actions Background */}
      {swipeActions && (
        <>
          {swipeActions.left && (
            <div 
              className={cn(
                'absolute inset-y-0 right-0 flex items-center justify-center w-20 transition-opacity',
                swipeActions.left.color || 'bg-destructive',
                swipeOffset < -20 ? 'opacity-100' : 'opacity-0'
              )}
            >
              {swipeActions.left.icon}
            </div>
          )}
          {swipeActions.right && (
            <div 
              className={cn(
                'absolute inset-y-0 left-0 flex items-center justify-center w-20 transition-opacity',
                swipeActions.right.color || 'bg-primary',
                swipeOffset > 20 ? 'opacity-100' : 'opacity-0'
              )}
            >
              {swipeActions.right.icon}
            </div>
          )}
        </>
      )}
      
      {/* Main Card */}
      <Card
        className={cn(
          'transition-transform cursor-pointer select-none',
          'hover:shadow-md active:scale-[0.98]',
          isTouch && 'touch-manipulation',
          className
        )}
        style={{ transform: `translateX(${swipeOffset}px)` }}
        onClick={handleClick}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        {...(onLongPress ? longPressProps : {})}
      >
        {children}
      </Card>
    </div>
  );
};

// ============================================================================
// MOBILE NAVIGATION COMPONENT
// ============================================================================

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  title,
  onBack,
  actions,
  className
}) => {
  return (
    <div className={cn(
      'flex items-center justify-between p-4 bg-background border-b sticky top-0 z-10',
      className
    )}>
      <div className="flex items-center gap-3">
        {onBack && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-2 h-auto"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        )}
        <h1 className="text-lg font-semibold truncate">{title}</h1>
      </div>
      
      {actions && (
        <div className="flex items-center gap-2">
          {actions}
        </div>
      )}
    </div>
  );
};

// ============================================================================
// TOUCH OPTIMIZED BUTTON COMPONENT
// ============================================================================

export const TouchOptimizedButton: React.FC<TouchOptimizedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  className,
  disabled
}) => {
  const sizeClasses = {
    sm: 'min-h-[40px] px-3 py-2 text-sm',
    md: 'min-h-[44px] px-4 py-2.5 text-base',
    lg: 'min-h-[48px] px-6 py-3 text-lg'
  };

  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
  };

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'touch-manipulation select-none transition-all active:scale-95',
        sizeClasses[size],
        variantClasses[variant],
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      {children}
    </Button>
  );
};

// ============================================================================
// MOBILE FILTER DRAWER COMPONENT
// ============================================================================

interface MobileFilterDrawerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  trigger?: React.ReactNode;
}

export const MobileFilterDrawer: React.FC<MobileFilterDrawerProps> = ({
  children,
  title = "Filters",
  description = "Adjust your search criteria",
  trigger
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  
  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Filter className="h-4 w-4 mr-2" />
      Filters
    </Button>
  );

  if (isMobile) {
    return (
      <Drawer>
        <DrawerTrigger asChild>
          {trigger || defaultTrigger}
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>{title}</DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>
          <div className="px-4 pb-4 max-h-[70vh] overflow-y-auto">
            {children}
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        {trigger || defaultTrigger}
      </SheetTrigger>
      <SheetContent side="right" className="w-full sm:max-w-md">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        <div className="mt-6 overflow-y-auto max-h-[calc(100vh-120px)]">
          {children}
        </div>
      </SheetContent>
    </Sheet>
  );
};

// ============================================================================
// SWIPEABLE LIST COMPONENT
// ============================================================================

export const SwipeableList: React.FC<SwipeableListProps> = ({
  items,
  renderItem,
  onSwipeLeft,
  onSwipeRight,
  className
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {items.map((item, index) => (
        <MobileCard
          key={item.id || index}
          swipeActions={{
            left: onSwipeLeft ? {
              icon: <ChevronLeft className="h-5 w-5 text-white" />,
              action: () => onSwipeLeft(item, index),
              color: 'bg-destructive'
            } : undefined,
            right: onSwipeRight ? {
              icon: <ChevronRight className="h-5 w-5 text-white" />,
              action: () => onSwipeRight(item, index),
              color: 'bg-primary'
            } : undefined
          }}
        >
          {renderItem(item, index)}
        </MobileCard>
      ))}
    </div>
  );
};

// ============================================================================
// MOBILE SEARCH BAR COMPONENT
// ============================================================================

interface MobileSearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
}

export const MobileSearchBar: React.FC<MobileSearchBarProps> = ({
  value,
  onChange,
  placeholder = "Search...",
  onFocus,
  onBlur,
  className
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className={cn(
      'relative transition-all duration-200',
      isFocused && 'transform scale-105',
      className
    )}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        onFocus={() => {
          setIsFocused(true);
          onFocus?.();
        }}
        onBlur={() => {
          setIsFocused(false);
          onBlur?.();
        }}
        className={cn(
          'w-full pl-10 pr-4 py-3 text-base',
          'bg-background border border-border rounded-lg',
          'focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent',
          'placeholder:text-muted-foreground',
          'touch-manipulation'
        )}
      />
      {value && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onChange('')}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

// ============================================================================
// MOBILE OPTIMIZED CARD GRID
// ============================================================================

interface MobileCardGridProps {
  items: any[];
  renderCard: (item: any, index: number) => React.ReactNode;
  loading?: boolean;
  loadingCount?: number;
  emptyState?: React.ReactNode;
  className?: string;
}

export const MobileCardGrid: React.FC<MobileCardGridProps> = ({
  items,
  renderCard,
  loading = false,
  loadingCount = 6,
  emptyState,
  className
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  
  if (loading) {
    return (
      <ResponsiveGrid className={className}>
        {Array.from({ length: loadingCount }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6 space-y-4">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="h-4 bg-muted rounded w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </ResponsiveGrid>
    );
  }

  if (items.length === 0 && emptyState) {
    return <div className={className}>{emptyState}</div>;
  }

  return (
    <ResponsiveGrid 
      className={className}
      minItemWidth={isMobile ? 280 : 320}
      gap={isMobile ? 16 : 24}
    >
      {items.map((item, index) => renderCard(item, index))}
    </ResponsiveGrid>
  );
};

export default {
  ResponsiveGrid,
  MobileCard,
  MobileNavigation,
  TouchOptimizedButton,
  MobileFilterDrawer,
  SwipeableList,
  MobileSearchBar,
  MobileCardGrid,
};