"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Calendar, MapPin } from "lucide-react";
import { ShiftWithDetails } from "@/lib/types";
import GoogleMap from './GoogleMap';

interface ShiftInfoCardProps {
  shift: ShiftWithDetails;
}

export function ShiftInfoCard({ shift }: ShiftInfoCardProps) {
  const location = shift.location || shift.job?.location;

  const fullAddress = location;

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center gap-2 pb-2">
        <Calendar className="h-5 w-5 text-muted-foreground" />
        <CardTitle className="text-lg">Shift Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Status</span>
            <span className="font-medium">{shift.status}</span>
          </div>
          <div className="flex justify-between items-start">
            <span className="text-muted-foreground flex items-center">
              <MapPin className="h-4 w-4 mr-2" />
              Location
            </span>
            <span className="font-medium text-right">{location || 'Not specified'}</span>
          </div>
        </div>
        {fullAddress && <GoogleMap shift={shift} />}
      </CardContent>
    </Card>
  );
}
