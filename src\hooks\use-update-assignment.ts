import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

async function updateAssignment({ assignmentId, userId, roleCode }: { assignmentId: string, userId: string | null, roleCode?: string }) {
  const response = await fetch(`/api/assignments/${assignmentId}`,
    {
      method: 'PUT',
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, roleCode }),
    });

  if (!response.ok) {
    throw new Error('Failed to update assignment');
  }

  return response.json();
}

export function useUpdateAssignment() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(updateAssignment, {
    onSuccess: () => {
      queryClient.invalidateQueries(['shifts']);
      toast({
        title: 'Assignment Updated',
        description: 'The worker assignment has been updated.',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update assignment.',
        variant: 'destructive',
      });
    },
  });
}
