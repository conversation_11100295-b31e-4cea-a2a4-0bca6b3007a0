"use client";
import { useRouter, use<PERSON>ara<PERSON> } from "next/navigation";
import { useUser } from "@/hooks/use-user";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { VisualJobTimeline } from "@/components/visual-job-timeline";
import { useState, useMemo, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { JobResponse } from "@/types/api";

// Timeline-specific types for the scheduling view (matching API response)
interface TimelineCrewChief {
  id: string;
  assignmentId: string;
  name: string;
  avatar: string;
  status: string;
  roleCode: string;
}

interface TimelineWorker {
  id: string;
  assignmentId: string;
  name: string;
  avatar: string;
  status: string;
  roleCode: string;
}

interface TimelineShift {
  id: string;
  status: string;
  startTime: string;
  endTime: string;
  date: string;
  crewChiefs: TimelineCrewChief[];
  workers: Record<string, TimelineWorker[]>; // Workers grouped by role code
  location?: string;
  notes?: string;
  description?: string;
  totalAssigned?: number;
  staffingCompletion?: number;
}

interface SchedulingTimelineData {
  job: JobResponse;
  shifts: TimelineShift[];
}

// Custom hook for scheduling timeline data
function useSchedulingTimeline(jobId: string) {
  return useQuery<SchedulingTimelineData>({
    queryKey: ['scheduling-timeline', jobId],
    queryFn: async (): Promise<SchedulingTimelineData> => {
      const response = await fetch(`/api/jobs/${jobId}/scheduling-timeline`);
      if (!response.ok) {
        throw new Error('Failed to fetch scheduling timeline data');
      }
      return response.json();
    },
    enabled: !!jobId,
    staleTime: 60000, // 30 seconds
    gcTime: 300000, // 5 minutes
  });
}

export default function JobSchedulingTimelinePage() {
  const { id: jobId } = useParams();
  const router = useRouter();
  const { user } = useUser();
  const [selectedCrewChief, setSelectedCrewChief] = useState<string>("all");
  const [refreshKey, setRefreshKey] = useState(0);

  const { 
    data: timelineData, 
    isLoading, 
    isError, 
    refetch 
  } = useSchedulingTimeline(jobId as string);

  // Force refetch on initial load
  useEffect(() => {
    if (jobId) {
      console.log(`🔄 [Page] Forcing refetch for jobId: ${jobId}`);
      refetch();
    }
  }, [jobId, refetch]);

  const job = timelineData?.job;
  const allShifts = timelineData?.shifts || [];

  // Debug logging
  useEffect(() => {
    console.log(`🏗️ [Page] Job Data:`, job?.name || 'Loading...');
    console.log(`📊 [Page] Timeline Data:`, { 
      isLoading, 
      isError, 
      shiftsCount: allShifts.length,
      jobId: jobId as string,
      fullData: timelineData
    });
    if (allShifts.length > 0) {
      console.log(`📋 [Page] First few shifts:`, allShifts.slice(0, 3).map((s: TimelineShift) => ({ 
        id: s.id, 
        status: s.status,
        startTime: s.startTime,
        crewChiefsCount: s.crewChiefs?.length || 0,
        crewChiefs: s.crewChiefs,
        workersStructure: typeof s.workers === 'object' ? Object.keys(s.workers) : 'not object',
        workers: s.workers
      })));
      
      // Additional debugging for data structure
      const firstShift = allShifts[0];
      console.log(`🔍 [Page] First shift detailed structure:`, {
        crewChiefsIsArray: Array.isArray(firstShift.crewChiefs),
        crewChiefsLength: firstShift.crewChiefs?.length,
        workersType: typeof firstShift.workers,
        workersKeys: typeof firstShift.workers === 'object' && firstShift.workers ? Object.keys(firstShift.workers) : 'no keys'
      });
    }
  }, [job, isLoading, isError, allShifts, jobId, timelineData]);

  // Extract unique crew chiefs from all shifts (updated for new data structure)
  const crewChiefs = useMemo(() => {
    const chiefs = new Map<string, { id: string; name: string }>();

    allShifts.forEach((shift: TimelineShift) => {
      // The API now returns crewChiefs as a direct array
      if (shift.crewChiefs && Array.isArray(shift.crewChiefs)) {
        shift.crewChiefs.forEach((crewChief: TimelineCrewChief) => {
          if (crewChief.name) {
            // Use the user id for consistent filtering
            chiefs.set(crewChief.id, {
              id: crewChief.id,
              name: crewChief.name
            });
          }
        });
      }
    });

    return Array.from(chiefs.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [allShifts]);

  // Filter shifts based on selected crew chief (updated for new data structure)
  const shifts = useMemo(() => {
    if (selectedCrewChief === "all") {
      return allShifts;
    }

    if (selectedCrewChief === "unassigned") {
      return allShifts.filter((shift: TimelineShift) => {
        return !shift.crewChiefs || shift.crewChiefs.length === 0;
      });
    }

    return allShifts.filter((shift: TimelineShift) => {
      return shift.crewChiefs && shift.crewChiefs.some((chief: TimelineCrewChief) => chief.id === selectedCrewChief);
    });
  }, [allShifts, selectedCrewChief]);

  // Handle refresh function
  const handleRefresh = () => {
    refetch();
    setRefreshKey(prev => prev + 1); // Increment refresh key to force component update
  };

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-muted-foreground">Please log in to view the job timeline.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p>Loading job timeline...</p>
        </div>
      </div>
    );
  }

  if (isError || !job) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-red-600">
            {!job ? 'Job not found' : 'Error loading job timeline'}
          </p>
          <Button
            onClick={() => handleRefresh()}
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold">Job Timeline Manager</h1>
              <p className="text-muted-foreground">{job.name}</p>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push(`/jobs/${jobId}`)}
              className="self-start sm:self-auto"
            >
              ← Back to Job
            </Button>
          </div>

          {/* Filter Controls */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <label htmlFor="crew-chief-filter" className="text-sm font-medium">
                Filter by Crew Chief:
              </label>
              <Select value={selectedCrewChief} onValueChange={setSelectedCrewChief}>
                <SelectTrigger className="w-48" id="crew-chief-filter">
                  <SelectValue placeholder="Select crew chief" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Shifts</SelectItem>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                  {crewChiefs.map((chief) => (
                    <SelectItem key={chief.id} value={chief.id}>
                      {chief.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Filter Status */}
            <div className="text-sm text-muted-foreground">
              Showing {shifts.length} of {allShifts.length} shifts
              {selectedCrewChief !== "all" && (
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-md">
                  {selectedCrewChief === "unassigned"
                    ? "Unassigned"
                    : crewChiefs.find(c => c.id === selectedCrewChief)?.name || "Unknown"
                  }
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Visual Timeline */}
        {shifts.length > 0 ? (
          <>
            {/* Debug info before rendering timeline */}
            {console.log(`🎯 [Page] Rendering timeline with ${shifts.length} shifts:`, {
              shiftsPreview: shifts.slice(0, 2).map((s: TimelineShift) => ({
                id: s.id,
                date: s.date,
                startTime: s.startTime,
                endTime: s.endTime,
                status: s.status
              }))
            })}
            <VisualJobTimeline
              job={job}
              shifts={shifts}
              onRefresh={handleRefresh}
              key={refreshKey} // Add key to force re-render on refresh
            />
          </>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
            <h2 className="text-lg font-semibold mb-2">
              {selectedCrewChief === "all" ? "No Shifts Found" : "No Matching Shifts"}
            </h2>
            <p className="text-muted-foreground mb-4">
              {selectedCrewChief === "all"
                ? "This job doesn't have any shifts scheduled yet."
                : selectedCrewChief === "unassigned"
                  ? "No shifts without assigned crew chiefs found."
                  : `No shifts assigned to ${crewChiefs.find(c => c.id === selectedCrewChief)?.name || "this crew chief"} found.`
              }
            </p>
            {selectedCrewChief === "all" ? (
              <Button
                variant="outline"
                onClick={() => router.push(`/jobs/${jobId}/shifts/new`)}
              >
                Add First Shift
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={() => setSelectedCrewChief("all")}
              >
                Show All Shifts
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
