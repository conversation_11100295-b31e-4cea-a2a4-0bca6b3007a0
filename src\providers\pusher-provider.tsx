"use client";

import { useEffect } from 'react';
import Pusher from 'pusher-js';
import { useQueryClient } from '@tanstack/react-query';

export const PusherProvider = ({ children }: { children: React.ReactNode }) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const pusher = new Pusher(process.env.NEXT_PUBLIC_PUSHER_KEY!, {
      cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
    });

    const channel = pusher.subscribe('up-for-grabs');

    channel.bind('new-shift', () => {
      // Invalidate queries related to shifts to refetch data
      queryClient.invalidateQueries({ queryKey: ['shifts'] });
      queryClient.invalidateQueries({ queryKey: ['shifts-infinite'] });
    });

    return () => {
      pusher.unsubscribe('up-for-grabs');
      pusher.disconnect();
    };
  }, [queryClient]);

  return <>{children}</>;
};
