import { NextResponse } from 'next/server';

export type ApiError = {
  message: string;
  statusCode?: number;
  details?: any;
};

export function handleDatabaseError(error: any): ApiError {
  // Simplified handler; expand with Prisma error codes as needed
  if (typeof error?.message === 'string' && error.message.includes('Unique constraint')) {
    return { message: 'Duplicate value', statusCode: 400 };
  }
  return { message: 'Internal server error', statusCode: 500, details: error?.message };
}

export function createErrorResponse(error: ApiError) {
  const status = error.statusCode || 500;
  return NextResponse.json({ error: error.message, details: error.details }, { status });
}
