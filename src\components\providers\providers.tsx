'use client';

import React from 'react';
import NextAuthSessionProvider from '@/components/providers/session-provider';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { MantineProvider } from '@/components/providers/mantine-provider';
import { EnhancedQueryProvider } from '@/providers/enhanced-query-provider';
import { LoadingProvider } from '@/providers/loading-provider';
import { PusherProvider } from '@/providers/pusher-provider';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextAuthSessionProvider>
      <LoadingProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <MantineProvider>
            <EnhancedQueryProvider>
              <PusherProvider>
                {children}
              </PusherProvider>
            </EnhancedQueryProvider>
          </MantineProvider>
        </ThemeProvider>
      </LoadingProvider>
    </NextAuthSessionProvider>
  );
}
