/**
 * Build-time check utility for API routes
 * 
 * This utility helps prevent database operations during the build process,
 * which can cause deployment failures when DATABASE_URL is not available.
 */

import { NextResponse } from 'next/server';

/**
 * Checks if we're in build time and should skip database operations
 */
export function isBuildTime(): boolean {
  // Force build-time mode if any of these indicators are present
  const buildTimeIndicators = [
    process.env.BUILD_TIME === 'true',
    process.env.NEXT_PHASE === 'phase-production-build',
    process.env.CLOUDBUILD === 'true',
    // Always assume build time during Docker builds in /app directory
    process.env.PWD === '/app' && process.env.NODE_ENV === 'production',
    // No PORT means we're not in a running container
    process.env.NODE_ENV === 'production' && !process.env.PORT,
    // Check for invalid DATABASE_URLs
    process.env.DATABASE_URL?.includes('user:pass@host:port'),
    // If DATABASE_URL is empty or undefined in production, we're building
    process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL
  ];
  
  const isBuild = buildTimeIndicators.some(indicator => indicator === true);
  
  // Force log this information during any production build
  if (process.env.NODE_ENV === 'production') {
    console.log('[BUILD-TIME-CHECK]', {
      BUILD_TIME: process.env.BUILD_TIME,
      NEXT_PHASE: process.env.NEXT_PHASE,
      CLOUDBUILD: process.env.CLOUDBUILD,
      PWD: process.env.PWD,
      PORT: process.env.PORT,
      NODE_ENV: process.env.NODE_ENV,
      hasDatabaseUrl: !!process.env.DATABASE_URL,
      databaseUrl: process.env.DATABASE_URL?.substring(0, 20) + '...',
      isBuildTime: isBuild
    });
  }
  
  return isBuild;
}

/**
 * Returns a standard build-time response for API routes
 */
export function buildTimeResponse(routeName: string = 'API route'): NextResponse {
  return NextResponse.json({
    success: false,
    error: 'Database not available during build',
    message: `${routeName} is skipped during build time`,
    timestamp: new Date().toISOString(),
    buildTime: true
  });
}

/**
 * Wrapper function for API routes that need database access
 * Returns build-time response if in build mode, otherwise executes the handler
 */
export async function withDatabaseCheck<T>(
  routeName: string,
  handler: () => Promise<T>
): Promise<T | NextResponse> {
  if (isBuildTime()) {
    return buildTimeResponse(routeName);
  }
  
  return handler();
}

/**
 * Higher-order function for API route handlers
 * Automatically handles build-time checks
 */
export function withBuildTimeCheck(routeName: string) {
  return function<T extends any[], R>(
    handler: (...args: T) => Promise<R>
  ) {
    return async (...args: T): Promise<R | NextResponse> => {
      if (isBuildTime()) {
        return buildTimeResponse(routeName) as R;
      }
      
      return handler(...args);
    };
  };
}

/**
 * Environment check for debugging
 */
export function getEnvironmentInfo() {
  return {
    NODE_ENV: process.env.NODE_ENV,
    hasDatabaseUrl: !!process.env.DATABASE_URL,
    isBuildTime: isBuildTime(),
    timestamp: new Date().toISOString()
  };
}