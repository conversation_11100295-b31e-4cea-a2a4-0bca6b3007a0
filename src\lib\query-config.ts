// Enhanced query configuration
export const QUERY_CONFIG = {
  // Stale times based on data volatility
  STALE_TIMES: {
    STATIC: 10 * 60 * 1000,      // 10 minutes - companies, users (static data)
    SEMI_STATIC: 0,   // 0 milliseconds to always re-fetch in background
    DYNAMIC: 0,       // 0 milliseconds to always re-fetch in background
    REAL_TIME: 0,         // 0 milliseconds to always re-fetch in background
  },
  // Cache times (garbage collection)
  CACHE_TIMES: {
    LONG: 48 * 60 * 60 * 1000,         // 24 hours
    MEDIUM: 48 * 60 * 60 * 1000,       // 24 hours
    SHORT: 6 * 60 * 60 * 1000,        // 24 hours
  },
  // Retry configuration
  RETRY: {
    ATTEMPTS: 3,
    DELAY: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 10000),
  },
  MEMORY_LIMITS: {
    MAX_CACHE_SIZE: 200,
    CLEANUP_THRESHOLD: 150,
    STALE_CLEANUP_AGE: 5 * 60 * 1000, // 5 minutes
  },
};

// Factory for creating a memory manager
export const createMemoryManager = () => ({
  shouldCleanup: () => false, // Implement logic based on memory usage
  updateSize: (size: number) => {}, // Implement size tracking
});

// Factory for creating a performance monitor
export const createPerformanceMonitor = () => ({
  recordCacheHit: () => {},
  recordCacheMiss: () => {},
  recordMemoryCleanup: () => {},
  getMetrics: () => ({
    cacheHitRate: 'N/A',
    backgroundRefreshes: 0,
  }),
});

// Factory for creating a background refresh scheduler
export const createBackgroundRefreshScheduler = () => ({
  schedule: (callback: () => void, interval: number) => setInterval(callback, interval),
  cancel: (id: NodeJS.Timeout) => clearInterval(id),
});


// Smart cache key generation with dependency tracking
export const createSmartCacheKey = (
  baseKey: string,
  params?: Record<string, any>,
  dependencies?: string[]
) => {
  const key: Array<string | Record<string, any>> = [baseKey];
  
  if (params) {
    // Sort params for consistent cache keys
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, k) => ({ ...acc, [k]: params[k] }), {});
    key.push(sortedParams);
  }
  
  if (dependencies) {
    key.push(...dependencies);
  }
  
  return key;
};
