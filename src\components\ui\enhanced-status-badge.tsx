import React from 'react';
import { StatusBadge, type StatusBadgeProps, getFulfillmentStatus, getPriorityBadge, getPerformanceStatus, getRoleStatus, getCertificationStatus, getUrgencyStatus, getNetworkStatus } from './status-badge';

export function EnhancedStatusBadge(props: StatusBadgeProps) {
  return <StatusBadge {...props} />;
}

export { getFulfillmentStatus, getPriorityBadge, getPerformanceStatus, getRoleStatus, getCertificationStatus, getUrgencyStatus, getNetworkStatus };
