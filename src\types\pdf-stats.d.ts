export interface PDFStats {
  overview: {
    totalGenerations: number;
    averageGenerationTime: number;
    cacheHitRate: number;
    errorRate: number;
    totalCachedPDFs: number;
    cacheSize: number;
    availableTemplates: number;
  };
  performance: {
    totalGenerations: number;
    averageGenerationTime: number;
    averagePdfSize: number;
    cacheHitRate: number;
    errorRate: number;
    peakMemoryUsage: number;
    slowestGeneration: { generationTime: number; timesheetId: string; } | null;
    fastestGeneration: { generationTime: number; timesheetId: string; } | null;
  };
  cache: {
    totalEntries: number;
    totalSize: number;
    hitRate: number;
    oldestEntry: string | null;
    newestEntry: string | null;
    mostAccessed: any; // Can be improved if shape is known
  };
  recommendations: string[];
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    lastUpdated: string;
  };
}
