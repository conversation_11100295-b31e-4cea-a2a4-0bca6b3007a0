import { prisma } from '@/lib/prisma';
import { PDFTemplate, PDFElement } from './unified-pdf-generator';

export interface PDFConfiguration {
  id: string;
  name: string;
  description?: string;
  template: PDFTemplate;
  isDefault: boolean;
  isActive: boolean;
  companyId?: string; // For company-specific templates
  createdAt: Date;
  updatedAt: Date;
  version: string;
}

export interface PDFConfigurationInput {
  name: string;
  description?: string;
  template: Omit<PDFTemplate, 'id'>;
  isDefault?: boolean;
  companyId?: string;
}

/**
 * PDF Configuration Manager for template management
 */
export class PDFConfigManager {
  private static instance: PDFConfigManager;
  private templateCache = new Map<string, PDFTemplate>();

  static getInstance(): PDFConfigManager {
    if (!PDFConfigManager.instance) {
      PDFConfigManager.instance = new PDFConfigManager();
    }
    return PDFConfigManager.instance;
  }

  /**
   * Get template by ID with caching
   */
  async getTemplate(templateId: string): Promise<PDFTemplate | null> {
    // Check cache first
    if (this.templateCache.has(templateId)) {
      return this.templateCache.get(templateId)!;
    }

    try {
      // Try to load from database (when implemented)
      // For now, return built-in templates
      const template = await this.getBuiltInTemplate(templateId);
      
      if (template) {
        this.templateCache.set(templateId, template);
      }
      
      return template;
    } catch (error) {
      console.error('Error loading template:', error);
      return null;
    }
  }

  /**
   * Get default template for company or system
   */
  async getDefaultTemplate(companyId?: string): Promise<PDFTemplate> {
    // Company-specific default template
    if (companyId) {
      const companyTemplate = await this.getTemplate(`company-${companyId}-default`);
      if (companyTemplate) {
        return companyTemplate;
      }
    }

    // System default template
    return await this.getBuiltInTemplate('system-default') || this.createFallbackTemplate();
  }

  /**
   * Create new PDF configuration
   */
  async createConfiguration(config: PDFConfigurationInput): Promise<PDFConfiguration> {
    const id = this.generateConfigId();
    const template: PDFTemplate = {
      id,
      ...config.template,
      version: '1.0'
    };

    const configuration: PDFConfiguration = {
      id,
      name: config.name,
      description: config.description,
      template,
      isDefault: config.isDefault || false,
      isActive: true,
      companyId: config.companyId,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0'
    };

    // Cache the template
    this.templateCache.set(id, template);

    // In production, save to database
    // await this.saveToDatabase(configuration);

    return configuration;
  }

  /**
   * Update existing configuration
   */
  async updateConfiguration(
    id: string, 
    updates: Partial<PDFConfigurationInput>
  ): Promise<PDFConfiguration | null> {
    // In production, load from database and update
    // For now, return null as not implemented
    return null;
  }

  /**
   * List available templates
   */
  async listTemplates(companyId?: string): Promise<PDFTemplate[]> {
    const templates: PDFTemplate[] = [];

    // Add built-in templates
    const builtInIds = ['system-default', 'enhanced-timesheet', 'minimal-timesheet'];
    for (const id of builtInIds) {
      const template = await this.getBuiltInTemplate(id);
      if (template) {
        templates.push(template);
      }
    }

    // Add company-specific templates (when implemented)
    if (companyId) {
      // Load company templates from database
    }

    return templates;
  }

  /**
   * Validate template configuration
   */
  validateTemplate(template: PDFTemplate): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields
    if (!template.id) errors.push('Template ID is required');
    if (!template.name) errors.push('Template name is required');
    if (!template.elements || template.elements.length === 0) {
      errors.push('Template must have at least one element');
    }

    // Validate elements
    template.elements?.forEach((element, index) => {
      if (!element.id) errors.push(`Element ${index} missing ID`);
      if (!element.type) errors.push(`Element ${index} missing type`);
      if (!element.position) errors.push(`Element ${index} missing position`);
      
      // Validate position bounds
      if (element.position.x < 0 || element.position.y < 0) {
        errors.push(`Element ${index} has invalid position`);
      }
    });

    // Validate metadata
    if (!template.metadata) {
      errors.push('Template metadata is required');
    } else {
      if (!['letter', 'a4'].includes(template.metadata.pageSize)) {
        errors.push('Invalid page size');
      }
      if (!['portrait', 'landscape'].includes(template.metadata.orientation)) {
        errors.push('Invalid orientation');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Clear template cache
   */
  clearCache(): void {
    this.templateCache.clear();
  }

  /**
   * Get built-in template by ID
   */
  private async getBuiltInTemplate(templateId: string): Promise<PDFTemplate | null> {
    switch (templateId) {
      case 'system-default':
        return this.createDefaultTemplate();
      
      case 'enhanced-timesheet':
        return this.createEnhancedTemplate();
      
      case 'minimal-timesheet':
        return this.createMinimalTemplate();
      
      default:
        return null;
    }
  }

  /**
   * Create default template
   */
  private createDefaultTemplate(): PDFTemplate {
    return {
      id: 'system-default',
      name: 'Default Timesheet Template',
      version: '1.0',
      elements: [
        // Header
        {
          id: 'title',
          type: 'text',
          position: { x: 306, y: 60 },
          dimensions: { width: 200, height: 30 },
          style: { fontSize: 24, fontWeight: 'bold', alignment: 'center' },
          dataBinding: 'title'
        },
        
        // Job Information
        {
          id: 'job-label',
          type: 'text',
          position: { x: 50, y: 100 },
          dimensions: { width: 50, height: 20 },
          style: { fontSize: 10, fontWeight: 'bold' },
          dataBinding: 'jobLabel'
        },
        {
          id: 'job-name',
          type: 'text',
          position: { x: 112, y: 100 },
          dimensions: { width: 200, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'jobName'
        },
        
        // Company Information
        {
          id: 'company-label',
          type: 'text',
          position: { x: 50, y: 120 },
          dimensions: { width: 70, height: 20 },
          style: { fontSize: 10, fontWeight: 'bold' },
          dataBinding: 'companyLabel'
        },
        {
          id: 'company-name',
          type: 'text',
          position: { x: 135, y: 120 },
          dimensions: { width: 200, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'companyName'
        },
        
        // Location
        {
          id: 'location-label',
          type: 'text',
          position: { x: 50, y: 140 },
          dimensions: { width: 60, height: 20 },
          style: { fontSize: 10, fontWeight: 'bold' },
          dataBinding: 'locationLabel'
        },
        {
          id: 'location-value',
          type: 'text',
          position: { x: 110, y: 140 },
          dimensions: { width: 200, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'location'
        },
        
        // Date Information
        {
          id: 'date-label',
          type: 'text',
          position: { x: 450, y: 100 },
          dimensions: { width: 40, height: 20 },
          style: { fontSize: 10, fontWeight: 'bold' },
          dataBinding: 'dateLabel'
        },
        {
          id: 'date-value',
          type: 'text',
          position: { x: 505, y: 100 },
          dimensions: { width: 100, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'shiftDate'
        },
        
        // Time Information
        {
          id: 'start-time-label',
          type: 'text',
          position: { x: 450, y: 120 },
          dimensions: { width: 60, height: 20 },
          style: { fontSize: 10, fontWeight: 'bold' },
          dataBinding: 'startTimeLabel'
        },
        {
          id: 'start-time-value',
          type: 'text',
          position: { x: 510, y: 120 },
          dimensions: { width: 80, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'startTime'
        },
        
        {
          id: 'end-time-label',
          type: 'text',
          position: { x: 450, y: 140 },
          dimensions: { width: 60, height: 20 },
          style: { fontSize: 10, fontWeight: 'bold' },
          dataBinding: 'endTimeLabel'
        },
        {
          id: 'end-time-value',
          type: 'text',
          position: { x: 510, y: 140 },
          dimensions: { width: 80, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'endTime'
        },
        
        // Signature areas
        {
          id: 'signature-line',
          type: 'line',
          position: { x: 50, y: 500 },
          dimensions: { width: 200, height: 1 },
          style: { color: { r: 0, g: 0, b: 0 } }
        },
        {
          id: 'signature-label',
          type: 'text',
          position: { x: 50, y: 480 },
          dimensions: { width: 120, height: 20 },
          style: { fontSize: 10 },
          dataBinding: 'signatureLabel'
        }
      ],
      metadata: {
        pageSize: 'letter',
        orientation: 'portrait',
        margins: { top: 50, right: 50, bottom: 50, left: 50 }
      }
    };
  }

  /**
   * Create enhanced template with more features
   */
  private createEnhancedTemplate(): PDFTemplate {
    const defaultTemplate = this.createDefaultTemplate();
    
    return {
      ...defaultTemplate,
      id: 'enhanced-timesheet',
      name: 'Enhanced Timesheet Template',
      elements: [
        ...defaultTemplate.elements,
        
        // Additional elements for enhanced template
        {
          id: 'logo-placeholder',
          type: 'rectangle',
          position: { x: 50, y: 50 },
          dimensions: { width: 100, height: 50 },
          style: { color: { r: 0.9, g: 0.9, b: 0.9 } }
        },
        
        {
          id: 'qr-code-placeholder',
          type: 'rectangle',
          position: { x: 500, y: 50 },
          dimensions: { width: 50, height: 50 },
          style: { color: { r: 0.9, g: 0.9, b: 0.9 } }
        },
        
        {
          id: 'notes-section',
          type: 'rectangle',
          position: { x: 50, y: 600 },
          dimensions: { width: 500, height: 100 },
          style: { color: { r: 0.95, g: 0.95, b: 0.95 } }
        }
      ]
    };
  }

  /**
   * Create minimal template
   */
  private createMinimalTemplate(): PDFTemplate {
    return {
      id: 'minimal-timesheet',
      name: 'Minimal Timesheet Template',
      version: '1.0',
      elements: [
        {
          id: 'title',
          type: 'text',
          position: { x: 306, y: 60 },
          dimensions: { width: 200, height: 30 },
          style: { fontSize: 20, fontWeight: 'bold', alignment: 'center' },
          dataBinding: 'title'
        },
        {
          id: 'job-info',
          type: 'text',
          position: { x: 50, y: 100 },
          dimensions: { width: 300, height: 20 },
          style: { fontSize: 12 },
          dataBinding: 'jobInfo'
        },
        {
          id: 'date-info',
          type: 'text',
          position: { x: 400, y: 100 },
          dimensions: { width: 150, height: 20 },
          style: { fontSize: 12 },
          dataBinding: 'dateInfo'
        }
      ],
      metadata: {
        pageSize: 'letter',
        orientation: 'portrait',
        margins: { top: 50, right: 50, bottom: 50, left: 50 }
      }
    };
  }

  /**
   * Create fallback template when others fail
   */
  private createFallbackTemplate(): PDFTemplate {
    return {
      id: 'fallback',
      name: 'Fallback Template',
      version: '1.0',
      elements: [
        {
          id: 'error-message',
          type: 'text',
          position: { x: 50, y: 100 },
          dimensions: { width: 500, height: 30 },
          style: { fontSize: 16, color: { r: 1, g: 0, b: 0 } },
          dataBinding: 'errorMessage'
        }
      ],
      metadata: {
        pageSize: 'letter',
        orientation: 'portrait',
        margins: { top: 50, right: 50, bottom: 50, left: 50 }
      }
    };
  }

  /**
   * Generate unique configuration ID
   */
  private generateConfigId(): string {
    return `pdf-config-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const pdfConfigManager = PDFConfigManager.getInstance();