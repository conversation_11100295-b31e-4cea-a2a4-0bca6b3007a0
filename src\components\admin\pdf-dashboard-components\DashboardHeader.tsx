'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { formatTimeForDisplay } from '@/lib/utils';

interface DashboardHeaderProps {
  lastRefresh: Date | null;
  onRefresh: () => void;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({ lastRefresh, onRefresh }) => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <h1 className="text-3xl font-bold">PDF Management Dashboard</h1>
        <p className="text-gray-600">Monitor and manage PDF generation system</p>
      </div>
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-500">
          {lastRefresh ? `Last updated: ${formatTimeForDisplay(lastRefresh)}` : 'Updating...'}
        </span>
        <Button onClick={onRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
    </div>
  );
};
